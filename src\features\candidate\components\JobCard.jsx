import React from 'react';
import { Card, Tag, Button, Typography, Space, Tooltip } from 'antd';
import { MapPin, Clock, DollarSign, Building, Eye, Heart } from 'lucide-react';
import { getRelativeTime } from '@/utils/helpers';

const { Title, Text, Paragraph } = Typography;

/**
 * JobCard component for displaying job information
 */
const JobCard = ({
  job,
  onView = null,
  onApply = null,
  onSave = null,
  showActions = true,
  isApplied = false,
  isSaved = false,
  className = '',
}) => {
  const formatSalary = (salaryRange) => {
    if (!salaryRange) return 'Salary not specified';

    if (typeof salaryRange === 'object') {
      const { min, max, currency = '₹' } = salaryRange;
      if (min && max) {
        return `${currency}${min.toLocaleString()} - ${currency}${max.toLocaleString()}`;
      }
      if (min) return `${currency}${min.toLocaleString()}+`;
      if (max) return `Up to ${currency}${max.toLocaleString()}`;
    }

    return salaryRange;
  };

  const getExperienceColor = (level) => {
    const colors = {
      entry: 'green',
      mid: 'blue',
      senior: 'orange',
      lead: 'red',
      executive: 'purple',
    };
    return colors[level?.toLowerCase()] || 'default';
  };

  return (
    <Card
      className={`h-full transition-all duration-200 hover:shadow-md ${className}`}
      actions={
        showActions
          ? [
              <Tooltip title="View Details">
                <Button
                  type="text"
                  icon={<Eye size={16} />}
                  onClick={() => onView?.(job)}
                >
                  View
                </Button>
              </Tooltip>,
              <Tooltip title={isSaved ? 'Saved' : 'Save Job'}>
                <Button
                  type="text"
                  icon={<Heart size={16} />}
                  onClick={() => onSave?.(job)}
                  className={isSaved ? 'text-red-500' : ''}
                >
                  {isSaved ? 'Saved' : 'Save'}
                </Button>
              </Tooltip>,
              <Button
                type="primary"
                onClick={() => onApply?.(job)}
                disabled={isApplied}
                size="small"
              >
                {isApplied ? 'Applied' : 'Apply'}
              </Button>,
            ]
          : undefined
      }
    >
      <div className="space-y-3">
        {/* Job Title */}
        <div>
          <Title
            level={5}
            className="!mb-1 line-clamp-2"
          >
            {job.title}
          </Title>
          <div className="flex items-center text-gray-500 text-sm">
            <Building
              size={14}
              className="mr-1"
            />
            <Text type="secondary">Company ID: {job.company_id?.slice(0, 8)}...</Text>
          </div>
        </div>

        {/* Job Description */}
        {job.description && (
          <Paragraph
            className="!mb-2 text-sm text-gray-600 line-clamp-3"
            ellipsis={{ rows: 3 }}
          >
            {job.description}
          </Paragraph>
        )}

        {/* Job Details */}
        <div className="space-y-2">
          {/* Location */}
          <div className="flex items-center text-sm text-gray-600">
            <MapPin
              size={14}
              className="mr-2 text-gray-400"
            />
            <span>{job.location}</span>
          </div>

          {/* Salary */}
          <div className="flex items-center text-sm text-gray-600">
            <DollarSign
              size={14}
              className="mr-2 text-gray-400"
            />
            <span>{formatSalary(job.salary_range)}</span>
          </div>

          {/* Posted Date */}
          <div className="flex items-center text-sm text-gray-600">
            <Clock
              size={14}
              className="mr-2 text-gray-400"
            />
            <span>Posted {getRelativeTime(job.created_at)}</span>
          </div>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          <Tag color={getExperienceColor(job.experience_level)}>{job.experience_level}</Tag>
          {job.status && (
            <Tag color={job.status === 'active' ? 'green' : 'default'}>{job.status}</Tag>
          )}
        </div>

        {/* Skills */}
        {job.required_skills &&
          Array.isArray(job.required_skills) &&
          job.required_skills.length > 0 && (
            <div>
              <Text
                type="secondary"
                className="text-xs"
              >
                Required Skills:
              </Text>
              <div className="flex flex-wrap gap-1 mt-1">
                {job.required_skills.slice(0, 3).map((skill, index) => (
                  <Tag
                    key={index}
                    size="small"
                    className="text-xs"
                  >
                    {skill}
                  </Tag>
                ))}
                {job.required_skills.length > 3 && (
                  <Tag
                    size="small"
                    className="text-xs"
                  >
                    +{job.required_skills.length - 3} more
                  </Tag>
                )}
              </div>
            </div>
          )}
      </div>
    </Card>
  );
};

export default JobCard;
