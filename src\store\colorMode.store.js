import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';

// Get initial color mode from localStorage or system preference
const getInitialColorMode = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return 'light';

  // Check localStorage first
  if (typeof localStorage !== 'undefined') {
    const stored = localStorage.getItem('color-mode');
    if (stored === 'light' || stored === 'dark') return stored;
  }

  // Fall back to system preference
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

export const useColorModeStore = create(
  devtools(
    persist(
      (set) => ({
        colorMode: getInitialColorMode(),

        // Simple toggle - just update state
        toggleColorMode: () =>
          set((state) => ({
            colorMode: state.colorMode === 'light' ? 'dark' : 'light',
          })),

        // Set a specific color mode
        setColorMode: (mode) => {
          if (mode === 'light' || mode === 'dark') {
            set({ colorMode: mode });
          }
        },
      }),
      {
        name: 'color-mode-storage',
        getStorage: () => localStorage,
      }
    )
  )
);
