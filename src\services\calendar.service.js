/**
 * Calendar Service - Google Calendar Integration
 * 
 * This service handles all calendar operations including:
 * - Google Calendar sync
 * - Event creation, updating, deletion
 * - Calendar management
 * - Supabase integration for local storage
 */

import { supabase } from '@/utils/supabaseClient';
import { getGoogleAPIClient, isSignedInToGoogle, refreshGoogleToken } from '@/utils/googleApi';
import showToast from '@/utils/toast';

/**
 * Get all calendars from Google Calendar
 * @returns {Promise<Object>} - Calendar list result
 */
export const getGoogleCalendars = async () => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    const response = await client.calendar.calendarList.list();
    
    return {
      success: true,
      data: response.result.items || []
    };
  } catch (error) {
    console.error('Error fetching Google calendars:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get events from Google Calendar
 * @param {string} calendarId - Calendar ID (default: 'primary')
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Events result
 */
export const getGoogleCalendarEvents = async (calendarId = 'primary', options = {}) => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    const defaultOptions = {
      timeMin: new Date().toISOString(),
      maxResults: 100,
      singleEvents: true,
      orderBy: 'startTime'
    };

    const queryOptions = { ...defaultOptions, ...options };

    const response = await client.calendar.events.list({
      calendarId,
      ...queryOptions
    });

    return {
      success: true,
      data: response.result.items || []
    };
  } catch (error) {
    console.error('Error fetching Google Calendar events:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Create event in Google Calendar
 * @param {Object} eventData - Event data
 * @param {string} calendarId - Calendar ID (default: 'primary')
 * @returns {Promise<Object>} - Creation result
 */
export const createGoogleCalendarEvent = async (eventData, calendarId = 'primary') => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    // Format event data for Google Calendar API
    const googleEvent = formatEventForGoogle(eventData);

    const response = await client.calendar.events.insert({
      calendarId,
      resource: googleEvent
    });

    return {
      success: true,
      data: response.result
    };
  } catch (error) {
    console.error('Error creating Google Calendar event:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update event in Google Calendar
 * @param {string} eventId - Event ID
 * @param {Object} eventData - Updated event data
 * @param {string} calendarId - Calendar ID (default: 'primary')
 * @returns {Promise<Object>} - Update result
 */
export const updateGoogleCalendarEvent = async (eventId, eventData, calendarId = 'primary') => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    const googleEvent = formatEventForGoogle(eventData);

    const response = await client.calendar.events.update({
      calendarId,
      eventId,
      resource: googleEvent
    });

    return {
      success: true,
      data: response.result
    };
  } catch (error) {
    console.error('Error updating Google Calendar event:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete event from Google Calendar
 * @param {string} eventId - Event ID
 * @param {string} calendarId - Calendar ID (default: 'primary')
 * @returns {Promise<Object>} - Deletion result
 */
export const deleteGoogleCalendarEvent = async (eventId, calendarId = 'primary') => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    await client.calendar.events.delete({
      calendarId,
      eventId
    });

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting Google Calendar event:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Format event data for Google Calendar API
 * @param {Object} eventData - Local event data
 * @returns {Object} - Google Calendar formatted event
 */
const formatEventForGoogle = (eventData) => {
  const {
    title,
    description,
    date,
    time,
    duration,
    location,
    participants = [],
    isOnline,
    meetingLink,
    type
  } = eventData;

  // Create start and end datetime
  const startDateTime = new Date(`${date}T${time}`);
  const endDateTime = new Date(startDateTime.getTime() + (duration * 60000));

  const googleEvent = {
    summary: title,
    description: description || '',
    start: {
      dateTime: startDateTime.toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    end: {
      dateTime: endDateTime.toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    attendees: participants.map(email => ({ email })),
    reminders: {
      useDefault: false,
      overrides: [
        { method: 'email', minutes: 24 * 60 }, // 1 day before
        { method: 'popup', minutes: 30 } // 30 minutes before
      ]
    }
  };

  // Add location
  if (isOnline && meetingLink) {
    googleEvent.location = 'Online Meeting';
    googleEvent.description += `\n\nJoin Meeting: ${meetingLink}`;
  } else if (location) {
    googleEvent.location = location;
  }

  // Add event type as extended property
  if (type) {
    googleEvent.extendedProperties = {
      private: {
        eventType: type,
        platform: 'flyt'
      }
    };
  }

  return googleEvent;
};

/**
 * Format Google Calendar event for local use
 * @param {Object} googleEvent - Google Calendar event
 * @returns {Object} - Local event format
 */
export const formatGoogleEventForLocal = (googleEvent) => {
  const startDateTime = new Date(googleEvent.start.dateTime || googleEvent.start.date);
  const endDateTime = new Date(googleEvent.end.dateTime || googleEvent.end.date);
  
  const duration = Math.round((endDateTime - startDateTime) / 60000); // Duration in minutes

  return {
    id: googleEvent.id,
    googleEventId: googleEvent.id,
    title: googleEvent.summary || 'Untitled Event',
    description: googleEvent.description || '',
    date: startDateTime.toISOString().split('T')[0],
    time: startDateTime.toTimeString().slice(0, 5),
    duration,
    location: googleEvent.location || '',
    participants: googleEvent.attendees?.map(attendee => attendee.email) || [],
    isOnline: googleEvent.location === 'Online Meeting' || googleEvent.hangoutLink,
    meetingLink: googleEvent.hangoutLink || extractMeetingLink(googleEvent.description),
    type: googleEvent.extendedProperties?.private?.eventType || 'meeting',
    status: googleEvent.status,
    created: googleEvent.created,
    updated: googleEvent.updated
  };
};

/**
 * Extract meeting link from event description
 * @param {string} description - Event description
 * @returns {string} - Meeting link or empty string
 */
const extractMeetingLink = (description = '') => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const matches = description.match(urlRegex);
  
  if (matches) {
    // Look for common meeting platforms
    const meetingLink = matches.find(url => 
      url.includes('meet.google.com') ||
      url.includes('zoom.us') ||
      url.includes('teams.microsoft.com') ||
      url.includes('webex.com')
    );
    return meetingLink || '';
  }
  
  return '';
};

export default {
  getGoogleCalendars,
  getGoogleCalendarEvents,
  createGoogleCalendarEvent,
  updateGoogleCalendarEvent,
  deleteGoogleCalendarEvent,
  formatGoogleEventForLocal
};
