/**
 * Candidate Interviews Page
 *
 * Displays all interviews for a candidate with:
 * - Upcoming interviews
 * - Past interviews
 * - Interview requests
 * - Interview details and preparation
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Space,
  Button,
  Row,
  Col,
  Tabs,
  List,
  Avatar,
  Tag,
  Badge,
  Empty,
  Spin,
  Divider,
  Progress,
  Statistic,
  Rate,
  message,
  Modal,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  UserOutlined,
  VideoCameraOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  TeamOutlined,
  LinkOutlined,
  RightOutlined,
  InfoCircleOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import { formatDateTime } from '@/utils/helpers';
import {
  getInterviewRequests,
  getInterviewDetails,
  cancelInterviewRequest,
} from '@/services/interviewRequest.service';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

// Interview status colors
const statusColors = {
  pending: 'orange',
  accepted: 'blue',
  completed: 'green',
  cancelled: 'red',
  rejected: 'red',
};

// Interview type icons
const interviewTypeIcons = {
  video: <VideoCameraOutlined />,
  phone: <PhoneOutlined />,
  'in-person': <EnvironmentOutlined />,
};

const CandidateInterviews = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('upcoming');
  const [interviews, setInterviews] = useState({
    upcoming: [],
    past: [],
    requests: [],
  });
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    upcoming: 0,
    completed: 0,
    pending: 0,
  });
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState(null);
  const [detailsLoading, setDetailsLoading] = useState(false);

  // Fetch interviews data
  const fetchInterviews = useCallback(async () => {
    try {
      setLoading(true);
      const { success, data } = await getInterviewRequests(user.id, 'candidate');

      if (success && data) {
        // Sort and categorize interviews
        const now = new Date();
        const upcoming = [];
        const past = [];
        const requests = [];

        data.forEach((interview) => {
          const interviewDate = new Date(interview.interview_date || interview.preferred_date);

          if (interview.status === 'pending') {
            requests.push(interview);
          } else if (interviewDate > now && interview.status !== 'cancelled') {
            upcoming.push(interview);
          } else if (interviewDate <= now || interview.status === 'cancelled') {
            past.push(interview);
          }
        });

        // Sort by date
        upcoming.sort(
          (a, b) =>
            new Date(a.interview_date || a.preferred_date) -
            new Date(b.interview_date || b.preferred_date)
        );
        past.sort(
          (a, b) =>
            new Date(b.interview_date || b.preferred_date) -
            new Date(a.interview_date || a.preferred_date)
        );
        requests.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        setInterviews({
          upcoming,
          past,
          requests,
        });

        setStats({
          total: data.length,
          upcoming: upcoming.length,
          completed: data.filter((i) => i.status === 'completed').length,
          pending: requests.length,
        });
      }
    } catch (error) {
      console.error('Error fetching interviews:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Add useEffect to fetch interviews when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchInterviews();
    }
  }, [user, fetchInterviews]);

  // View interview details
  const viewInterviewDetails = async (interview) => {
    setSelectedInterview(interview);
    setDetailsVisible(true);

    try {
      setDetailsLoading(true);
      const { success, data } = await getInterviewDetails(interview.id);

      if (success && data) {
        setSelectedInterview((prev) => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Error fetching interview details:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  // Handle interview cancellation
  const handleCancelInterview = async (interviewId) => {
    try {
      setLoading(true);
      const { success, error } = await cancelInterviewRequest(interviewId);

      if (success) {
        message.success('Interview cancelled successfully');
        fetchInterviews();
      } else {
        message.error(error || 'Failed to cancel interview');
      }
    } catch (error) {
      console.error('Error cancelling interview:', error);
      message.error('Failed to cancel interview');
    } finally {
      setLoading(false);
    }
  };

  // Format interview date and time
  const formatInterviewDateTime = (interview) => {
    const date = new Date(interview.interview_date || interview.preferred_date);
    return formatDateTime(date);
  };

  // Render interview card
  const renderInterviewItem = (interview) => {
    const isUpcoming = new Date(interview.interview_date || interview.preferred_date) > new Date();

    return (
      <List.Item
        key={interview.id}
        actions={[
          <Button
            type="primary"
            onClick={() => viewInterviewDetails(interview)}
            icon={<RightOutlined />}
          >
            View Details
          </Button>,
          isUpcoming && interview.status === 'accepted' && (
            <Button
              danger
              onClick={() => {
                Modal.confirm({
                  title: 'Cancel Interview',
                  content: 'Are you sure you want to cancel this interview?',
                  onOk: () => handleCancelInterview(interview.id),
                });
              }}
            >
              Cancel
            </Button>
          ),
        ].filter(Boolean)}
      >
        <List.Item.Meta
          avatar={
            <Avatar
              size={50}
              src={interview.jobs?.companies?.logo_url}
              icon={<TeamOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
          }
          title={
            <div className="flex items-center">
              <span className="font-medium">{interview.jobs?.title || 'Interview'}</span>
              <Tag
                color={statusColors[interview.status] || 'default'}
                className="ml-2"
              >
                {interview.status.charAt(0).toUpperCase() + interview.status.slice(1)}
              </Tag>
              {isUpcoming && interview.status === 'accepted' && (
                <Badge
                  status="processing"
                  text="Upcoming"
                  className="ml-2"
                />
              )}
            </div>
          }
          description={
            <Space
              direction="vertical"
              size={0}
            >
              <div>
                <Text strong>{interview.jobs?.companies?.company_name}</Text>
              </div>
              <div className="flex items-center text-gray-500">
                <CalendarOutlined className="mr-1" />
                <Text type="secondary">{formatInterviewDateTime(interview)}</Text>
                <Divider type="vertical" />
                {interviewTypeIcons[interview.interview_type] || <VideoCameraOutlined />}
                <Text
                  type="secondary"
                  className="ml-1 capitalize"
                >
                  {interview.interview_type || 'Video'} Interview
                </Text>
                <Divider type="vertical" />
                <ClockCircleOutlined className="mr-1" />
                <Text type="secondary">{interview.duration_minutes || 60} mins</Text>
              </div>
            </Space>
          }
        />
      </List.Item>
    );
  };

  // Render interview details modal
  const renderDetailsModal = () => {
    if (!selectedInterview) return null;

    const interviewDate = new Date(
      selectedInterview.interview_date || selectedInterview.preferred_date
    );
    const isPast = interviewDate < new Date();
    const isUpcoming = !isPast && selectedInterview.status === 'accepted';

    return (
      <Modal
        title={
          <div className="flex items-center">
            <span>Interview Details</span>
            <Tag
              color={statusColors[selectedInterview.status] || 'default'}
              className="ml-2"
            >
              {selectedInterview.status.charAt(0).toUpperCase() + selectedInterview.status.slice(1)}
            </Tag>
          </div>
        }
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={null}
        width={700}
      >
        <Spin spinning={detailsLoading}>
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Card className="border-0 shadow-none">
                <Row
                  gutter={16}
                  align="middle"
                >
                  <Col
                    xs={24}
                    sm={4}
                  >
                    <Avatar
                      size={80}
                      src={selectedInterview.jobs?.companies?.logo_url}
                      icon={<TeamOutlined />}
                      style={{ backgroundColor: '#1890ff' }}
                    />
                  </Col>
                  <Col
                    xs={24}
                    sm={20}
                  >
                    <Title
                      level={4}
                      className="mb-0"
                    >
                      {selectedInterview.jobs?.title || 'Interview'}
                    </Title>
                    <Text strong>{selectedInterview.jobs?.companies?.company_name}</Text>
                    <div className="mt-2 flex items-center text-gray-500">
                      <CalendarOutlined className="mr-1" />
                      <Text type="secondary">{formatInterviewDateTime(selectedInterview)}</Text>
                      <Divider type="vertical" />
                      {interviewTypeIcons[selectedInterview.interview_type] || (
                        <VideoCameraOutlined />
                      )}
                      <Text
                        type="secondary"
                        className="ml-1 capitalize"
                      >
                        {selectedInterview.interview_type || 'Video'} Interview
                      </Text>
                      <Divider type="vertical" />
                      <ClockCircleOutlined className="mr-1" />
                      <Text type="secondary">{selectedInterview.duration_minutes || 60} mins</Text>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>

            {isUpcoming && (
              <Col span={24}>
                <Card title="Interview Preparation">
                  <div className="mb-4">
                    <Progress
                      percent={60}
                      status="active"
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                    />
                    <Text type="secondary">Your preparation is 60% complete</Text>
                  </div>

                  <div className="mb-2">
                    <Text strong>Preparation Checklist:</Text>
                  </div>
                  <ul className="pl-5">
                    <li className="mb-2">
                      <Text>Research the company's products, culture, and recent news</Text>
                    </li>
                    <li className="mb-2">
                      <Text>Review the job description and requirements</Text>
                    </li>
                    <li className="mb-2">
                      <Text>Prepare answers to common interview questions</Text>
                    </li>
                    <li className="mb-2">
                      <Text>Prepare questions to ask the interviewer</Text>
                    </li>
                    <li className="mb-2">
                      <Text>Test your camera, microphone, and internet connection</Text>
                    </li>
                  </ul>
                </Card>
              </Col>
            )}

            <Col span={24}>
              <Card
                title={
                  <div className="flex items-center">
                    <InfoCircleOutlined className="mr-2" />
                    <span>Interview Information</span>
                  </div>
                }
              >
                <Row gutter={[16, 16]}>
                  <Col
                    xs={24}
                    sm={12}
                  >
                    <div className="mb-3">
                      <Text type="secondary">Interviewer</Text>
                      <div className="flex items-center mt-1">
                        <Avatar
                          size="small"
                          icon={<UserOutlined />}
                          className="mr-2"
                        />
                        <Text strong>
                          {selectedInterview.interviewer_profiles?.full_name || 'To be assigned'}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col
                    xs={24}
                    sm={12}
                  >
                    <div className="mb-3">
                      <Text type="secondary">Location/Link</Text>
                      <div className="mt-1">
                        {selectedInterview.interview_link ? (
                          <a
                            href={selectedInterview.interview_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center"
                          >
                            <LinkOutlined className="mr-1" />
                            <Text strong>Join Interview</Text>
                          </a>
                        ) : (
                          <Text>{selectedInterview.interview_location || 'To be provided'}</Text>
                        )}
                      </div>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div>
                      <Text type="secondary">Additional Information</Text>
                      <Paragraph className="mt-1">
                        {selectedInterview.additional_info ||
                          'Please be prepared to discuss your experience and skills related to the position. The interview will include technical questions and a discussion about your previous work.'}
                      </Paragraph>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>

            {isPast && selectedInterview.feedback && (
              <Col span={24}>
                <Card
                  title={
                    <div className="flex items-center">
                      <FileTextOutlined className="mr-2" />
                      <span>Feedback</span>
                    </div>
                  }
                >
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <div className="mb-3">
                        <Space
                          direction="vertical"
                          size="middle"
                          style={{ width: '100%' }}
                        >
                          <div>
                            <Text type="secondary">Overall Rating</Text>
                            <div className="mt-1">
                              <Rate
                                disabled
                                value={selectedInterview.rating || 0}
                              />
                            </div>
                          </div>
                          <div>
                            <Text type="secondary">Feedback</Text>
                            <Paragraph className="mt-1">{selectedInterview.feedback}</Paragraph>
                          </div>
                        </Space>
                      </div>
                    </Col>
                  </Row>
                </Card>
              </Col>
            )}

            <Col span={24}>
              <div className="flex justify-end">
                {isUpcoming && (
                  <Space>
                    <Button
                      danger
                      onClick={() => {
                        Modal.confirm({
                          title: 'Cancel Interview',
                          content: 'Are you sure you want to cancel this interview?',
                          onOk: () => {
                            handleCancelInterview(selectedInterview.id);
                            setDetailsVisible(false);
                          },
                        });
                      }}
                    >
                      Cancel Interview
                    </Button>
                    <Button type="primary">
                      {selectedInterview.interview_link ? 'Join Interview' : 'Confirm Attendance'}
                    </Button>
                  </Space>
                )}
                {!isUpcoming && (
                  <Button
                    type="primary"
                    onClick={() => setDetailsVisible(false)}
                  >
                    Close
                  </Button>
                )}
              </div>
            </Col>
          </Row>
        </Spin>
      </Modal>
    );
  };

  return (
    <div className="candidate-interviews p-6">
      {/* Header */}
      <div className="mb-6">
        <Row
          gutter={16}
          align="middle"
          justify="space-between"
        >
          <Col>
            <div className="flex items-center">
              <div>
                <Title
                  level={2}
                  className="m-0"
                >
                  My Interviews
                </Title>
                <Text type="secondary">Manage and track all your interview activities</Text>
              </div>
            </div>
          </Col>
          <Col>
            <Space>
              <Button icon={<BellOutlined />}>
                Notifications
                {stats.upcoming > 0 && (
                  <Badge
                    count={stats.upcoming}
                    offset={[5, -5]}
                  />
                )}
              </Button>
              <Link to="/candidate/calendar">
                <Button
                  type="primary"
                  icon={<CalendarOutlined />}
                >
                  View Calendar
                </Button>
              </Link>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Statistics Cards */}
      <Row
        gutter={16}
        className="mb-6"
      >
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <Statistic
              title="Total Interviews"
              value={stats.total}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <Statistic
              title="Upcoming"
              value={stats.upcoming}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <Statistic
              title="Completed"
              value={stats.completed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <Statistic
              title="Pending Requests"
              value={stats.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Tabs and Lists */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        >
          <TabPane
            tab={
              <span>
                <CalendarOutlined />
                Upcoming
                {interviews.upcoming.length > 0 && (
                  <Badge
                    count={interviews.upcoming.length}
                    offset={[5, -5]}
                  />
                )}
              </span>
            }
            key="upcoming"
          >
            <Spin spinning={loading}>
              {interviews.upcoming.length > 0 ? (
                <List
                  itemLayout="horizontal"
                  dataSource={interviews.upcoming}
                  renderItem={renderInterviewItem}
                  pagination={{
                    pageSize: 5,
                    hideOnSinglePage: true,
                  }}
                />
              ) : (
                <Empty
                  description="No upcoming interviews"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Spin>
          </TabPane>

          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                Past Interviews
              </span>
            }
            key="past"
          >
            <Spin spinning={loading}>
              {interviews.past.length > 0 ? (
                <List
                  itemLayout="horizontal"
                  dataSource={interviews.past}
                  renderItem={renderInterviewItem}
                  pagination={{
                    pageSize: 5,
                    hideOnSinglePage: true,
                  }}
                />
              ) : (
                <Empty
                  description="No past interviews"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Spin>
          </TabPane>

          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                Requests
                {interviews.requests.length > 0 && (
                  <Badge
                    count={interviews.requests.length}
                    offset={[5, -5]}
                  />
                )}
              </span>
            }
            key="requests"
          >
            <Spin spinning={loading}>
              {interviews.requests.length > 0 ? (
                <List
                  itemLayout="horizontal"
                  dataSource={interviews.requests}
                  renderItem={renderInterviewItem}
                  pagination={{
                    pageSize: 5,
                    hideOnSinglePage: true,
                  }}
                />
              ) : (
                <Empty
                  description="No interview requests"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Spin>
          </TabPane>
        </Tabs>
      </Card>

      {/* Interview Details Modal */}
      {renderDetailsModal()}
    </div>
  );
};

export default CandidateInterviews;
