/**
 * Candidate Profile Hook
 *
 * Provides a unified interface for managing candidate profile data and account settings
 * Connects to the database and handles data persistence
 */
import { useState, useCallback } from 'react';
import { message } from 'antd';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '../store/candidate.store';

const useCandidateProfile = () => {
  const { user, profile, updateProfile, setError } = useAuth();
  const { uploadProfilePhoto, uploadResume } = useCandidateStore();

  /**
   * Fetch the complete candidate profile with all related data
   * @param {boolean} _forceRefresh - Whether to force a refresh from the database
   */
  const fetchCandidateProfile = useCallback(
    async (_forceRefresh = false) => {
      if (!user?.id) return null;

      try {
        // Fetch the complete candidate profile view that includes all related data
        const { data, error } = await supabase
          .from('candidate_profiles_complete')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        // Update the profile in auth store
        if (data) {
          updateProfile(data);
        }

        return data;
      } catch (error) {
        console.error('Error fetching candidate profile:', error);
        setError(error.message);
        return null;
      }
    },
    [user, setError, updateProfile]
  );

  /**
   * Update candidate profile data
   * @param {Object} profileData - Profile data to update
   */
  const updateCandidateProfile = useCallback(
    async (profileData) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      try {
        // Update the profile in the database
        const { data, error } = await supabase
          .from('profiles')
          .update({
            ...profileData,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id)
          .select()
          .single();

        if (error) throw error;

        // Update the profile in auth store
        updateProfile(data);

        return data;
      } catch (error) {
        console.error('Error updating candidate profile:', error);
        throw error;
      } finally {
      }
    },
    [user, updateProfile]
  );

  /**
   * Update candidate account settings
   * @param {Object} settings - Account settings to update
   * @param {string} settingsType - Type of settings (notifications, privacy, preferences)
   */
  const updateAccountSettings = useCallback(
    async (settings, settingsType) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      try {
        // Create the update object with the correct field based on settings type
        const updateData = {};

        switch (settingsType) {
          case 'notifications':
            updateData.notification_settings = settings;
            break;
          case 'privacy':
            updateData.privacy_settings = settings;
            break;
          case 'preferences':
            updateData.preferences = settings;
            break;
          default:
            throw new Error('Invalid settings type');
        }

        // Update the settings in the database
        const { data, error } = await supabase
          .from('candidate_settings')
          .upsert({
            candidate_id: user.id,
            ...updateData,
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) throw error;

        // Update the profile with the new settings
        updateProfile({
          ...profile,
          [settingsType === 'notifications'
            ? 'notification_settings'
            : settingsType === 'privacy'
              ? 'privacy_settings'
              : 'preferences']: settings,
        });

        return data;
      } catch (error) {
        console.error(`Error updating ${settingsType} settings:`, error);
        throw error;
      } finally {
      }
    },
    [user, profile, updateProfile]
  );

  /**
   * Upload profile photo
   * @param {File} file - Photo file to upload
   */
  const handlePhotoUpload = useCallback(
    async (file) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      try {
        const result = await uploadProfilePhoto(user.id, file);

        if (result?.url) {
          // Update the profile with the new photo URL
          await updateCandidateProfile({
            profile_photo_url: result.url,
          });

          message.success('Profile photo updated successfully');
        }

        return result;
      } catch (error) {
        message.error('Failed to upload profile photo');
        throw error;
      }
    },
    [user, uploadProfilePhoto, updateCandidateProfile]
  );

  /**
   * Upload resume
   * @param {File} file - Resume file to upload
   */
  const handleResumeUpload = useCallback(
    async (file) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      try {
        const result = await uploadResume(user.id, file);

        if (result?.url) {
          // Update the profile with the new resume URL
          await updateCandidateProfile({
            resume_url: result.url,
            resume_name: file.name,
          });

          message.success('Resume uploaded successfully');
        }

        return result;
      } catch (error) {
        message.error('Failed to upload resume');
        throw error;
      }
    },
    [user, uploadResume, updateCandidateProfile]
  );

  /**
   * Update password
   * @param {Object} passwordData - Password data (oldPassword, newPassword)
   */
  const updatePassword = useCallback(async (passwordData) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword,
      });
      console.log('Password: ', passwordData, error);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error updating password:', error.message);
      throw error;
    }
  }, []);

  /**
   * Deactivate account
   * This is a sensitive operation that should require confirmation
   */
  const deactivateAccount = useCallback(async () => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      // Call a secure server-side function to handle account deactivation
      const { error } = await supabase.rpc('deactivate_candidate_account', {
        user_id: user.id,
      });

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error deactivating account:', error);
      throw error;
    }
  }, [user]);

  return {
    profile,
    fetchCandidateProfile,
    updateCandidateProfile,
    updateAccountSettings,
    handlePhotoUpload,
    handleResumeUpload,
    updatePassword,
    deactivateAccount,
  };
};

export default useCandidateProfile;
