import React from 'react';
import { Card, List, Tag, Typography, Button, Empty, Skeleton } from 'antd';
import { Eye, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { getRelativeTime } from '@/utils/helpers';

const { Title, Text } = Typography;

/**
 * RecentApplicationsSection component
 */
const RecentApplicationsSection = ({
  applications,
  loading,
  onViewApplication,
  onViewAllApplications,
}) => {
  const getStatusIcon = (status) => {
    const icons = {
      pending: (
        <Clock
          size={16}
          className="text-yellow-500"
        />
      ),
      under_review: (
        <AlertCircle
          size={16}
          className="text-blue-500"
        />
      ),
      accepted: (
        <CheckCircle
          size={16}
          className="text-green-500"
        />
      ),
      rejected: (
        <XCircle
          size={16}
          className="text-red-500"
        />
      ),
    };
    return (
      icons[status] || (
        <Clock
          size={16}
          className="text-gray-500"
        />
      )
    );
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      under_review: 'blue',
      accepted: 'green',
      rejected: 'red',
    };
    return colors[status] || 'default';
  };

  if (loading) {
    return (
      <Card
        title="Recent Applications"
        extra={<Button type="link">View All</Button>}
      >
        <Skeleton
          active
          paragraph={{ rows: 4 }}
        />
      </Card>
    );
  }

  return (
    <Card
      title="Recent Applications"
      extra={
        <Button
          type="link"
          onClick={onViewAllApplications}
        >
          View All
        </Button>
      }
    >
      {applications.length === 0 ? (
        <Empty
          description="No applications yet"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <List
          dataSource={applications.slice(0, 5)}
          renderItem={(application) => (
            <List.Item
              key={application.id}
              actions={[
                <Button
                  type="text"
                  icon={<Eye size={16} />}
                  onClick={() => onViewApplication?.(application)}
                >
                  View
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={getStatusIcon(application.status)}
                title={
                  <div className="flex items-center justify-between">
                    <Text
                      strong
                      className="line-clamp-1"
                    >
                      {application.jobs?.title || 'Job Title'}
                    </Text>
                    <Tag
                      color={getStatusColor(application.status)}
                      size="small"
                    >
                      {application.status.replace('_', ' ')}
                    </Tag>
                  </div>
                }
                description={
                  <div className="space-y-1">
                    <div className="text-sm text-gray-600">
                      {application.jobs?.location || 'Location not specified'}
                    </div>
                    <div className="text-xs text-gray-500">
                      Applied {getRelativeTime(application.created_at)}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default RecentApplicationsSection;
