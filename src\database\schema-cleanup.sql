-- Comprehensive Supabase Schema Cleanup
-- This script removes unused tables, functions, and objects after candidate removal
-- Run this script in your Supabase SQL editor AFTER running the candidate cleanup

BEGIN;

-- 1. Remove unused authentication-related functions
-- (Since we're using <PERSON> for auth, these Supabase auth functions may not be needed)
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS handle_user_signup() CASCADE;
DROP FUNCTION IF EXISTS create_user_profile(uuid, text) CASCADE;
DROP FUNCTION IF EXISTS get_user_role(uuid) CASCADE;

-- 2. Remove unused triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users CASCADE;
DROP TRIGGER IF EXISTS handle_updated_at ON profiles CASCADE;

-- 3. Clean up unused views
DROP VIEW IF EXISTS user_profiles_complete CASCADE;
DROP VIEW IF EXISTS active_users CASCADE;
DROP VIEW IF EXISTS user_statistics CASCADE;

-- 4. Remove unused tables that might have been created for candidate functionality
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS user_preferences CASCADE;
DROP TABLE IF EXISTS user_activity_logs CASCADE;
DROP TABLE IF EXISTS email_verifications CASCADE;
DROP TABLE IF EXISTS password_resets CASCADE;
DROP TABLE IF EXISTS user_tokens CASCADE;

-- 5. Clean up unused indexes
DROP INDEX IF EXISTS idx_profiles_email_unique;
DROP INDEX IF EXISTS idx_profiles_username;
DROP INDEX IF EXISTS idx_profiles_role_status;
DROP INDEX IF EXISTS idx_user_sessions_user_id;
DROP INDEX IF EXISTS idx_user_activity_user_id;

-- 6. Remove unused RLS policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;

-- 7. Clean up unused storage buckets (if any were created for candidates)
-- Note: This needs to be done through Supabase dashboard or storage API
-- DELETE FROM storage.objects WHERE bucket_id IN ('user-avatars', 'user-documents');
-- DELETE FROM storage.buckets WHERE id IN ('user-avatars', 'user-documents');

-- 8. Remove unused enum values and types
-- Be very careful with this - only remove if you're absolutely sure they're not used
-- DROP TYPE IF EXISTS user_status CASCADE;
-- DROP TYPE IF EXISTS verification_status CASCADE;

-- 9. Clean up any remaining candidate-related columns in existing tables
-- Update jobs table to remove candidate-specific fields if they exist
ALTER TABLE IF EXISTS jobs 
DROP COLUMN IF EXISTS candidate_requirements CASCADE,
DROP COLUMN IF EXISTS candidate_count CASCADE,
DROP COLUMN IF EXISTS applications_count CASCADE;

-- Update company_profiles to remove candidate-related fields
ALTER TABLE IF EXISTS company_profiles
DROP COLUMN IF EXISTS preferred_candidate_experience CASCADE,
DROP COLUMN IF EXISTS candidate_rating_threshold CASCADE;

-- Update interviewer_profiles to remove candidate-specific fields
ALTER TABLE IF EXISTS interviewer_profiles
DROP COLUMN IF EXISTS candidate_specialization CASCADE,
DROP COLUMN IF EXISTS candidate_interview_count CASCADE;

-- 10. Optimize remaining tables by updating statistics
ANALYZE profiles;
ANALYZE company_profiles;
ANALYZE interviewer_profiles;
ANALYZE jobs;
ANALYZE interviews;

-- 11. Clean up any orphaned records
-- Remove any profiles that don't have corresponding role-specific profiles
DELETE FROM profiles 
WHERE role = 'company' 
AND id NOT IN (SELECT id FROM company_profiles);

DELETE FROM profiles 
WHERE role = 'interviewer' 
AND id NOT IN (SELECT id FROM interviewer_profiles);

-- 12. Update sequences if needed
-- Reset any sequences that might have been affected
-- SELECT setval('profiles_id_seq', (SELECT MAX(id) FROM profiles));

-- 13. Recreate optimized indexes for remaining functionality
-- Create indexes for better performance on remaining tables
CREATE INDEX IF NOT EXISTS idx_profiles_role_active 
ON profiles(role) WHERE role IN ('company', 'interviewer');

CREATE INDEX IF NOT EXISTS idx_company_profiles_status 
ON company_profiles(status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_interviewer_profiles_status 
ON interviewer_profiles(status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_jobs_company_status 
ON jobs(company_id, status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_interviews_status_date 
ON interviews(status, interview_date) WHERE status IN ('scheduled', 'completed');

-- 14. Update RLS policies for remaining functionality
-- Ensure proper RLS policies are in place for company and interviewer profiles

-- Company profiles RLS
DROP POLICY IF EXISTS "Companies can view own profile" ON company_profiles;
CREATE POLICY "Companies can view own profile" ON company_profiles
FOR SELECT USING (auth.uid()::text = id::text);

DROP POLICY IF EXISTS "Companies can update own profile" ON company_profiles;
CREATE POLICY "Companies can update own profile" ON company_profiles
FOR UPDATE USING (auth.uid()::text = id::text);

-- Interviewer profiles RLS  
DROP POLICY IF EXISTS "Interviewers can view own profile" ON interviewer_profiles;
CREATE POLICY "Interviewers can view own profile" ON interviewer_profiles
FOR SELECT USING (auth.uid()::text = id::text);

DROP POLICY IF EXISTS "Interviewers can update own profile" ON interviewer_profiles;
CREATE POLICY "Interviewers can update own profile" ON interviewer_profiles
FOR UPDATE USING (auth.uid()::text = id::text);

-- 15. Create helpful views for the remaining functionality
CREATE OR REPLACE VIEW active_companies AS
SELECT 
  cp.*,
  p.email,
  p.created_at as profile_created_at
FROM company_profiles cp
JOIN profiles p ON cp.id = p.id
WHERE cp.status = 'active' AND p.role = 'company';

CREATE OR REPLACE VIEW active_interviewers AS
SELECT 
  ip.*,
  p.email,
  p.created_at as profile_created_at
FROM interviewer_profiles ip
JOIN profiles p ON ip.id = p.id
WHERE ip.status = 'active' AND p.role = 'interviewer';

-- 16. Create functions for common operations
CREATE OR REPLACE FUNCTION get_user_profile_complete(user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_data jsonb;
  role_data jsonb;
  user_role text;
BEGIN
  -- Get basic profile
  SELECT to_jsonb(p.*), p.role INTO profile_data, user_role
  FROM profiles p
  WHERE p.id = user_id;
  
  IF profile_data IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- Get role-specific data
  IF user_role = 'company' THEN
    SELECT to_jsonb(cp.*) INTO role_data
    FROM company_profiles cp
    WHERE cp.id = user_id;
  ELSIF user_role = 'interviewer' THEN
    SELECT to_jsonb(ip.*) INTO role_data
    FROM interviewer_profiles ip
    WHERE ip.id = user_id;
  END IF;
  
  -- Merge the data
  IF role_data IS NOT NULL THEN
    profile_data := profile_data || role_data;
  END IF;
  
  RETURN profile_data;
END;
$$;

-- 17. Final verification queries
-- Run these to verify the cleanup was successful
-- SELECT 'Total profiles' as metric, COUNT(*) as count FROM profiles
-- UNION ALL
-- SELECT 'Company profiles', COUNT(*) FROM company_profiles
-- UNION ALL
-- SELECT 'Interviewer profiles', COUNT(*) FROM interviewer_profiles
-- UNION ALL
-- SELECT 'Active jobs', COUNT(*) FROM jobs WHERE status = 'active'
-- UNION ALL
-- SELECT 'Total interviews', COUNT(*) FROM interviews;

COMMIT;

-- Post-cleanup recommendations:
-- 1. Run VACUUM ANALYZE on all tables to reclaim space and update statistics
-- 2. Monitor query performance and add indexes as needed
-- 3. Update your application code to remove any references to deleted tables/functions
-- 4. Test all functionality thoroughly
-- 5. Consider setting up monitoring for the remaining tables
