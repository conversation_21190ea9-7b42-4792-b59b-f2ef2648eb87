import React from 'react';
import { <PERSON>, Row, Col, Button, Empty, Skeleton } from 'antd';
import JobCard from '../jobs/JobCard';

/**
 * RecentJobsSection component for displaying latest job postings
 */
const RecentJobsSection = ({
  jobs,
  loading,
  onViewJob,
  onApplyJob,
  onSaveJob,
  onViewAllJobs,
  appliedJobs = [],
  savedJobs = [],
}) => {
  if (loading) {
    return (
      <Card
        title="Latest Job Opportunities"
        extra={<Button type="link">View All Jobs</Button>}
      >
        <Row gutter={[16, 16]}>
          {[1, 2, 3].map((i) => (
            <Col
              xs={24}
              sm={12}
              lg={8}
              key={i}
            >
              <Card>
                <Skeleton
                  active
                  paragraph={{ rows: 4 }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  }

  const isJobApplied = (jobId) => {
    return appliedJobs.some((app) => app.job_id === jobId);
  };

  const isJobSaved = (jobId) => {
    return savedJobs.some((saved) => saved.job_id === jobId);
  };

  return (
    <Card
      title="Latest Job Opportunities"
      extra={
        <Button
          type="link"
          onClick={onViewAllJobs}
        >
          View All Jobs
        </Button>
      }
      style={{ marginBottom: '1.5rem' }}
    >
      {jobs.length === 0 ? (
        <Empty
          description="No jobs available"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <Row gutter={[16, 16]}>
          {jobs.slice(0, 6).map((job) => (
            <Col
              xs={24}
              sm={12}
              lg={8}
              key={job.id}
            >
              <JobCard
                job={job}
                onView={onViewJob}
                onApply={onApplyJob}
                onSave={onSaveJob}
                isApplied={isJobApplied(job.id)}
                isSaved={isJobSaved(job.id)}
              />
            </Col>
          ))}
        </Row>
      )}
    </Card>
  );
};

export default RecentJobsSection;
