@import 'tailwindcss';

/* ========== THEME VARIABLES ========== */
:root {
  /* Base colors */
  --background: #ffffff;
  --foreground: #1e1e1e;
  --primary: #0056d2;
  --primary-hover: #1a73e8;

  /* UI elements */
  --secondary: #f0f4ff;
  --secondary-hover: #dce6ff;
  --muted: #6c757d;
  --border: #e2e8f0;
  --card-bg: #ffffff;
  --card-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);

  /* Status colors */
  --success: #28a745;
  --warning: #ffc107;
  --error: #dc3545;
  --info: #17a2b8;

  /* Text colors */
  --text-primary: #1e1e1e;
  --text-secondary: #4a5568;
  --text-muted: #6c757d;
  --text-light: #f8f9fa;
}

/* Dark mode variables */
.dark {
  --background: #121212;
  --foreground: #ffffff;
  --primary: #3b82f6;
  --primary-hover: #60a5fa;
  --secondary: #2c2c2c;
  --secondary-hover: #3a3a3a;
  --muted: #a0a0a0;
  --border: #3a3a3a;
  --card-bg: #1e1e1e;
  --card-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3);
  --success: #2dd36f;
  --warning: #ffc409;
  --error: #eb445a;
  --info: #50c8ff;
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --text-light: #f8f9fa;
}

/* ========== BASE STYLES ========== */
html {
  background-color: var(--background);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
}

a {
  color: var(--primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* ========== UTILITY CLASSES ========== */
.bg-card {
  background-color: var(--card-bg);
  box-shadow: var(--card-shadow);
}

.border-theme {
  border-color: var(--border);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

/* ========== COMPONENT STYLES ========== */
/* Page Loader Animation */
.page-loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--background);
  z-index: 9999;
}

.loader {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: block;
  margin: 15px auto;
  position: relative;
  background: var(--foreground);
  box-shadow:
    -24px 0 var(--foreground),
    24px 0 var(--foreground);
  box-sizing: border-box;
  animation: shadowPulse 2s linear infinite;
}

@keyframes shadowPulse {
  33% {
    background: var(--foreground);
    box-shadow:
      -24px 0 var(--primary),
      24px 0 var(--foreground);
  }
  66% {
    background: var(--primary);
    box-shadow:
      -24px 0 var(--foreground),
      24px 0 var(--foreground);
  }
  100% {
    background: var(--foreground);
    box-shadow:
      -24px 0 var(--foreground),
      24px 0 var(--primary);
  }
}

.loader-text {
  font-size: 24px;
  font-weight: 500;
  color: var(--text-primary);
  letter-spacing: 1px;
}

.dot-animation {
  display: inline-block;
}

.dot {
  opacity: 0;
  animation: dotFade 1.5s infinite;
  animation-fill-mode: forwards;
}

.dot:nth-child(1) {
  animation-delay: 0.2s;
}

.dot:nth-child(2) {
  animation-delay: 0.4s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes dotFade {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* ========== ANT DESIGN OVERRIDES ========== */

.ant-drawer-body .ant-menu-submenu-title {
  height: auto !important;
  line-height: 1.5 !important;
  padding: 12px 16px !important;
}

.ant-drawer-body .ant-menu-item {
  height: auto !important;
  line-height: 1.5 !important;
  padding: 12px 16px !important;
}

.ant-drawer-body .ant-menu-submenu-content {
  padding: 12px;
  background-color: var(--secondary);
  border-radius: 8px;
  margin: 0 12px 12px;
}

/* Dark mode specific overrides for Ant Design components */
.dark .ant-menu {
  background-color: transparent !important;
  color: var(--text-primary) !important;
}

.dark .ant-drawer-content {
  background-color: var(--card-bg) !important;
}

.dark .ant-drawer-header {
  background-color: var(--card-bg) !important;
  border-bottom-color: var(--border) !important;
}

.dark .ant-drawer-title {
  color: var(--text-primary) !important;
}

.dark .ant-menu-item:hover:not(.ant-menu-item-selected) {
  color: var(--primary) !important;
}

.dark .ant-menu-item-selected {
  color: var(--primary) !important;
}

.dark .ant-btn-default {
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .ant-btn-text {
  color: var(--text-primary) !important;
}

.dark .ant-btn-text:hover {
  background-color: var(--secondary-hover) !important;
}

/* Header menu styles */
.ant-menu-horizontal .ant-menu-item,
.ant-menu-horizontal .ant-menu-submenu-title {
  font-size: 16px;
  font-weight: 500;
  padding: 0 16px;
}

.ant-menu-horizontal .ant-menu-submenu-title:hover,
.ant-menu-horizontal .ant-menu-item:hover:not(.ant-menu-item-selected) {
  color: var(--primary) !important;
}

.ant-menu-horizontal .ant-menu-item-selected {
  font-weight: 600;
}

/* Mobile menu styles */
.ant-drawer-body .ant-menu-item,
.ant-drawer-body .ant-menu-submenu-title {
  font-size: 16px;
  font-weight: 500;
}
