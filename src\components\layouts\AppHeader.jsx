import { Layout, Button, Avatar, Badge, Dropdown, Tooltip } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  BellOutlined,
  BulbOutlined,
  BulbFilled,
  UserOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { logo_lite, logo_dark } from '@/assets';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Header } = Layout;

const AppHeader = ({
  collapsed,
  toggleCollapsed,
  toggleMobileDrawer,
  isMobile,
  isTablet,
  isDark,
  notificationItems,
  userMenuItems,
  setSearchModalVisible,
  notifications,
  profile,
  interviewItems,
  upcomingInterviews,
  handleThemeToggle,
  showInterviews = false,
}) => {
  const { isLargeDesktop } = useDeviceDetect();

  // Responsive icon and button sizes
  const iconStyle = {
    fontSize: isMobile ? '18px' : isTablet ? '20px' : '22px',
  };

  const buttonStyle = {
    fontSize: isMobile ? '14px' : isTablet ? '15px' : '16px',
    height: isMobile ? '36px' : isTablet ? '38px' : '40px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '8px',
  };

  return (
    <Header
      className="p-0 bg-card shadow-sm flex items-center justify-between sticky-header"
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 1,
        width: '100%',
        padding: isMobile ? '0 12px' : '0 16px',
        height: isMobile ? '56px' : '64px',
      }}
    >
      <div className="flex items-center">
        {isMobile ? (
          <Button
            type="default"
            icon={<MenuUnfoldOutlined style={iconStyle} />}
            onClick={toggleMobileDrawer}
            style={buttonStyle}
            size={isMobile ? 'middle' : 'large'}
          />
        ) : (
          <Button
            type="default"
            icon={
              collapsed ? (
                <MenuUnfoldOutlined style={iconStyle} />
              ) : (
                <MenuFoldOutlined style={iconStyle} />
              )
            }
            onClick={toggleCollapsed}
            style={buttonStyle}
            size={isMobile ? 'middle' : 'large'}
          />
        )}

        {isMobile && (
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="logo"
            className="w-24 ml-2"
          />
        )}
      </div>

      <div className="flex items-center">
        {/* Search button - hide on small mobile */}
        {(!isMobile || isLargeDesktop) && (
          <Tooltip title="Search (Ctrl+K)">
            <Button
              type="default"
              icon={<SearchOutlined style={iconStyle} />}
              onClick={() => setSearchModalVisible(true)}
              style={{ ...buttonStyle, borderRadius: '8px' }}
              size={isMobile ? 'middle' : 'large'}
            >
              {isLargeDesktop && (
                <span
                  className="ml-1"
                  style={{ fontSize: isMobile ? '14px' : '16px' }}
                >
                  Search
                </span>
              )}
            </Button>
          </Tooltip>
        )}

        {/* Interviews dropdown - only show if enabled */}
        {showInterviews && upcomingInterviews.length > 0 && (
          <Tooltip title="Upcoming Interviews">
            <Dropdown
              menu={{ items: interviewItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Badge count={upcomingInterviews.length}>
                <Button
                  type="default"
                  icon={<CalendarOutlined style={iconStyle} />}
                  style={buttonStyle}
                  size={isMobile ? 'middle' : 'large'}
                />
              </Badge>
            </Dropdown>
          </Tooltip>
        )}

        {/* Notifications */}
        <Tooltip title="Notifications">
          <Dropdown
            menu={{ items: notificationItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <Badge count={notifications.filter((n) => !n.read).length}>
              <Button
                type="default"
                icon={<BellOutlined style={iconStyle} />}
                style={buttonStyle}
                size={isMobile ? 'middle' : 'large'}
              />
            </Badge>
          </Dropdown>
        </Tooltip>

        {/* Theme toggle - hide on mobile */}
        {!isMobile && (
          <Tooltip title="Toggle theme">
            <Button
              type="default"
              icon={isDark ? <BulbFilled style={iconStyle} /> : <BulbOutlined style={iconStyle} />}
              onClick={handleThemeToggle}
              style={buttonStyle}
              size={isMobile ? 'middle' : 'large'}
            />
          </Tooltip>
        )}

        {/* User menu */}
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          trigger={['click']}
        >
          <Button
            type="default"
            style={buttonStyle}
            size={isMobile ? 'middle' : 'large'}
          >
            <Avatar
              size={isMobile ? 28 : 32}
              icon={<UserOutlined style={{ fontSize: isMobile ? '16px' : '18px' }} />}
              src={profile?.profile_photo_url}
            />
            {!isMobile && isLargeDesktop && (
              <span
                className="hidden md:inline"
                style={{ fontSize: isMobile ? '14px' : '16px' }}
              >
                {profile.role == 'company'
                  ? profile.company_name || profile?.username || 'User'
                  : profile?.full_name || profile?.username || 'User'}
              </span>
            )}
          </Button>
        </Dropdown>
      </div>
    </Header>
  );
};

export default AppHeader;
