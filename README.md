# Project Structure and Conventions

This project uses the following file organization:

- `src/components/` - Reusable UI components
- `src/app/layouts/` - Layout components for different user roles
- `src/hooks/` - Custom React hooks
- `src/store/` - Zustand state management
- `src/router/` - Routing configuration
- `src/utils/` - Utility functions

When adding new files, please follow these existing patterns to maintain consistency.
