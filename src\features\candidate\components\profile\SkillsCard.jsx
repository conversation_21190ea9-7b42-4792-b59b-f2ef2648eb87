/**
 * Skills & Expertise Card Component
 * Shows skills organized by categories
 */
import React from 'react';
import { Card, Typography, Tag, Alert, Button } from 'antd';
import { Zap } from 'lucide-react';
import { SKILL_CATEGORIES } from '@/features/candidate/constants';

const { Title } = Typography;

const SkillsCard = ({ profile, onEditProfile }) => {
  return (
    <Card
      title={
        <span className="flex items-center">
          <Zap size={18} className="mr-2 text-orange-500" />
          Skills & Expertise
        </span>
      }
      className="shadow-sm rounded-lg"
    >
      {profile?.skills && Array.isArray(profile.skills) && profile.skills.length > 0 ? (
        <div className="space-y-6">
          {SKILL_CATEGORIES.map((category) => {
            const categorySkills = profile.skills.filter(
              (skill) => skill.category === category
            );
            if (categorySkills.length === 0) return null;

            return (
              <div key={category} className="border-l-4 border-blue-500 pl-4">
                <Title level={5} className="mb-3 text-gray-700">
                  {category}
                </Title>
                <div className="flex flex-wrap gap-2">
                  {categorySkills.map((skill) => (
                    <Tag
                      key={skill.name}
                      color="blue"
                      className="px-3 py-2 text-sm rounded-lg border-0 bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors"
                    >
                      <span className="font-medium">{skill.name}</span>
                      {skill.years_experience && (
                        <span className="ml-1 text-xs opacity-75">
                          ({skill.years_experience}+ yrs)
                        </span>
                      )}
                    </Tag>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <Alert
          message="Showcase your skills"
          description="Add your technical and professional skills to stand out to employers and get better job matches."
          type="info"
          showIcon
          className="border-l-4 border-blue-500"
          action={
            <Button
              size="small"
              type="primary"
              onClick={onEditProfile}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Add Skills
            </Button>
          }
        />
      )}
    </Card>
  );
};

export default SkillsCard;
