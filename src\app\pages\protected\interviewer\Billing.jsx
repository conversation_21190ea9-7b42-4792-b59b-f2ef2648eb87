import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Tabs,
  Button,
  Row,
  Col,
  Statistic,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Checkbox,
  Divider,
  Table,
  Alert,
  Empty,
  Spin,
  Progress,
} from 'antd';
import {
  BankOutlined,
  WalletOutlined,
  TransactionOutlined,
  // RupeeOutlined,
  HistoryOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  DownloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import { showToast } from '@/utils/toast';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const RecruiterBilling = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [withdrawModalVisible, setWithdrawModalVisible] = useState(false);
  const [addBankModalVisible, setAddBankModalVisible] = useState(false);
  const [billingData, setBillingData] = useState(null);
  const [form] = Form.useForm();
  const [withdrawForm] = Form.useForm();

  const { user } = useAuth();

  // Load mock billing data on component mount
  useEffect(() => {
    loadMockBillingData();
  }, []);

  // Load mock billing data
  const loadMockBillingData = () => {
    setLoading(true);

    // Mock earnings data
    const mockEarnings = {
      available: 24500,
      pending: 12000,
      total_earned: 98500,
      monthly_target: 50000,
      current_month: 32500,
      withdrawal_threshold: 5000,
    };

    // Mock bank accounts
    const mockBankAccounts = [
      {
        id: 'ba_1',
        account_holder: 'John Doe',
        bank_name: 'HDFC Bank',
        account_number: 'XXXX4567',
        ifsc_code: 'HDFC0001234',
        is_default: true,
        verified: true,
      },
      {
        id: 'ba_2',
        account_holder: 'John Doe',
        bank_name: 'ICICI Bank',
        account_number: 'XXXX7890',
        ifsc_code: 'ICIC0005678',
        is_default: false,
        verified: true,
      },
    ];

    // Mock earnings history
    const mockEarningsHistory = [
      {
        id: 'earn_001',
        amount: 8500,
        status: 'completed',
        date: dayjs().subtract(5, 'day').format('YYYY-MM-DD'),
        description: 'Commission for candidate placement - Software Engineer',
        company: 'TechCorp Solutions',
      },
      {
        id: 'earn_002',
        amount: 12000,
        status: 'pending',
        date: dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
        description: 'Commission for candidate placement - Product Manager',
        company: 'InnovateTech',
      },
      {
        id: 'earn_003',
        amount: 7500,
        status: 'completed',
        date: dayjs().subtract(15, 'day').format('YYYY-MM-DD'),
        description: 'Commission for candidate placement - UI/UX Designer',
        company: 'DesignHub',
      },
      {
        id: 'earn_004',
        amount: 9000,
        status: 'completed',
        date: dayjs().subtract(25, 'day').format('YYYY-MM-DD'),
        description: 'Commission for candidate placement - DevOps Engineer',
        company: 'CloudSys Technologies',
      },
    ];

    // Mock withdrawals
    const mockWithdrawals = [
      {
        id: 'with_001',
        amount: 15000,
        status: 'completed',
        date: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        bank_account: 'HDFC Bank (XXXX4567)',
        reference: 'REF123456',
      },
      {
        id: 'with_002',
        amount: 25000,
        status: 'completed',
        date: dayjs().subtract(60, 'day').format('YYYY-MM-DD'),
        bank_account: 'HDFC Bank (XXXX4567)',
        reference: 'REF789012',
      },
      {
        id: 'with_003',
        amount: 20000,
        status: 'completed',
        date: dayjs().subtract(90, 'day').format('YYYY-MM-DD'),
        bank_account: 'ICICI Bank (XXXX7890)',
        reference: 'REF345678',
      },
    ];

    const mockData = {
      earnings: mockEarnings,
      bankAccounts: mockBankAccounts,
      earningsHistory: mockEarningsHistory,
      withdrawals: mockWithdrawals,
    };

    setBillingData(mockData);
    setLoading(false);
  };

  // Add bank account
  const addBankAccount = async (values) => {
    setLoading(true);
    try {
      // Create new bank account object
      const newBankAccount = {
        id: `ba_${Math.floor(Math.random() * 1000)}`,
        account_holder: values.accountHolder,
        bank_name: values.bankName,
        account_number: `XXXX${values.accountNumber.slice(-4)}`,
        ifsc_code: values.ifscCode,
        is_default: values.setDefault,
        verified: false,
      };

      // If setting as default, update other bank accounts
      if (values.setDefault) {
        const updatedBankAccounts = billingData.bankAccounts.map((account) => ({
          ...account,
          is_default: false,
        }));

        setBillingData({
          ...billingData,
          bankAccounts: [...updatedBankAccounts, newBankAccount],
        });
      } else {
        setBillingData({
          ...billingData,
          bankAccounts: [...billingData.bankAccounts, newBankAccount],
        });
      }

      showToast.success('Bank account added successfully. Verification pending.');
      setAddBankModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Error adding bank account:', error);
      showToast.error('Failed to add bank account');
    } finally {
      setLoading(false);
    }
  };

  // Remove bank account
  const removeBankAccount = async (id) => {
    setLoading(true);
    try {
      const updatedBankAccounts = billingData.bankAccounts.filter((account) => account.id !== id);

      // If removing default account, set another as default if available
      if (
        billingData.bankAccounts.find((account) => account.id === id)?.is_default &&
        updatedBankAccounts.length > 0
      ) {
        updatedBankAccounts[0].is_default = true;
      }

      setBillingData({
        ...billingData,
        bankAccounts: updatedBankAccounts,
      });

      showToast.success('Bank account removed successfully');
    } catch (error) {
      console.error('Error removing bank account:', error);
      showToast.error('Failed to remove bank account');
    } finally {
      setLoading(false);
    }
  };

  // Set default bank account
  const setDefaultBankAccount = async (id) => {
    setLoading(true);
    try {
      const updatedBankAccounts = billingData.bankAccounts.map((account) => ({
        ...account,
        is_default: account.id === id,
      }));

      setBillingData({
        ...billingData,
        bankAccounts: updatedBankAccounts,
      });

      showToast.success('Default bank account updated');
    } catch (error) {
      console.error('Error setting default bank account:', error);
      showToast.error('Failed to update default bank account');
    } finally {
      setLoading(false);
    }
  };

  // Handle withdrawal
  const handleWithdrawal = async (values) => {
    setLoading(true);
    try {
      // Check if amount is valid
      if (values.amount > billingData.earnings.available) {
        throw new Error('Withdrawal amount exceeds available balance');
      }

      if (values.amount < billingData.earnings.withdrawal_threshold) {
        throw new Error(
          `Minimum withdrawal amount is ₹${billingData.earnings.withdrawal_threshold}`
        );
      }

      // Create new withdrawal
      const newWithdrawal = {
        id: `with_${Math.floor(Math.random() * 10000)}`,
        amount: values.amount,
        status: 'processing',
        date: dayjs().format('YYYY-MM-DD'),
        bank_account: `${billingData.bankAccounts.find((account) => account.id === values.bankAccount).bank_name} (${billingData.bankAccounts.find((account) => account.id === values.bankAccount).account_number})`,
        reference: `REF${Math.floor(Math.random() * 1000000)}`,
      };

      // Update earnings
      const updatedEarnings = {
        ...billingData.earnings,
        available: billingData.earnings.available - values.amount,
      };

      setBillingData({
        ...billingData,
        withdrawals: [newWithdrawal, ...billingData.withdrawals],
        earnings: updatedEarnings,
      });

      showToast.success('Withdrawal request submitted successfully');
      setWithdrawModalVisible(false);
      withdrawForm.resetFields();

      // Simulate withdrawal processing
      setTimeout(() => {
        const updatedWithdrawals = billingData.withdrawals.map((withdrawal) =>
          withdrawal.id === newWithdrawal.id ? { ...withdrawal, status: 'completed' } : withdrawal
        );

        setBillingData((prevData) => ({
          ...prevData,
          withdrawals: updatedWithdrawals,
        }));

        showToast.success('Withdrawal processed successfully');
      }, 5000);
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      showToast.error(error.message || 'Failed to process withdrawal');
    } finally {
      setLoading(false);
    }
  };

  // Columns for the earnings history table
  const earningsColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => dayjs(date).format('DD MMM YYYY'),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Company',
      dataIndex: 'company',
      key: 'company',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `₹${amount.toFixed(2)}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'completed' ? 'green' : status === 'pending' ? 'orange' : 'volcano'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
  ];

  // Columns for the withdrawals table
  const withdrawalsColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => dayjs(date).format('DD MMM YYYY'),
    },
    {
      title: 'Bank Account',
      dataIndex: 'bank_account',
      key: 'bank_account',
    },
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `₹${amount.toFixed(2)}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag
          color={status === 'completed' ? 'green' : status === 'processing' ? 'blue' : 'volcano'}
        >
          {status.toUpperCase()}
        </Tag>
      ),
    },
  ];

  // Render bank account item
  const renderBankAccount = (account) => {
    return (
      <Card
        key={account.id}
        className="bank-account-card mb-4"
        size="small"
        extra={
          <Space>
            {account.is_default && <Tag color="green">Default</Tag>}
            {!account.is_default && (
              <Button
                type="link"
                size="small"
                onClick={() => setDefaultBankAccount(account.id)}
              >
                Set Default
              </Button>
            )}
            <Button
              type="link"
              danger
              size="small"
              onClick={() => removeBankAccount(account.id)}
            >
              Remove
            </Button>
          </Space>
        }
      >
        <div className="flex items-center">
          <div className="mr-4">
            <BankOutlined style={{ fontSize: 24 }} />
          </div>
          <div>
            <div className="font-medium">{account.bank_name}</div>
            <div className="text-muted text-sm">
              {account.account_holder} • {account.account_number} • {account.ifsc_code}
            </div>
            {!account.verified && (
              <Tag
                color="orange"
                className="mt-1"
              >
                Verification Pending
              </Tag>
            )}
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="recruiter-billing-page">
      <Title
        level={4}
        className="mb-6"
      >
        Earnings & Withdrawals
      </Title>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="billing-tabs"
      >
        {/* Overview Tab */}
        <TabPane
          tab={
            <span className="tab-label">
              <WalletOutlined />
              Overview
            </span>
          }
          key="overview"
        >
          {loading ? (
            <div className="text-center py-8">
              <Spin size="large" />
            </div>
          ) : (
            <>
              <Row gutter={[24, 24]}>
                <Col
                  xs={24}
                  md={16}
                >
                  <Card
                    title="Earnings Summary"
                    className="h-full"
                  >
                    {billingData?.earnings ? (
                      <>
                        <Row gutter={[16, 16]}>
                          <Col
                            xs={24}
                            sm={8}
                          >
                            <Statistic
                              title="Available Balance"
                              value={billingData.earnings.available}
                              prefix="₹"
                              precision={2}
                              valueStyle={{ color: '#3f8600' }}
                            />
                            <Button
                              type="primary"
                              className="mt-4"
                              onClick={() => setWithdrawModalVisible(true)}
                              disabled={
                                billingData.earnings.available <
                                billingData.earnings.withdrawal_threshold
                              }
                            >
                              Withdraw Funds
                            </Button>
                            {billingData.earnings.available <
                              billingData.earnings.withdrawal_threshold && (
                              <div className="text-muted text-xs mt-2">
                                Minimum withdrawal: ₹{billingData.earnings.withdrawal_threshold}
                              </div>
                            )}
                          </Col>
                          <Col
                            xs={24}
                            sm={8}
                          >
                            <Statistic
                              title="Pending Earnings"
                              value={billingData.earnings.pending}
                              prefix="₹"
                              precision={2}
                              valueStyle={{ color: '#faad14' }}
                            />
                            <div className="text-muted text-xs mt-2">
                              Will be available after verification
                            </div>
                          </Col>
                          <Col
                            xs={24}
                            sm={8}
                          >
                            <Statistic
                              title="Total Earned"
                              value={billingData.earnings.total_earned}
                              prefix="₹"
                              precision={2}
                            />
                            <div className="text-muted text-xs mt-2">Lifetime earnings</div>
                          </Col>
                        </Row>

                        <Divider />

                        <div className="monthly-progress mb-4">
                          <div className="flex justify-between mb-2">
                            <Text>Monthly Target</Text>
                            <Text strong>
                              ₹{billingData.earnings.current_month} / ₹
                              {billingData.earnings.monthly_target}
                            </Text>
                          </div>
                          <Progress
                            percent={Math.round(
                              (billingData.earnings.current_month /
                                billingData.earnings.monthly_target) *
                                100
                            )}
                            status="active"
                            strokeColor={{
                              '0%': '#108ee9',
                              '100%': '#87d068',
                            }}
                          />
                          <div className="text-muted text-xs mt-2">
                            {Math.round(
                              (billingData.earnings.current_month /
                                billingData.earnings.monthly_target) *
                                100
                            )}
                            % of monthly target achieved
                          </div>
                        </div>

                        <div className="earnings-stats">
                          <Row gutter={[16, 16]}>
                            <Col span={12}>
                              <Card size="small">
                                <Statistic
                                  title="This Month"
                                  value={billingData.earnings.current_month}
                                  precision={2}
                                  valueStyle={{ color: '#3f8600' }}
                                  prefix="₹"
                                  suffix={<ArrowUpOutlined style={{ fontSize: 16 }} />}
                                />
                              </Card>
                            </Col>
                            <Col span={12}>
                              <Card size="small">
                                <Statistic
                                  title="Average Monthly"
                                  value={Math.round(billingData.earnings.total_earned / 6)} // Assuming 6 months of activity
                                  precision={2}
                                  valueStyle={{ color: '#cf1322' }}
                                  prefix="₹"
                                  suffix={<ArrowDownOutlined style={{ fontSize: 16 }} />}
                                />
                              </Card>
                            </Col>
                          </Row>
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-6">
                        <Empty description="No earnings data available" />
                      </div>
                    )}
                  </Card>
                </Col>

                <Col
                  xs={24}
                  md={8}
                >
                  <Card
                    title="Bank Accounts"
                    className="h-full"
                  >
                    {billingData?.bankAccounts && billingData.bankAccounts.length > 0 ? (
                      <div className="bank-accounts-list">
                        {billingData.bankAccounts.map((account) => renderBankAccount(account))}
                      </div>
                    ) : (
                      <Empty description="No bank accounts" />
                    )}

                    <Button
                      type="primary"
                      block
                      className="mt-4"
                      onClick={() => setAddBankModalVisible(true)}
                    >
                      Add Bank Account
                    </Button>
                  </Card>
                </Col>
              </Row>

              <Card
                title="Recent Earnings"
                className="mt-6"
              >
                {billingData?.earningsHistory && billingData.earningsHistory.length > 0 ? (
                  <Table
                    columns={earningsColumns}
                    dataSource={billingData.earningsHistory.slice(0, 5)}
                    rowKey="id"
                    pagination={false}
                  />
                ) : (
                  <Empty description="No recent earnings" />
                )}
                <div className="text-right mt-4">
                  <Button
                    type="link"
                    onClick={() => setActiveTab('earnings')}
                  >
                    View All Earnings
                  </Button>
                </div>
              </Card>
            </>
          )}
        </TabPane>

        {/* Earnings History Tab */}
        <TabPane
          tab={
            <span className="tab-label">
              <TransactionOutlined />
              Earnings History
            </span>
          }
          key="earnings"
        >
          <Card>
            {loading ? (
              <div className="text-center py-4">
                <Spin />
              </div>
            ) : billingData?.earningsHistory && billingData.earningsHistory.length > 0 ? (
              <Table
                columns={earningsColumns}
                dataSource={billingData.earningsHistory}
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            ) : (
              <Empty description="No earnings history available" />
            )}
          </Card>
        </TabPane>

        {/* Withdrawals Tab */}
        <TabPane
          tab={
            <span className="tab-label">
              <HistoryOutlined />
              Withdrawals
            </span>
          }
          key="withdrawals"
        >
          <Card>
            <div className="mb-4 flex justify-between items-center">
              <Title
                level={5}
                className="mb-0"
              >
                Withdrawal History
              </Title>
              <Button
                type="primary"
                onClick={() => setWithdrawModalVisible(true)}
                disabled={
                  !billingData?.earnings ||
                  billingData.earnings.available < billingData.earnings.withdrawal_threshold
                }
              >
                Withdraw Funds
              </Button>
            </div>

            {loading ? (
              <div className="text-center py-4">
                <Spin />
              </div>
            ) : billingData?.withdrawals && billingData.withdrawals.length > 0 ? (
              <Table
                columns={withdrawalsColumns}
                dataSource={billingData.withdrawals}
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            ) : (
              <Empty description="No withdrawal history available" />
            )}
          </Card>
        </TabPane>
      </Tabs>

      {/* Withdraw Funds Modal */}
      <Modal
        title="Withdraw Funds"
        open={withdrawModalVisible}
        onCancel={() => {
          setWithdrawModalVisible(false);
          withdrawForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={withdrawForm}
          layout="vertical"
          onFinish={handleWithdrawal}
          initialValues={{
            bankAccount: billingData?.bankAccounts?.find((account) => account.is_default)?.id,
            amount: billingData?.earnings?.available || 0,
          }}
        >
          <div className="mb-4">
            <Alert
              message={`Available Balance: ₹${billingData?.earnings?.available?.toFixed(2) || '0.00'}`}
              type="info"
              showIcon
            />
          </div>

          <Form.Item
            name="bankAccount"
            label="Bank Account"
            rules={[{ required: true, message: 'Please select a bank account' }]}
          >
            <Select>
              {billingData?.bankAccounts?.map((account) => (
                <Option
                  key={account.id}
                  value={account.id}
                >
                  {account.bank_name} - {account.account_number} {account.is_default && '(Default)'}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="Amount"
            rules={[
              { required: true, message: 'Please enter amount' },
              {
                validator: (_, value) => {
                  if (value > (billingData?.earnings?.available || 0)) {
                    return Promise.reject('Amount exceeds available balance');
                  }
                  if (value < (billingData?.earnings?.withdrawal_threshold || 0)) {
                    return Promise.reject(
                      `Minimum withdrawal amount is ₹${billingData?.earnings?.withdrawal_threshold}`
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              prefix="₹"
              type="number"
              min={billingData?.earnings?.withdrawal_threshold || 0}
              max={billingData?.earnings?.available || 0}
              step="100"
            />
          </Form.Item>

          <Alert
            message="Withdrawal Information"
            description={
              <ul className="list-disc pl-4 mt-2">
                <li>Withdrawals are processed within 1-3 business days</li>
                <li>Minimum withdrawal amount is ₹{billingData?.earnings?.withdrawal_threshold}</li>
                <li>Bank account must be verified before withdrawal</li>
                <li>A small processing fee may be charged by the bank</li>
              </ul>
            }
            type="info"
            showIcon
            className="mb-4"
          />

          <Form.Item>
            <div className="flex justify-end">
              <Space>
                <Button
                  onClick={() => {
                    setWithdrawModalVisible(false);
                    withdrawForm.resetFields();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  disabled={
                    !billingData?.earnings ||
                    billingData.earnings.available < billingData.earnings.withdrawal_threshold
                  }
                >
                  Withdraw Funds
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Bank Account Modal */}
      <Modal
        title="Add Bank Account"
        open={addBankModalVisible}
        onCancel={() => {
          setAddBankModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={addBankAccount}
          initialValues={{ setDefault: billingData?.bankAccounts?.length === 0 }}
        >
          <Form.Item
            name="accountHolder"
            label="Account Holder Name"
            rules={[{ required: true, message: 'Please enter account holder name' }]}
          >
            <Input placeholder="John Doe" />
          </Form.Item>

          <Form.Item
            name="bankName"
            label="Bank Name"
            rules={[{ required: true, message: 'Please select bank name' }]}
          >
            <Select placeholder="Select bank">
              <Option value="SBI">State Bank of India</Option>
              <Option value="HDFC Bank">HDFC Bank</Option>
              <Option value="ICICI Bank">ICICI Bank</Option>
              <Option value="Axis Bank">Axis Bank</Option>
              <Option value="Kotak Mahindra Bank">Kotak Mahindra Bank</Option>
              <Option value="Punjab National Bank">Punjab National Bank</Option>
              <Option value="Bank of Baroda">Bank of Baroda</Option>
              <Option value="Yes Bank">Yes Bank</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="accountNumber"
            label="Account Number"
            rules={[
              { required: true, message: 'Please enter account number' },
              { min: 9, message: 'Account number must be at least 9 digits' },
              { max: 18, message: 'Account number cannot exceed 18 digits' },
            ]}
          >
            <Input placeholder="Enter account number" />
          </Form.Item>

          <Form.Item
            name="confirmAccountNumber"
            label="Confirm Account Number"
            dependencies={['accountNumber']}
            rules={[
              { required: true, message: 'Please confirm account number' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('accountNumber') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('Account numbers do not match'));
                },
              }),
            ]}
          >
            <Input placeholder="Confirm account number" />
          </Form.Item>

          <Form.Item
            name="ifscCode"
            label="IFSC Code"
            rules={[
              { required: true, message: 'Please enter IFSC code' },
              { pattern: /^[A-Z]{4}0[A-Z0-9]{6}$/, message: 'Please enter a valid IFSC code' },
            ]}
          >
            <Input
              placeholder="e.g., HDFC0001234"
              style={{ textTransform: 'uppercase' }}
            />
          </Form.Item>

          <Form.Item
            name="setDefault"
            valuePropName="checked"
          >
            <Checkbox>Set as default bank account</Checkbox>
          </Form.Item>

          <Alert
            message="Verification Notice"
            description="Your bank account will need to be verified before you can withdraw funds. This process may take 2-3 business days."
            type="info"
            showIcon
            className="mb-4"
          />

          <Form.Item>
            <div className="flex justify-end">
              <Space>
                <Button
                  onClick={() => {
                    setAddBankModalVisible(false);
                    form.resetFields();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  Add Bank Account
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// <Text strong>₹{billingData.earnings.current_month} / ₹{billingData.earnings.monthly_target}</Text>
export default RecruiterBilling;
