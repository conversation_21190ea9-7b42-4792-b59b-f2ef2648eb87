import React from 'react';
import { Button, Typography } from 'antd';
import { Link } from 'react-router-dom';
import { useColorModeStore } from '@/store/colorMode.store';

const { Title, Paragraph } = Typography;

const AccessDenied = () => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  return (
    <div className="flex flex-col justify-center items-center min-h-[80vh] px-4 text-center">
      <div className="w-full max-w-md mb-8">
        <svg
          viewBox="0 0 500 400"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-auto"
        >
          <path
            d="M250 300C323.797 300 384 270.751 384 235C384 199.249 323.797 170 250 170C176.203 170 116 199.249 116 235C116 270.751 176.203 300 250 300Z"
            fill={isDark ? '#2C2C2C' : '#F0F4FF'}
          />
          <rect
            x="175"
            y="50"
            width="150"
            height="200"
            rx="10"
            fill={isDark ? '#3A3A3A' : '#DCE6FF'}
          />
          <rect
            x="200"
            y="120"
            width="100"
            height="60"
            rx="5"
            fill={isDark ? '#1E1E1E' : '#FFFFFF'}
          />
          <path
            d="M250 120V180"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="12"
            strokeLinecap="round"
          />
          <circle
            cx="250"
            cy="220"
            r="15"
            fill={isDark ? '#60A5FA' : '#0056D2'}
          />
          <path
            d="M210 120H290"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="12"
            strokeLinecap="round"
          />
          <path
            d="M175 120V80C175 63.4315 188.431 50 205 50H295C311.569 50 325 63.4315 325 80V120"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="8"
            strokeLinecap="round"
          />
          <text
            x="250"
            y="350"
            fontFamily="Arial"
            fontSize="120"
            fontWeight="bold"
            fill={isDark ? '#60A5FA' : '#0056D2'}
            textAnchor="middle"
          >
            403
          </text>
        </svg>
      </div>

      <Title
        level={2}
        className="mb-2"
      >
        Access Denied
      </Title>
      <Paragraph className="text-lg mb-8 max-w-lg">
        You don't have permission to access this page. Please contact your administrator if you
        believe this is an error.
      </Paragraph>

      <div className="flex gap-4">
        <Button
          type="primary"
          size="large"
          onClick={() => window.history.back()}
        >
          Go Back
        </Button>
        <Link to="/">
          <Button size="large">Home Page</Button>
        </Link>
      </div>
    </div>
  );
};

export default AccessDenied;
