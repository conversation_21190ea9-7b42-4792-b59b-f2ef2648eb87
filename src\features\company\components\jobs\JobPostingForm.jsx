import React from 'react';
import { Form, Input, Select, Button, InputNumber, Switch, Card, Typography, Divider } from 'antd';
import {
  BankOutlined,
  EnvironmentOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  BookOutlined,
  SaveOutlined,
  SendOutlined,
} from '@ant-design/icons';
import { EXPERIENCE_LEVELS } from '@/features/company/constants';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const JobPostingForm = ({ onSubmit, initialValues = {}, loading = false, editMode = false }) => {
  const [form] = Form.useForm();

  const handleFinish = (values) => {
    onSubmit({
      ...values,
      status: values.publish ? 'active' : 'draft',
    });
  };

  return (
    <Card className="shadow-sm">
      <Title level={3}>{editMode ? 'Edit Job Posting' : 'Create New Job Posting'}</Title>
      <Text
        type="secondary"
        className="mb-6 block"
      >
        Fill in the details below to create a new job posting for candidates.
      </Text>

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          publish: true,
          ...initialValues,
        }}
        onFinish={handleFinish}
      >
        <Divider orientation="left">Basic Information</Divider>

        <Form.Item
          name="title"
          label="Job Title"
          rules={[{ required: true, message: 'Please enter the job title' }]}
        >
          <Input
            prefix={<BankOutlined />}
            placeholder="e.g. Senior Sales Manager"
          />
        </Form.Item>

        <Form.Item
          name="location"
          label="Location"
          rules={[{ required: true, message: 'Please enter the job location' }]}
        >
          <Input
            prefix={<EnvironmentOutlined />}
            placeholder="e.g. Mumbai, Delhi, Remote"
          />
        </Form.Item>

        <Form.Item
          name="employment_type"
          label="Employment Type"
          rules={[{ required: true, message: 'Please select employment type' }]}
        >
          <Select placeholder="Select employment type">
            <Option value="Full-time">Full-time</Option>
            <Option value="Part-time">Part-time</Option>
            <Option value="Contract">Contract</Option>
            <Option value="Temporary">Temporary</Option>
            <Option value="Internship">Internship</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="experience_level"
          label="Experience Level"
          rules={[{ required: true, message: 'Please select experience level' }]}
        >
          <Select placeholder="Select experience level">
            {EXPERIENCE_LEVELS.map((level) => (
              <Option
                key={level}
                value={level}
              >
                {level}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="Salary Range (₹ LPA)"
          className="mb-0"
        >
          <Input.Group compact>
            <Form.Item
              name="min_salary"
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber
                style={{ width: '45%' }}
                placeholder="Min"
                min={0}
                max={100}
                prefix={<DollarOutlined />}
              />
            </Form.Item>
            <Input
              style={{ width: '10%', textAlign: 'center' }}
              placeholder="to"
              disabled
            />
            <Form.Item
              name="max_salary"
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber
                style={{ width: '45%' }}
                placeholder="Max"
                min={0}
                max={100}
                prefix={<DollarOutlined />}
              />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        <Divider orientation="left">Job Details</Divider>

        <Form.Item
          name="description"
          label="Job Description"
          rules={[{ required: true, message: 'Please enter job description' }]}
        >
          <TextArea
            rows={6}
            placeholder="Describe the job role, responsibilities, and requirements"
          />
        </Form.Item>

        <Form.Item
          name="skills_required"
          label="Required Skills"
          rules={[{ required: true, message: 'Please enter required skills' }]}
        >
          <Select
            mode="tags"
            placeholder="Add skills (e.g. Sales, Marketing, CRM)"
          >
            <Option value="Sales">Sales</Option>
            <Option value="Marketing">Marketing</Option>
            <Option value="CRM">CRM</Option>
            <Option value="Negotiation">Negotiation</Option>
            <Option value="Client Relationship">Client Relationship</Option>
            <Option value="Market Research">Market Research</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="benefits"
          label="Benefits"
        >
          <TextArea
            rows={4}
            placeholder="List the benefits offered with this position"
          />
        </Form.Item>

        <Form.Item
          name="application_deadline"
          label="Application Deadline"
        >
          <Input type="date" />
        </Form.Item>

        <Divider orientation="left">Publishing Options</Divider>

        <Form.Item
          name="publish"
          valuePropName="checked"
        >
          <Switch
            checkedChildren="Publish"
            unCheckedChildren="Save as Draft"
          />
          <Text
            type="secondary"
            className="ml-2"
          >
            {form.getFieldValue('publish')
              ? 'Job will be visible to candidates immediately'
              : 'Job will be saved as draft'}
          </Text>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={form.getFieldValue('publish') ? <SendOutlined /> : <SaveOutlined />}
          >
            {editMode
              ? form.getFieldValue('publish')
                ? 'Update & Publish'
                : 'Update as Draft'
              : form.getFieldValue('publish')
                ? 'Create & Publish'
                : 'Save as Draft'}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default JobPostingForm;
