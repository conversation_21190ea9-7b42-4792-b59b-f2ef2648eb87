import React, { useState } from 'react';
import { MenuOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Drawer, Menu, Space, Tooltip } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import useDeviceDetect from '@/hooks/useDeviceDetect';
import { useColorModeStore } from '@/store/colorMode.store';
import { AUTH_NAV_ITEMS, getDashboardPath, MAIN_NAV_ITEMS } from '@/utils/constants';
import ColorModeToggle from '../shared/ColorModeToggle';
import { logo_lite, logo_dark } from '../../assets';
import { UserButton } from '@clerk/clerk-react';

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, role } = useAuth();
  const { isMobile, isDesktop } = useDeviceDetect();
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';
  const [drawerVisible, setDrawerVisible] = useState(false);

  const showDrawer = () => setDrawerVisible(true);
  const closeDrawer = () => setDrawerVisible(false);

  const dashboardPath = getDashboardPath(role);

  // Create menu items from MAIN_NAV_ITEMS
  const mainMenuItems = MAIN_NAV_ITEMS.map((item) => ({
    key: item.key,
    label: item.children ? <span>{item.label}</span> : <Link to={item.key}>{item.label}</Link>,
    // icon: item.icon ? <item.icon /> : null,
    children: item.children
      ? item.children.map((child) => ({
          key: `${item.key}-${child.key}`,
          label: <Link to={child.path || `${item.key}/${child.key}`}>{child.label}</Link>,
          icon: child.icon ? <child.icon /> : null,
        }))
      : null,
  }));

  // Create mobile menu items (can include additional items specific to mobile)
  const mobileMenuItems = [
    ...mainMenuItems,
    // Add login/register buttons to mobile menu if user is not authenticated
    ...(user
      ? []
      : AUTH_NAV_ITEMS.unauthenticated.map((item) => ({
          key: item.key,
          label: <Link to={item.key}>{item.label}</Link>,
          icon: item.icon ? <item.icon /> : null,
        }))),
  ];

  // Theme class is applied in App.jsx to avoid conflicts

  return (
    <header
      className="py-3 px-4 sm:px-6 lg:px-10 mx-auto min-h-[70px] rounded-2xl sticky top-4 z-50 backdrop-blur-sm"
      style={
        isDark
          ? {
              boxShadow: '0 0 10px rgba(250, 250, 250, 0.1)',
            }
          : {
              boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
            }
      }
    >
      <div className="flex items-center justify-between w-full max-w-7xl mx-auto">
        {/* Logo */}
        <Link
          to="/"
          className="flex items-center z-10"
        >
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="logo"
            className="w-28 sm:w-36"
          />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden lg:block flex-grow max-w-xl mx-8">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            className="border-0 bg-transparent flex justify-center"
            items={mainMenuItems}
          />
        </div>

        {/* Auth Buttons and Color Mode Toggle */}
        <div className="flex items-center gap-2 sm:gap-3">
          {user ? (
            <Tooltip title={AUTH_NAV_ITEMS.authenticated.description}>
              <Button
                type="primary"
                shape="round"
                size={isMobile ? 'small' : 'large'}
                icon={<AUTH_NAV_ITEMS.authenticated.icon />}
                onClick={() => navigate(dashboardPath)}
              >
                {AUTH_NAV_ITEMS.authenticated.label}
              </Button>
            </Tooltip>
          ) : (
            // Only show login/register buttons on desktop
            !isMobile && (
              <Space size={isMobile ? 2 : 3}>
                {AUTH_NAV_ITEMS.unauthenticated.map((item) => (
                  <Tooltip
                    key={item.key}
                    title={item.description}
                  >
                    <Button
                      shape="round"
                      type={item.key === 'get-started' ? 'primary' : 'default'}
                      size={isMobile ? 'middle' : 'large'}
                      icon={<item.icon />}
                      onClick={() => navigate(item.key)}
                    >
                      {item.label}
                    </Button>
                  </Tooltip>
                ))}
              </Space>
            )
          )}
          <UserButton />
          {/* Color Mode Toggle */}
          <ColorModeToggle />

          {/* Mobile Menu Button - Only show on mobile */}
          {!isDesktop && (
            <Button
              className="ml-1 sm:ml-2"
              type="text"
              icon={<MenuOutlined className="text-foreground" />}
              onClick={showDrawer}
            />
          )}
        </div>
      </div>

      {/* Mobile Drawer - Only render on mobile */}
      {!isDesktop && (
        <Drawer
          title={
            <div className="flex items-center justify-between">
              <img
                src={isDark ? logo_dark : logo_lite}
                alt="logo"
                className="w-28"
              />
              <ColorModeToggle />
            </div>
          }
          placement="right"
          onClose={closeDrawer}
          open={drawerVisible}
          width="80%"
          styles={{
            body: { padding: 0 },
          }}
        >
          <Menu
            mode="vertical"
            selectedKeys={[location.pathname]}
            className="border-0"
            items={mobileMenuItems}
          />
        </Drawer>
      )}
    </header>
  );
};

export default Header;
