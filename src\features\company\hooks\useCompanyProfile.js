/**
 * Company Profile Hook
 *
 * Provides a unified interface for managing company profile data and account settings
 * Uses optimized caching for better performance
 */
import { useState, useCallback } from 'react';
import { message } from 'antd';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCompanyStore from '../store/company.store';

const useCompanyProfile = () => {
  const { user, profile, updateProfile, setError } = useAuth();
  const { uploadCompanyLogo } = useCompanyStore();
  const [loading, setLoading] = useState(false);

  /**
   * Fetch the complete company profile with all related data
   * Uses optimized caching for better performance
   * @param {boolean} forceRefresh - Whether to force a refresh from the database
   */
  const fetchCompanyProfile = useCallback(
    async (forceRefresh = false) => {
      if (!user?.id) return null;

      try {
        setLoading(true);

        // Fetch company profile directly
        const { data, error } = await supabase
          .from('company_profiles_complete')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;

        // Update the profile in auth store
        if (data) {
          updateProfile(data);
        }

        return data;
      } catch (error) {
        console.error('Error fetching company profile:', error);
        setError(error.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [user, setError, updateProfile]
  );

  /**
   * Update company profile data
   * Invalidates cache after update
   * @param {Object} profileData - Profile data to update
   */
  const updateCompanyProfile = useCallback(
    async (profileData) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      setLoading(true);

      try {
        // Update the profile in the database
        const { data, error } = await supabase
          .from('profiles')
          .update({
            ...profileData,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id)
          .select()
          .single();

        if (error) throw error;

        // Update the profile in auth store
        updateProfile(data);

        return data;
      } catch (error) {
        console.error('Error updating company profile:', error);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [user, updateProfile]
  );

  /**
   * Update company account settings
   * Invalidates cache after update
   * @param {Object} settings - Account settings to update
   * @param {string} settingsType - Type of settings (notifications, privacy, preferences)
   */
  const updateAccountSettings = useCallback(
    async (settings, settingsType) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      setLoading(true);

      try {
        // Create the update object with the correct field based on settings type
        const updateData = {};

        switch (settingsType) {
          case 'notifications':
            updateData.notification_settings = settings;
            break;
          case 'privacy':
            updateData.privacy_settings = settings;
            break;
          case 'preferences':
            updateData.preferences = settings;
            break;
          default:
            throw new Error('Invalid settings type');
        }

        // Update the settings in the database
        const { data, error } = await supabase
          .from('company_settings')
          .upsert({
            company_id: user.id,
            ...updateData,
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) throw error;

        // Update the profile with the new settings
        updateProfile({
          ...profile,
          [settingsType === 'notifications'
            ? 'notification_settings'
            : settingsType === 'privacy'
              ? 'privacy_settings'
              : 'preferences']: settings,
        });

        return data;
      } catch (error) {
        console.error(`Error updating ${settingsType} settings:`, error);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [user, profile, updateProfile]
  );

  /**
   * Upload company logo
   * Invalidates cache after upload
   * @param {File} file - Logo file to upload
   */
  const handleLogoUpload = useCallback(
    async (file) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      setLoading(true);

      try {
        const result = await uploadCompanyLogo(user.id, file);

        if (result?.url) {
          // Update the profile with the new logo URL
          await updateCompanyProfile({
            company_logo_url: result.url,
          });

          // Cache is invalidated in updateCompanyProfile
          message.success('Company logo updated successfully');
        }

        return result;
      } catch (error) {
        message.error('Failed to upload company logo');
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [user, uploadCompanyLogo, updateCompanyProfile]
  );

  return {
    loading,
    fetchCompanyProfile,
    updateCompanyProfile,
    updateAccountSettings,
    handleLogoUpload,
  };
};

export default useCompanyProfile;
