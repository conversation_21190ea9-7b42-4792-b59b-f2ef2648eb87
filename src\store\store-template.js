/**
 * Standardized Store Pattern Template
 * 
 * This template defines the consistent patterns all stores should follow:
 * 
 * 1. Consistent naming conventions
 * 2. Standardized loading/error states
 * 3. Unified cache management
 * 4. Common action patterns
 * 5. Consistent middleware usage
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';

// === STANDARDIZED PATTERNS ===

// 1. Initial State Pattern
const createInitialState = (domainSpecificState = {}) => ({
  // Standard UI states (consistent across all stores)
  loading: false,
  error: null,
  
  // Standard cache management
  _cache: {
    lastFetch: null,
    expiry: 5 * 60 * 1000, // 5 minutes default
  },
  
  // Domain-specific state
  ...domainSpecificState,
});

// 2. Loading State Helpers
const createLoadingHelpers = (set) => ({
  setLoading: (loading) => set({ loading }, false, 'setLoading'),
  setError: (error) => set({ error }, false, 'setError'),
  clearError: () => set({ error: null }, false, 'clearError'),
});

// 3. Cache Helpers
const createCacheHelpers = (set, get) => ({
  updateCache: (key, data) => {
    const { _cache } = get();
    set({
      _cache: {
        ..._cache,
        [key]: data,
        lastFetch: Date.now(),
      }
    }, false, 'updateCache');
  },
  
  isCacheValid: (key, customExpiry = null) => {
    const { _cache } = get();
    const expiry = customExpiry || _cache.expiry;
    const lastFetch = _cache.lastFetch;
    return lastFetch && (Date.now() - lastFetch < expiry);
  },
  
  clearCache: () => {
    set({
      _cache: {
        lastFetch: null,
        expiry: 5 * 60 * 1000,
      }
    }, false, 'clearCache');
  },
});

// 4. Standard Action Pattern
const createAsyncAction = (actionName, asyncFn) => async (...args) => {
  const { setLoading, setError, clearError } = get();
  
  setLoading(true);
  clearError();
  
  try {
    const result = await asyncFn(...args);
    setLoading(false);
    return { success: true, data: result };
  } catch (error) {
    console.error(`${actionName} error:`, error);
    setError(error.message);
    setLoading(false);
    return { success: false, error: error.message };
  }
};

// 5. Store Creation Pattern
const createStandardStore = (storeName, initialState, actions, options = {}) => {
  const {
    persist: shouldPersist = false,
    persistConfig = {},
  } = options;

  const storeConfig = (set, get) => ({
    ...createInitialState(initialState),
    ...createLoadingHelpers(set),
    ...createCacheHelpers(set, get),
    ...actions(set, get),
    
    // Standard reset method
    resetStore: () => {
      set(createInitialState(initialState), false, 'resetStore');
    },
  });

  if (shouldPersist) {
    return create(
      devtools(
        persist(storeConfig, {
          name: `${storeName}-storage`,
          ...persistConfig,
        }),
        { name: storeName }
      )
    );
  }

  return create(
    devtools(storeConfig, { name: storeName })
  );
};

// === NAMING CONVENTIONS ===

/**
 * Standardized Naming Conventions:
 * 
 * State Properties:
 * - loading: boolean (main loading state)
 * - error: string | null (main error state)
 * - _cache: object (cache management)
 * 
 * Actions:
 * - fetch[Entity]: async (fetch data)
 * - create[Entity]: async (create new)
 * - update[Entity]: async (update existing)
 * - delete[Entity]: async (delete)
 * - set[Property]: sync (set state)
 * - clear[Property]: sync (clear state)
 * - resetStore: sync (reset all state)
 * 
 * Action Naming Pattern:
 * - [verb][Entity][Context?]
 * - Examples: fetchJobs, createJob, updateJobStatus, deleteJob
 * 
 * Loading States (for complex stores):
 * - [entity]Loading: boolean
 * - Examples: jobsLoading, applicationsLoading
 * 
 * Error States (for complex stores):
 * - [entity]Error: string | null
 * - Examples: jobsError, applicationsError
 */

export {
  createInitialState,
  createLoadingHelpers,
  createCacheHelpers,
  createAsyncAction,
  createStandardStore,
};
