import { Typography } from 'antd';
import { motion } from 'framer-motion';

const { Text, Title } = Typography;

const AppLoader = ({ message = 'Loading Flyt...' }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 text-white">
      <div className="text-center">
        {/* Loading text with typewriter effect */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="mb-4"
        >
          <Title
            level={3}
            className="text-white mb-2"
          >
            {message}
          </Title>
        </motion.div>
      </div>
    </div>
  );
};

export default AppLoader;
