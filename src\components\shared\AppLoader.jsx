import { Typography } from 'antd';
import { motion } from 'framer-motion';

const { Text, Title } = Typography;

const AppLoader = ({ message = 'Loading Flyt...' }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center">
        {/* Animated Logo/Brand */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="relative">
            {/* Main circle with pulse effect */}
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              className="w-20 h-20 mx-auto bg-primary rounded-full flex items-center justify-center shadow-lg"
            >
              <Title
                level={2}
                className="text-white m-0 font-bold"
              >
                F
              </Title>
            </motion.div>

            {/* Orbiting dots */}
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                animate={{
                  rotate: 360,
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'linear',
                  delay: index * 0.5,
                }}
                className="absolute inset-0 w-20 h-20"
                style={{
                  transformOrigin: '50% 50%',
                }}
              >
                <div
                  className="w-3 h-3 bg-primary rounded-full absolute"
                  style={{
                    top: '10%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    opacity: 0.6 + index * 0.2,
                  }}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Loading text with typewriter effect */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="mb-4"
        >
          <Title
            level={3}
            className="text-gray-800 dark:text-gray-200 mb-2"
          >
            {message}
          </Title>
        </motion.div>

        {/* Progress dots */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="flex justify-center space-x-2"
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 1, 0.3],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2,
                ease: 'easeInOut',
              }}
              className="w-2 h-2 bg-primary rounded-full"
            />
          ))}
        </motion.div>

        {/* Subtitle */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
          className="mt-6"
        >
          <Text className="text-gray-600 dark:text-gray-400 text-sm">
            Preparing your interview platform...
          </Text>
        </motion.div>
      </div>
    </div>
  );
};

export default AppLoader;
