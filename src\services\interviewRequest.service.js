/**
 * Interview Request Service
 *
 * Handles interview request operations including:
 * - Creating interview requests (candidates)
 * - Accepting/declining requests (interviewers)
 * - Calendar integration
 * - Real-time updates
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Create a new interview request (Candidate)
 * @param {Object} requestData - Interview request data
 * @returns {Promise<Object>} - Result with success/error
 */
export const createInterviewRequest = async (requestData) => {
  try {
    // Get candidate profile data
    const { data: candidateProfile, error: candidateError } = await supabase
      .from('candidate_profiles')
      .select('full_name, email, phone, title, years_of_experience, avatar_url')
      .eq('id', requestData.candidate_id)
      .single();

    if (candidateError) throw candidateError;

    // Get job and company data
    const { data: jobData, error: jobError } = await supabase
      .from('jobs')
      .select(
        `
        id,
        title,
        experience_level,
        required_skills,
        company_profiles:company_id (
          id,
          company_name,
          logo_url
        )
      `
      )
      .eq('id', requestData.job_id)
      .single();

    if (jobError) throw jobError;

    // Find available interviewer based on job requirements
    const { data: availableInterviewers, error: interviewerError } = await supabase
      .from('interviewer_profiles')
      .select('id, full_name, specializations, availability_status')
      .eq('availability_status', 'available')
      .limit(1);

    if (interviewerError) throw interviewerError;

    if (!availableInterviewers || availableInterviewers.length === 0) {
      throw new Error('No available interviewers found');
    }

    const interviewer = availableInterviewers[0];

    // Create interview request with enriched data
    const enrichedRequestData = {
      ...requestData,
      interviewer_id: interviewer.id,
      status: 'requested',
      // Candidate data
      candidate_name: candidateProfile.full_name,
      candidate_email: candidateProfile.email,
      candidate_photo: candidateProfile.avatar_url,
      current_job_title: candidateProfile.title,
      years_experience: candidateProfile.years_of_experience,
      // Job data
      job_title: jobData.title,
      experience_level: jobData.experience_level,
      required_skills: jobData.required_skills,
      // Company data
      company_name: jobData.company_profiles.company_name,
      company_logo_url: jobData.company_profiles.logo_url,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('interviews')
      .insert([enrichedRequestData])
      .select()
      .single();

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error('Error creating interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview requests for interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Interview requests
 */
export const getInterviewerRequests = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select('*')
      .eq('interviewer_id', interviewerId)
      .eq('status', 'requested')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching interview requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview requests for candidate
 * @param {string} candidateId - Candidate ID
 * @returns {Promise<Object>} - Interview requests
 */
export const getCandidateRequests = async (candidateId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select('*')
      .eq('candidate_id', candidateId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching candidate requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Accept interview request (Interviewer)
 * @param {string} requestId - Request ID
 * @param {Object} acceptanceData - Acceptance data
 * @returns {Promise<Object>} - Result
 */
export const acceptInterviewRequest = async (requestId, acceptanceData = {}) => {
  try {
    // Get the interview request
    const { data: request, error: requestError } = await supabase
      .from('interviews')
      .select('*')
      .eq('id', requestId)
      .single();

    if (requestError) throw requestError;

    // Update interview request status
    const { error: updateError } = await supabase
      .from('interviews')
      .update({
        status: 'scheduled',
        accepted_at: new Date().toISOString(),
        interview_date: request.preferred_date,
        updated_at: new Date().toISOString(),
        ...acceptanceData,
      })
      .eq('id', requestId);

    if (updateError) throw updateError;

    // Create calendar event for interviewer
    const interviewDate = new Date(request.preferred_date);
    const { error: calendarError } = await supabase.from('calendar_events').insert([
      {
        user_id: request.interviewer_id,
        title: `Interview: ${request.candidate_name}`,
        description: `Interview for ${request.job_title} position at ${request.company_name}`,
        date: interviewDate.toISOString().split('T')[0],
        time: interviewDate.toTimeString().split(' ')[0],
        duration: request.duration_minutes || 60,
        type: 'interview',
        is_online: request.interview_type === 'video',
        participants: [request.candidate_email],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ]);

    if (calendarError) throw calendarError;

    // Create calendar event for candidate
    const { error: candidateCalendarError } = await supabase.from('calendar_events').insert([
      {
        user_id: request.candidate_id,
        title: `Interview: ${request.job_title}`,
        description: `Interview with ${request.company_name}`,
        date: interviewDate.toISOString().split('T')[0],
        time: interviewDate.toTimeString().split(' ')[0],
        duration: request.duration_minutes || 60,
        type: 'interview',
        is_online: request.interview_type === 'video',
        participants: [request.candidate_email],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ]);

    if (candidateCalendarError) throw candidateCalendarError;

    return { success: true, data: request };
  } catch (error) {
    console.error('Error accepting interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Decline interview request (Interviewer)
 * @param {string} requestId - Request ID
 * @param {string} reason - Decline reason
 * @returns {Promise<Object>} - Result
 */
export const declineInterviewRequest = async (requestId, reason = '') => {
  try {
    const { error } = await supabase
      .from('interviews')
      .update({
        status: 'declined',
        declined_at: new Date().toISOString(),
        decline_reason: reason,
        updated_at: new Date().toISOString(),
      })
      .eq('id', requestId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error declining interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get calendar events for user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Calendar events
 */
export const getCalendarEvents = async (userId, _userType) => {
  try {
    const { data, error } = await supabase
      .from('calendar_events')
      .select('*')
      .eq('user_id', userId)
      .order('date', { ascending: true });

    if (error) throw error;

    // Transform events for calendar display
    const transformedEvents = data.map((event) => ({
      id: event.id,
      title: event.title,
      description: event.description,
      date: event.date,
      time: event.time,
      duration: event.duration,
      type: event.type,
      location: event.location,
      isOnline: event.is_online,
      meetingLink: event.meeting_link,
      participants: event.participants || [],
    }));

    return { success: true, data: transformedEvents };
  } catch (error) {
    console.error('Error fetching calendar events:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview requests for an interviewer with detailed information
 * @param {string} interviewerId - Interviewer ID
 * @param {string} status - Request status filter (optional)
 * @returns {Promise<Object>} Response with interview requests
 */
export const getInterviewRequests = async (interviewerId, status = null) => {
  try {
    let query = supabase
      .from('interviews')
      .select(
        `
        *,
        candidate_profiles:candidate_id (
          id,
          full_name,
          email,
          phone,
          title,
          years_of_experience,
          location,
          skills,
          avatar_url,
          rating
        ),
        jobs:job_id (
          id,
          title,
          experience_level,
          required_skills,
          company_profiles:company_id (
            id,
            company_name,
            logo_url
          )
        )
      `
      )
      .eq('interviewer_id', interviewerId)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) throw error;

    // Transform data to match expected format
    const transformedData =
      data?.map((request) => ({
        id: request.id,
        candidateId: request.candidate_id,
        interviewerId: request.interviewer_id,
        jobId: request.job_id,
        companyId: request.company_id,
        status: request.status,
        score: request.score,
        feedback: request.feedback,
        interviewDate: request.interview_date,
        preferredDate: request.preferred_date,
        interviewType: request.interview_type,
        durationMinutes: request.duration_minutes || 60,
        message: request.message,
        acceptedAt: request.accepted_at,
        declinedAt: request.declined_at,
        declineReason: request.decline_reason,
        createdAt: request.created_at,
        updatedAt: request.updated_at,
        // Candidate info
        candidateName: request.candidate_name || request.candidate_profiles?.full_name,
        candidateEmail: request.candidate_email || request.candidate_profiles?.email,
        candidatePhoto: request.candidate_photo || request.candidate_profiles?.avatar_url,
        candidateTitle: request.current_job_title || request.candidate_profiles?.title,
        candidateExperience:
          request.years_experience || request.candidate_profiles?.years_of_experience,
        candidateLocation: request.candidate_profiles?.location,
        candidateSkills: request.candidate_profiles?.skills || [],
        candidateRating: request.candidate_profiles?.rating,
        candidatePhone: request.candidate_profiles?.phone,
        // Job info
        jobTitle: request.job_title || request.jobs?.title,
        experienceLevel: request.experience_level || request.jobs?.experience_level,
        requiredSkills: request.required_skills || request.jobs?.required_skills || [],
        // Company info
        companyName: request.company_name || request.jobs?.company_profiles?.company_name,
        companyLogo: request.company_logo_url || request.jobs?.company_profiles?.logo_url,
      })) || [];

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    console.error('Error fetching interview requests:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

export default {
  createInterviewRequest,
  getInterviewerRequests,
  getCandidateRequests,
  acceptInterviewRequest,
  declineInterviewRequest,
  getCalendarEvents,
  getInterviewRequests,
};
