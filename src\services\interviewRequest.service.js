/**
 * Interview Request Service
 *
 * Handles interview request operations including:
 * - Creating interview requests (candidates)
 * - Accepting/declining requests (interviewers)
 * - Calendar integration
 * - Real-time updates
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Create a new interview request (Disabled - candidate functionality removed)
 * @param {Object} requestData - Interview request data
 * @returns {Promise<Object>} - Result with success/error
 */
export const createInterviewRequest = async (requestData) => {
  try {
    // Candidate functionality has been removed
    return { success: false, error: 'Candidate functionality is not available' };
  } catch (error) {
    console.error('Error creating interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview requests for interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Interview requests
 */
export const getInterviewerRequests = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select('*')
      .eq('interviewer_id', interviewerId)
      .eq('status', 'requested')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching interview requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview requests for candidate (disabled - candidate functionality removed)
 * @param {string} candidateId - Candidate ID
 * @returns {Promise<Object>} - Interview requests
 */
export const getCandidateRequests = async (candidateId) => {
  try {
    // Candidate functionality has been removed
    return { success: true, data: [] };
  } catch (error) {
    console.error('Error fetching candidate requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Accept interview request (Interviewer)
 * @param {string} requestId - Request ID
 * @param {Object} acceptanceData - Acceptance data
 * @returns {Promise<Object>} - Result
 */
export const acceptInterviewRequest = async (requestId, acceptanceData = {}) => {
  try {
    // Get the interview request
    const { data: request, error: requestError } = await supabase
      .from('interviews')
      .select('*')
      .eq('id', requestId)
      .single();

    if (requestError) throw requestError;

    // Update interview request status
    const { error: updateError } = await supabase
      .from('interviews')
      .update({
        status: 'scheduled',
        accepted_at: new Date().toISOString(),
        interview_date: request.preferred_date,
        updated_at: new Date().toISOString(),
        ...acceptanceData,
      })
      .eq('id', requestId);

    if (updateError) throw updateError;

    // Create calendar event for interviewer
    const interviewDate = new Date(request.preferred_date);
    const { error: calendarError } = await supabase.from('calendar_events').insert([
      {
        user_id: request.interviewer_id,
        title: `Interview: ${request.candidate_name}`,
        description: `Interview for ${request.job_title} position at ${request.company_name}`,
        date: interviewDate.toISOString().split('T')[0],
        time: interviewDate.toTimeString().split(' ')[0],
        duration: request.duration_minutes || 60,
        type: 'interview',
        is_online: request.interview_type === 'video',
        participants: [request.candidate_email],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ]);

    if (calendarError) throw calendarError;

    // Create calendar event for candidate
    const { error: candidateCalendarError } = await supabase.from('calendar_events').insert([
      {
        user_id: request.candidate_id,
        title: `Interview: ${request.job_title}`,
        description: `Interview with ${request.company_name}`,
        date: interviewDate.toISOString().split('T')[0],
        time: interviewDate.toTimeString().split(' ')[0],
        duration: request.duration_minutes || 60,
        type: 'interview',
        is_online: request.interview_type === 'video',
        participants: [request.candidate_email],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ]);

    if (candidateCalendarError) throw candidateCalendarError;

    return { success: true, data: request };
  } catch (error) {
    console.error('Error accepting interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Decline interview request (Interviewer)
 * @param {string} requestId - Request ID
 * @param {string} reason - Decline reason
 * @returns {Promise<Object>} - Result
 */
export const declineInterviewRequest = async (requestId, reason = '') => {
  try {
    const { error } = await supabase
      .from('interviews')
      .update({
        status: 'declined',
        declined_at: new Date().toISOString(),
        decline_reason: reason,
        updated_at: new Date().toISOString(),
      })
      .eq('id', requestId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error declining interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get calendar events for user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Calendar events
 */
export const getCalendarEvents = async (userId, _userType) => {
  try {
    const { data, error } = await supabase
      .from('calendar_events')
      .select('*')
      .eq('user_id', userId)
      .order('date', { ascending: true });

    if (error) throw error;

    // Transform events for calendar display
    const transformedEvents = data.map((event) => ({
      id: event.id,
      title: event.title,
      description: event.description,
      date: event.date,
      time: event.time,
      duration: event.duration,
      type: event.type,
      location: event.location,
      isOnline: event.is_online,
      meetingLink: event.meeting_link,
      participants: event.participants || [],
    }));

    return { success: true, data: transformedEvents };
  } catch (error) {
    console.error('Error fetching calendar events:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interview requests for an interviewer with detailed information
 * @param {string} interviewerId - Interviewer ID
 * @param {string} status - Request status filter (optional)
 * @returns {Promise<Object>} Response with interview requests (disabled - candidate functionality removed)
 */
export const getInterviewRequests = async (interviewerId, status = null) => {
  try {
    // Candidate functionality has been removed
    return {
      success: true,
      data: [],
    };
  } catch (error) {
    console.error('Error fetching interview requests:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

export default {
  createInterviewRequest,
  getInterviewerRequests,
  getCandidateRequests,
  acceptInterviewRequest,
  declineInterviewRequest,
  getCalendarEvents,
  getInterviewRequests,
};
