/**
 * Shared component for rendering Lucide icons with consistent styling
 */
import React from 'react';

/**
 * Lucide icon wrapper component
 * @param {Object} props
 * @param {React.Component} props.icon - Lucide icon component
 * @param {string} props.color - Icon color
 * @param {number} props.size - Icon size (default: 18)
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element}
 */
const LucideIcon = ({ icon: Icon, color, size = 18, className = '' }) => (
  <span
    className={`anticon ${className}`}
    style={{ color }}
  >
    <Icon size={size} />
  </span>
);

export default LucideIcon;
