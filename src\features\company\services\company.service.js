/**
 * Company Service - OPTIMIZED VERSION
 *
 * This service handles all API calls related to companies.
 * Updated to work with the new optimized database structure.
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Get a complete company profile by ID (uses optimized view)
 * @param {string} id - Company ID
 * @returns {Promise<Object>} - Complete company profile
 */
export const getCompanyProfile = async (id) => {
  try {
    // Use the optimized view for complete profile data
    const { data, error } = await supabase
      .from('company_profiles_complete')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching company profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create or update a company profile (optimized two-step process)
 * @param {Object} profileData - Complete company profile data
 * @returns {Promise<Object>} - Result of the operation
 */
export const upsertCompanyProfile = async (profileData) => {
  console.log('Upserting company profile:', profileData);
  try {
    // Step 1: Upsert profiles table (email, phone, role)
    const { error: profileError } = await supabase.from('profiles').upsert({
      id: profileData.id,
      email: profileData.email,
      phone_number: profileData.phone_number,
      role: 'company',
      username: profileData.username || profileData.company_name,
      updated_at: new Date(),
    });

    if (profileError) throw profileError;

    // Step 2: Upsert company-specific data (excluding email/phone)
    const companyData = { ...profileData };
    delete companyData.email;
    delete companyData.phone_number;
    delete companyData.role;
    delete companyData.username;

    const { data, error } = await supabase.from('company_profiles').upsert(companyData).select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error upserting company profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update a company profile (optimized two-step process)
 * @param {string} id - Company ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object>} - Result of the operation
 */
export const updateCompanyProfile = async (id, updates) => {
  try {
    // Step 1: Update profiles table if email/phone are included
    if (updates.email || updates.phone_number || updates.username) {
      const profileUpdates = {};
      if (updates.email) profileUpdates.email = updates.email;
      if (updates.phone_number) profileUpdates.phone_number = updates.phone_number;
      if (updates.username) profileUpdates.username = updates.username;
      profileUpdates.updated_at = new Date();

      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', id);

      if (profileError) throw profileError;
    }

    // Step 2: Update company-specific data (excluding email/phone)
    const companyUpdates = { ...updates };
    delete companyUpdates.email;
    delete companyUpdates.phone_number;
    delete companyUpdates.username;
    delete companyUpdates.role;
    companyUpdates.updated_at = new Date();

    const { data, error } = await supabase
      .from('company_profiles')
      .update(companyUpdates)
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating company profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Upload a company logo
 * @param {string} companyId - Company ID
 * @param {File} file - Logo file
 * @returns {Promise<Object>} - Result of the operation
 */
export const uploadCompanyLogo = async (companyId, file) => {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${companyId}/logo-${Date.now()}.${fileExt}`;

    // Using the correct bucket name 'company_logos' instead of 'company-documents'
    const { data, error } = await supabase.storage.from('company_logos').upload(fileName, file, {
      cacheControl: '3600',
      upsert: true,
    });

    if (error) throw error;

    // Get the public URL
    const { data: urlData } = supabase.storage.from('company_logos').getPublicUrl(fileName);

    // Update the company profile with the logo URL
    await updateCompanyProfile(companyId, { company_logo_url: urlData.publicUrl });

    return { success: true, data: urlData.publicUrl };
  } catch (error) {
    console.error('Error uploading company logo:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all job postings for a company
 * @param {string} companyId - Company ID
 * @returns {Promise<Object>} - Job postings
 */
export const getCompanyJobs = async (companyId) => {
  try {
    const { data, error } = await supabase.from('jobs').select('*').eq('company_id', companyId);

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching company jobs:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all applications for a company's job postings
 * @param {string} companyId - Company ID
 * @returns {Promise<Object>} - Job applications (disabled - candidate functionality removed)
 */
export const getCompanyApplications = async (companyId) => {
  try {
    // Candidate functionality has been removed
    return { success: true, data: [] };
  } catch (error) {
    console.error('Error fetching company applications:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get shortlisted candidates for a company
 * @param {string} companyId - Company ID
 * @returns {Promise<Object>} - Shortlisted candidates (disabled - candidate functionality removed)
 */
export const getShortlistedCandidates = async (companyId) => {
  try {
    // Candidate functionality has been removed
    return { success: true, data: [] };
  } catch (error) {
    console.error('Error fetching shortlisted candidates:', error);
    return { success: false, error: error.message };
  }
};
