import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Row,
  Col,
  Card,
  Button,
  Input,
  Collapse,
  Divider,
  Space,
  Tag,
  Empty,
} from 'antd';
import {
  QuestionCircleOutlined,
  SearchOutlined,
  UserOutlined,
  TeamOutlined,
  DollarOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph, Text } = Typography;
const { Panel } = Collapse;

const Faqs = () => {
  const { isMobile } = useDeviceDetect();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  const faqCategories = [
    { key: 'all', label: 'All FAQs', icon: <QuestionCircleOutlined /> },
    { key: 'general', label: 'General', icon: <RocketOutlined /> },
    { key: 'candidates', label: 'For Candidates', icon: <UserOutlined /> },
    { key: 'companies', label: 'For Companies', icon: <TeamOutlined /> },
    { key: 'pricing', label: 'Pricing', icon: <DollarOutlined /> },
    { key: 'technical', label: 'Technical', icon: <SettingOutlined /> },
    { key: 'security', label: 'Security', icon: <SecurityScanOutlined /> },
  ];

  const faqs = [
    {
      question: 'What is your interview platform?',
      answer:
        'Our interview platform is a comprehensive solution designed to streamline the interview process for both companies and candidates. It includes features for scheduling, video interviews, collaborative feedback, and analytics.',
      category: 'general',
    },
    {
      question: 'How do I create an account?',
      answer:
        "You can create an account by clicking the 'Sign Up' button in the top right corner of our homepage. You'll be asked to provide some basic information and choose whether you're signing up as a candidate or a company.",
      category: 'general',
    },
    {
      question: 'Is the platform free for candidates?',
      answer:
        'Yes, candidates can use our platform completely free of charge. This includes creating a profile, applying to jobs, scheduling interviews, and accessing preparation resources.',
      category: 'pricing',
    },
    {
      question: 'What pricing plans do you offer for companies?',
      answer:
        'We offer several pricing tiers for companies based on team size and feature needs. Our plans start with a free tier for small teams, and scale up to enterprise solutions. Visit our Pricing page for detailed information.',
      category: 'pricing',
    },
    {
      question: 'How do I schedule an interview?',
      answer:
        'As a company, you can schedule interviews by selecting a candidate, choosing available time slots, and sending an invitation. Candidates can then select from the proposed times or suggest alternatives.',
      category: 'companies',
    },
    {
      question: 'Can I prepare for my interview on the platform?',
      answer:
        'Yes, we offer comprehensive preparation resources for candidates, including practice questions, industry-specific guides, and even mock interviews with AI or human coaches.',
      category: 'candidates',
    },
    {
      question: 'How do video interviews work?',
      answer:
        'Our platform includes built-in video conferencing that requires no downloads. At the scheduled time, both parties receive a notification and can join with one click. The system includes recording options, notes, and collaborative feedback tools.',
      category: 'technical',
    },
    {
      question: 'Is my data secure on your platform?',
      answer:
        'Absolutely. We implement industry-leading security measures including end-to-end encryption for video interviews, secure data storage, and strict access controls. We are GDPR compliant and never share your data with third parties without consent.',
      category: 'security',
    },
    {
      question: 'Can I integrate the platform with my ATS?',
      answer:
        'Yes, we offer integrations with most popular Applicant Tracking Systems. Our API also allows for custom integrations if needed. Contact our support team for specific integration questions.',
      category: 'technical',
    },
    {
      question: 'How do I provide feedback after an interview?',
      answer:
        'After an interview concludes, interviewers receive a structured feedback form to complete. This can include ratings, comments, and recommendations. Feedback can be shared with other team members for collaborative hiring decisions.',
      category: 'companies',
    },
    {
      question: 'Can I use the platform on mobile devices?',
      answer:
        'Yes, our platform is fully responsive and works on all devices. We also offer mobile apps for iOS and Android for an optimized experience on smartphones and tablets.',
      category: 'technical',
    },
    {
      question: 'How do I get help if I have issues?',
      answer:
        'We offer multiple support channels including live chat, email support, and an extensive knowledge base. Premium plans include dedicated account managers and priority support.',
      category: 'general',
    },
  ];

  const filteredFaqs = faqs.filter((faq) => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="faqs-page max-w-5xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-12 mb-8">
        <Title
          level={1}
          className="mb-4"
        >
          Frequently Asked Questions
        </Title>
        <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
          Find answers to common questions about our interview platform. Can't find what you're
          looking for? Contact our support team.
        </Paragraph>
      </div>

      {/* Search and Filter */}
      <Card
        className="search-filter-card mb-8"
        bordered={false}
      >
        <Input
          size="large"
          placeholder="Search for questions..."
          prefix={<SearchOutlined />}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="mb-6"
        />

        <div className="categories-filter">
          <Space
            size={[8, 16]}
            wrap
          >
            {faqCategories.map((category) => (
              <Tag
                key={category.key}
                icon={category.icon}
                color={activeCategory === category.key ? 'primary' : 'default'}
                style={{
                  cursor: 'pointer',
                  padding: '6px 12px',
                  fontSize: '14px',
                }}
                onClick={() => setActiveCategory(category.key)}
              >
                {category.label}
              </Tag>
            ))}
          </Space>
        </div>
      </Card>

      {/* FAQ Collapse */}
      <div className="faq-collapse mb-16">
        {filteredFaqs.length > 0 ? (
          <Collapse accordion>
            {filteredFaqs.map((faq) => (
              <Panel
                header={faq.question}
                key={faq.id}
              >
                <p>{faq.answer}</p>
              </Panel>
            ))}
          </Collapse>
        ) : (
          <Empty description="No FAQs found" />
        )}
      </div>
    </div>
  );
};

export default Faqs;
