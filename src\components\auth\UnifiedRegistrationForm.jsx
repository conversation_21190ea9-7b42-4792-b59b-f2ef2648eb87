import { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { UserOutlined, MailOutlined, LockOutlined, PhoneOutlined } from '@ant-design/icons';
import { checkUniquePhoneNumber, checkUniqueEmail } from '@/utils/validation';
import { RegistrationService } from '@/services/registrationService';

const UnifiedRegistrationForm = ({ role, onSuccess, loading }) => {
  const [form] = Form.useForm();
  const [formLoading, setFormLoading] = useState(false);

  const handleIncompleteRegistration = async (email, formValues) => {
    try {
      await RegistrationService.cleanupIncompleteRegistration(email);
      message.success('Previous registration removed. Proceeding with fresh registration...');
      await proceedWithRegistration(formValues);
    } catch (error) {
      message.error('Failed to cleanup previous registration: ' + error.message);
      throw error;
    }
  };

  const proceedWithRegistration = async (values) => {
    // Check if phone number is unique
    const phoneResult = await checkUniquePhoneNumber(values.phoneNumber, role);

    if (!phoneResult.isUnique) {
      throw new Error(phoneResult.message || 'This phone number is already registered');
    }

    // Prepare and submit registration data
    const registrationData = {
      fullName: values.fullName,
      email: values.email,
      phoneNumber: values.phoneNumber,
      password: values.password,
      role: role,
    };

    if (onSuccess) {
      await onSuccess(registrationData);
    }
  };

  const handleSubmit = async (values) => {
    setFormLoading(true);
    try {
      // Validate password confirmation
      if (values.password !== values.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Check if email is unique
      const emailResult = await checkUniqueEmail(values.email);

      if (!emailResult.isUnique) {
        if (emailResult.hasIncompleteRegistration) {
          await handleIncompleteRegistration(values.email, values);
          return;
        } else {
          throw new Error(emailResult.message || 'This email address is already registered');
        }
      }

      // If email is unique, proceed with registration
      await proceedWithRegistration(values);
    } catch (error) {
      message.error(error.message || 'Registration failed');
    } finally {
      setFormLoading(false);
    }
  };

  const getRoleDisplayName = (role) => {
    switch (role) {
      case 'candidate':
        return 'Candidate';
      case 'interviewer':
        return 'Recruiter';
      case 'company':
        return 'Company';
      default:
        return 'User';
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      requiredMark={false}
      className="w-full"
    >
      <Form.Item
        name="fullName"
        rules={[
          { required: true, message: 'Please enter your full name' },
          { min: 2, message: 'Name must be at least 2 characters' },
        ]}
      >
        <Input
          prefix={<UserOutlined className="text-text-secondary" />}
          placeholder="Full name"
          size="large"
          className="rounded-lg h-12"
        />
      </Form.Item>

      <Form.Item
        name="email"
        rules={[
          { required: true, message: 'Please enter your email' },
          { type: 'email', message: 'Please enter a valid email' },
        ]}
      >
        <Input
          prefix={<MailOutlined className="text-text-secondary" />}
          placeholder="Email address"
          size="large"
          className="rounded-lg h-12"
        />
      </Form.Item>

      <Form.Item
        name="phoneNumber"
        rules={[
          { required: true, message: 'Please enter your phone number' },
          { pattern: /^[0-9]{10}$/, message: 'Please enter a valid 10-digit phone number' },
        ]}
      >
        <Input
          prefix={<PhoneOutlined className="text-text-secondary" />}
          placeholder="Phone number"
          size="large"
          className="rounded-lg h-12"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: 'Please enter a password' },
          { min: 8, message: 'Password must be at least 8 characters' },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
            message: 'Password must contain uppercase, lowercase, number and special character',
          },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined className="text-text-secondary" />}
          placeholder="Create a password"
          size="large"
          className="rounded-lg h-12"
        />
      </Form.Item>

      <Form.Item
        name="confirmPassword"
        dependencies={['password']}
        rules={[
          { required: true, message: 'Please confirm your password' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('Passwords do not match'));
            },
          }),
        ]}
      >
        <Input.Password
          prefix={<LockOutlined className="text-text-secondary" />}
          placeholder="Confirm password"
          size="large"
          className="rounded-lg h-12"
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          size="large"
          block
          loading={formLoading || loading}
          className="h-12 rounded-lg font-medium"
        >
          Create {getRoleDisplayName(role)} Account
        </Button>
      </Form.Item>
    </Form>
  );
};

export default UnifiedRegistrationForm;
