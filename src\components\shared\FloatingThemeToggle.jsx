import React from 'react';
import { Button, Tooltip } from 'antd';
import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { useColorModeStore } from '@/store/colorMode.store';

const FloatingThemeToggle = ({ position = 'bottom-right' }) => {
  const { colorMode, toggleColorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <Tooltip
        title={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
        placement="left"
      >
        <Button
          type="primary"
          shape="circle"
          size="large"
          icon={isDark ? <SunOutlined /> : <MoonOutlined />}
          onClick={toggleColorMode}
          className="shadow-lg hover:shadow-xl"
          style={{
            backgroundColor: isDark ? '#f0f4ff' : '#2c2c2c',
            color: isDark ? '#121212' : '#ffffff',
            border: 'none',
          }}
        />
      </Tooltip>
    </div>
  );
};

export default FloatingThemeToggle;
