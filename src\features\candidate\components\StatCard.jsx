import React from 'react';
import { Card, Statistic, Skeleton } from 'antd';

/**
 * StatCard component for displaying dashboard statistics
 */
const StatCard = ({ 
  title, 
  value, 
  icon, 
  loading = false, 
  prefix = null,
  suffix = null,
  valueStyle = {},
  className = '',
  onClick = null
}) => {
  if (loading) {
    return (
      <Card className={`h-full ${className}`}>
        <Skeleton active paragraph={{ rows: 2 }} />
      </Card>
    );
  }

  const cardProps = {
    className: `h-full transition-all duration-200 ${onClick ? 'cursor-pointer hover:shadow-md' : ''} ${className}`,
    ...(onClick && { onClick })
  };

  return (
    <Card {...cardProps}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <Statistic
            title={title}
            value={value}
            prefix={prefix}
            suffix={suffix}
            valueStyle={valueStyle}
          />
        </div>
        {icon && (
          <div className="ml-4 text-2xl text-gray-400">
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
};

export default StatCard;
