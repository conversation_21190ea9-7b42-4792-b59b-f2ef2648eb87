-- Safe SQL script to remove candidate-related data from Supabase
-- This script is more conservative and preserves data integrity
-- Run this script in your Supabase SQL editor

BEGIN;

-- 1. First, let's check what candidate-related tables exist
-- (Run these queries first to see what needs to be cleaned up)

-- Check for candidate profiles
-- SELECT COUNT(*) as candidate_count FROM candidate_profiles;

-- Check for candidate-related data in other tables
-- SELECT COUNT(*) as candidate_interviews FROM interviews WHERE candidate_id IS NOT NULL;
-- SELECT COUNT(*) as candidate_applications FROM applications WHERE candidate_id IS NOT NULL;

-- 2. Update interviews table to handle candidate removal
-- Instead of deleting interviews, we'll mark them as archived or update status
UPDATE interviews 
SET 
  status = 'archived',
  notes = COALESCE(notes, '') || ' [Candidate data removed]',
  updated_at = NOW()
WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

-- 3. Update applications table similarly
UPDATE applications 
SET 
  status = 'archived',
  notes = COALESCE(notes, '') || ' [Candidate data removed]',
  updated_at = NOW()
WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

-- 4. Archive candidate-related notifications instead of deleting
UPDATE notifications 
SET 
  is_read = true,
  archived = true,
  updated_at = NOW()
WHERE user_id IN (
  SELECT id FROM candidate_profiles
) AND user_type = 'candidate';

-- 5. Archive candidate messages
UPDATE messages 
SET 
  archived = true,
  updated_at = NOW()
WHERE (sender_id IN (SELECT id FROM candidate_profiles) AND sender_type = 'candidate')
   OR (recipient_id IN (SELECT id FROM candidate_profiles) AND recipient_type = 'candidate');

-- 6. Now safely remove candidate-specific data
-- Delete from candidate-only tables
DELETE FROM job_applications WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

DELETE FROM saved_jobs WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

DELETE FROM candidate_assessments WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

-- 7. Remove candidate profiles from auth-related tables
DELETE FROM profiles WHERE role = 'candidate';

-- 8. Finally, delete candidate profiles
DELETE FROM candidate_profiles;

-- 9. Drop candidate-specific tables that are no longer needed
DROP TABLE IF EXISTS job_applications CASCADE;
DROP TABLE IF EXISTS saved_jobs CASCADE;
DROP TABLE IF EXISTS candidate_assessments CASCADE;
DROP TABLE IF EXISTS candidate_skills CASCADE;
DROP TABLE IF EXISTS candidate_experience CASCADE;
DROP TABLE IF EXISTS candidate_education CASCADE;
DROP TABLE IF EXISTS candidate_certifications CASCADE;

-- 10. Drop candidate-related views
DROP VIEW IF EXISTS candidate_profiles_complete CASCADE;

-- 11. Drop candidate-related functions
DROP FUNCTION IF EXISTS get_candidate_profile(uuid) CASCADE;
DROP FUNCTION IF EXISTS update_candidate_profile(uuid, jsonb) CASCADE;
DROP FUNCTION IF EXISTS create_candidate_profile(uuid, jsonb) CASCADE;
DROP FUNCTION IF EXISTS get_candidate_applications(uuid) CASCADE;
DROP FUNCTION IF EXISTS get_candidate_interviews(uuid) CASCADE;

-- 12. Remove candidate-related RLS policies
DROP POLICY IF EXISTS "Candidates can view own profile" ON candidate_profiles;
DROP POLICY IF EXISTS "Candidates can update own profile" ON candidate_profiles;
DROP POLICY IF EXISTS "Candidates can view own applications" ON job_applications;
DROP POLICY IF EXISTS "Candidates can create applications" ON job_applications;
DROP POLICY IF EXISTS "Candidates can view own saved jobs" ON saved_jobs;
DROP POLICY IF EXISTS "Candidates can manage saved jobs" ON saved_jobs;

-- 13. Clean up indexes
DROP INDEX IF EXISTS idx_candidate_profiles_email;
DROP INDEX IF EXISTS idx_candidate_profiles_phone;
DROP INDEX IF EXISTS idx_job_applications_candidate_id;
DROP INDEX IF EXISTS idx_saved_jobs_candidate_id;

-- 14. Update table constraints to remove candidate references
-- Remove foreign key constraints that reference candidate tables
ALTER TABLE IF EXISTS interviews 
DROP CONSTRAINT IF EXISTS interviews_candidate_id_fkey CASCADE;

ALTER TABLE IF EXISTS applications
DROP CONSTRAINT IF EXISTS applications_candidate_id_fkey CASCADE;

-- 15. Optionally remove candidate columns from remaining tables
-- (Uncomment these if you want to completely remove candidate references)

-- ALTER TABLE interviews 
-- DROP COLUMN IF EXISTS candidate_id,
-- DROP COLUMN IF EXISTS candidate_name,
-- DROP COLUMN IF EXISTS candidate_email,
-- DROP COLUMN IF EXISTS candidate_phone;

-- ALTER TABLE applications
-- DROP COLUMN IF EXISTS candidate_id;

-- 16. Update any enum types (be very careful with this)
-- This should only be done if you're sure no other parts of the system use 'candidate' role
-- ALTER TYPE user_role DROP VALUE 'candidate';

-- 17. Verification queries (run these after the cleanup)
-- SELECT 'candidate_profiles' as table_name, COUNT(*) as remaining_records FROM candidate_profiles
-- UNION ALL
-- SELECT 'profiles with candidate role', COUNT(*) FROM profiles WHERE role = 'candidate'
-- UNION ALL  
-- SELECT 'interviews with candidate_id', COUNT(*) FROM interviews WHERE candidate_id IS NOT NULL;

COMMIT;

-- Note: After running this script, you may also want to:
-- 1. Clean up any candidate-related storage buckets in Supabase Storage
-- 2. Remove candidate-related API endpoints
-- 3. Update your application code to handle the removed candidate functionality
-- 4. Test thoroughly to ensure no functionality is broken
