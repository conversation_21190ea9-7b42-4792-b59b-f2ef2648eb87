import React, { useState, useEffect } from 'react';
import {
  Table,
  Typography,
  Button,
  Avatar,
  Tag,
  Space,
  Input,
  Pagination,
  Card,
  Tooltip,
  Badge,
  Dropdown,
  Menu,
  Spin,
  Empty,
  Select,
  Form,
  Drawer,
  Radio,
  Checkbox,
  Divider,
  Popover,
} from 'antd';
import {
  FileTextOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EditOutlined,
  EyeOutlined,
  SortAscendingOutlined,
  ReloadOutlined,
  CloseOutlined,
  DownOutlined,
} from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Text } = Typography;
const { Option } = Select;

const InterviewReports = () => {
  const { isMobile, isTablet } = useDeviceDetect();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState({
    status: [],
    experience: [],
    resumeAvailable: null,
  });
  const [tempFilters, setTempFilters] = useState({
    status: [],
    experience: [],
    resumeAvailable: null,
  });
  const [sortField, setSortField] = useState('id');
  const [sortOrder, setSortOrder] = useState('ascend');
  const [tempSortField, setTempSortField] = useState('id');
  const [tempSortOrder, setTempSortOrder] = useState('ascend');
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [filterPopoverVisible, setFilterPopoverVisible] = useState(false);
  const [form] = Form.useForm();

  // Generate mock data
  const generateMockData = () => {
    const statuses = ['Completed', 'Pending'];
    const experiences = ['1+', '2+', '3+', '5+'];

    return Array(67)
      .fill()
      .map((_, index) => ({
        key: index + 1,
        id: String(index + 1).padStart(2, '0'),
        name: `Sumesh Das ${index % 3 === 0 ? 'Kumar' : index % 5 === 0 ? 'Singh' : ''}`,
        avatar: `https://randomuser.me/api/portraits/men/${(index % 30) + 1}.jpg`,
        contact: '9778642544',
        email: `sumesh${index + 1}@gmail.com`,
        experience: experiences[index % experiences.length],
        resumeAvailable: index % 3 !== 0,
        status: statuses[index % statuses.length],
        reportGenerated: index % 4 === 0,
      }));
  };

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockData = generateMockData();
      setData(mockData);
      setFilteredData(mockData);
      setPagination({
        ...pagination,
        total: mockData.length,
      });
      setLoading(false);
    }, 1000);
  }, []);

  // Apply search, filters and sorting
  useEffect(() => {
    if (data.length === 0) return;

    let result = [...data];

    // Apply search
    if (searchText) {
      result = result.filter(
        (item) =>
          item.name.toLowerCase().includes(searchText.toLowerCase()) ||
          item.email.toLowerCase().includes(searchText.toLowerCase()) ||
          item.contact.includes(searchText)
      );
    }

    // Apply filters
    if (filters.status && filters.status.length > 0) {
      result = result.filter((item) => filters.status.includes(item.status));
    }

    if (filters.experience && filters.experience.length > 0) {
      result = result.filter((item) => filters.experience.includes(item.experience));
    }

    if (filters.resumeAvailable !== null) {
      result = result.filter((item) => item.resumeAvailable === filters.resumeAvailable);
    }

    // Apply sorting
    if (sortField && sortOrder) {
      result = [...result].sort((a, b) => {
        let compareA = a[sortField];
        let compareB = b[sortField];

        // Handle string comparison
        if (typeof compareA === 'string') {
          compareA = compareA.toLowerCase();
          compareB = compareB.toLowerCase();
        }

        if (sortOrder === 'ascend') {
          return compareA > compareB ? 1 : -1;
        } else {
          return compareA < compareB ? 1 : -1;
        }
      });
    }

    setFilteredData(result);
    setPagination({
      ...pagination,
      total: result.length,
      current: 1, // Reset to first page when filters change
    });
  }, [searchText, filters, sortField, sortOrder, data]);

  // Initialize temp filters when dropdown opens
  useEffect(() => {
    if (filterPopoverVisible) {
      setTempFilters({ ...filters });
      setTempSortField(sortField);
      setTempSortOrder(sortOrder);
    }
  }, [filterPopoverVisible]);

  // Get paginated data
  const getPaginatedData = () => {
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredData.slice(startIndex, endIndex);
  };

  const handleSearch = (value) => {
    setSearchText(value);
  };

  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  const handleGenerateReport = (record) => {
    console.log('Generate report for:', record);
    // In a real app, you would call an API to generate the report
    // For demo, let's update the local state
    const updatedData = data.map((item) => {
      if (item.id === record.id) {
        return { ...item, reportGenerated: true };
      }
      return item;
    });
    setData(updatedData);
  };

  const handleEditReport = (record) => {
    console.log('Edit report for:', record);
    // Implementation for editing report
  };

  const handleFilterSubmit = () => {
    setFilters(tempFilters);
    setSortField(tempSortField);
    setSortOrder(tempSortOrder);
    setFilterPopoverVisible(false);
  };

  const handleResetFilters = () => {
    setFilters({
      status: [],
      experience: [],
      resumeAvailable: null,
    });
    setTempFilters({
      status: [],
      experience: [],
      resumeAvailable: null,
    });
    setTempSortField('id');
    setTempSortOrder('ascend');
  };

  const handleApplyFilters = () => {
    setFilters(tempFilters);
    setSortField(tempSortField);
    setSortOrder(tempSortOrder);
    setFilterPopoverVisible(false);
  };

  const handlePageChange = (page, pageSize) => {
    setPagination({
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize,
    });
  };

  // Stats calculation
  const totalInterviews = filteredData.length;
  const completedInterviews = filteredData.filter((item) => item.status === 'Completed').length;
  const pendingInterviews = filteredData.filter((item) => item.status === 'Pending').length;
  const reportsGenerated = filteredData.filter((item) => item.reportGenerated).length;

  const columns = [
    {
      title: 'Sl. no.',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center',
      render: (id) => <Text className="text-gray-500">{id}</Text>,
    },
    {
      title: "Candidate's Name",
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (name, record) => (
        <div className="flex items-center">
          <Avatar
            src={record.avatar}
            size={36}
            style={{ marginRight: '8px' }}
          />
          <Text strong>{name}</Text>
        </div>
      ),
    },
    {
      title: 'Contact',
      dataIndex: 'contact',
      align: 'center',
      key: 'contact',
      render: (contact) => (
        <div className="flex items-center">
          <PhoneOutlined className="mr-2 text-blue-500" />
          <Text>{contact}</Text>
        </div>
      ),
      responsive: ['md'],
    },
    {
      title: 'Email ID',
      dataIndex: 'email',
      align: 'center',
      key: 'email',
      render: (email) => (
        <div className="flex items-center">
          <MailOutlined className="mr-2 text-blue-500" />
          <Text>{email}</Text>
        </div>
      ),
      responsive: ['lg'],
    },
    {
      title: 'Experience in Year',
      dataIndex: 'experience',
      key: 'experience',
      width: 120,
      align: 'center',
      render: (exp) => (
        <div className="flex justify-center">
          <Tag
            color="blue"
            className="rounded-full"
          >
            {exp} Years
          </Tag>
        </div>
      ),
      responsive: ['md'],
    },
    {
      title: 'Resume',
      dataIndex: 'resumeAvailable',
      key: 'resume',
      width: 80,
      align: 'center',
      render: (available) => (
        <div className="flex justify-center">
          <Tooltip title={available ? 'View Resume' : 'No Resume Available'}>
            <Button
              type="text"
              shape="circle"
              icon={<FileTextOutlined style={{ fontSize: '20px' }} />}
              className={available ? 'text-green-500' : 'text-gray-300'}
              disabled={!available}
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: 'Interview Status',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      align: 'center',
      render: (status) => (
        <div className="flex justify-center">
          <Tag
            color={status === 'Completed' ? 'success' : 'default'}
            className=" px-3 py-1 rounded-full"
            icon={status === 'Completed' ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
          >
            {status}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Report',
      key: 'report',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <div className="flex justify-center">
          {record.reportGenerated ? (
            <Button
              type="primary"
              className="bg-blue-500 hover:bg-blue-600"
              icon={<EditOutlined />}
              onClick={() => handleEditReport(record)}
            >
              Edit Report
            </Button>
          ) : (
            <Button
              type={record.status === 'Completed' ? 'primary' : 'default'}
              className={record.status === 'Completed' ? 'bg-yellow-500 hover:bg-yellow-600' : ''}
              icon={<ReloadOutlined />}
              disabled={record.status !== 'Completed'}
              onClick={() => handleGenerateReport(record)}
            >
              Generate
            </Button>
          )}
        </div>
      ),
    },
  ];

  // Responsive columns for mobile
  const mobileColumns = [
    {
      title: 'Candidate',
      key: 'candidate',
      render: (_, record) => (
        <div>
          <div className="flex items-center mb-1">
            <Avatar
              src={record.avatar}
              size={36}
              className="mr-2"
            />
            <div>
              <Text
                strong
                className="block"
              >
                {record.name}
              </Text>
              <Text
                type="secondary"
                className="text-xs"
              >
                {record.email}
              </Text>
            </div>
          </div>
          <div className="flex items-center justify-between mt-2">
            <Tag
              color={record.status === 'Completed' ? 'success' : 'default'}
              className="px-2 py-0.5 text-xs rounded-full"
            >
              {record.status}
            </Tag>
            <Tag
              color="blue"
              className="px-2 py-0.5 text-xs rounded-full"
            >
              {record.experience} Years
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type={
            record.reportGenerated
              ? 'primary'
              : record.status === 'Completed'
                ? 'default'
                : 'dashed'
          }
          size="small"
          className={
            record.reportGenerated
              ? 'bg-blue-500'
              : record.status === 'Completed'
                ? 'bg-yellow-500'
                : ''
          }
          disabled={record.status !== 'Completed' && !record.reportGenerated}
          onClick={() =>
            record.reportGenerated ? handleEditReport(record) : handleGenerateReport(record)
          }
        >
          {record.reportGenerated ? 'Edit' : 'Generate'}
        </Button>
      ),
    },
  ];

  // Filter & Sort popover content
  const filterPopoverContent = (
    <div style={{ width: 300 }}>
      <div className="mb-4">
        <Text strong>Filter by Status</Text>
        <div className="mt-2">
          <Checkbox.Group
            options={[
              { label: 'Completed', value: 'Completed' },
              { label: 'Pending', value: 'Pending' },
            ]}
            value={tempFilters.status}
            onChange={(values) => setTempFilters({ ...tempFilters, status: values })}
          />
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      <div className="mb-4">
        <Text strong>Filter by Experience</Text>
        <div className="mt-2">
          <Checkbox.Group
            options={[
              { label: '1+ Years', value: '1+' },
              { label: '2+ Years', value: '2+' },
              { label: '3+ Years', value: '3+' },
              { label: '5+ Years', value: '5+' },
            ]}
            value={tempFilters.experience}
            onChange={(values) => setTempFilters({ ...tempFilters, experience: values })}
          />
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      <div className="mb-4">
        <Text strong>Filter by Resume</Text>
        <div className="mt-2">
          <Radio.Group
            value={tempFilters.resumeAvailable}
            onChange={(e) => setTempFilters({ ...tempFilters, resumeAvailable: e.target.value })}
          >
            <Space direction="vertical">
              <Radio value={null}>All Candidates</Radio>
              <Radio value={true}>Resume Available</Radio>
              <Radio value={false}>Resume Not Available</Radio>
            </Space>
          </Radio.Group>
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      <div className="mb-4">
        <Text strong>Sort by</Text>
        <div className="mt-2">
          <Radio.Group
            value={`${tempSortField}-${tempSortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setTempSortField(field);
              setTempSortOrder(order);
            }}
          >
            <Space direction="vertical">
              <Radio value="id-ascend">ID (Ascending)</Radio>
              <Radio value="id-descend">ID (Descending)</Radio>
              <Radio value="name-ascend">Name (A-Z)</Radio>
              <Radio value="name-descend">Name (Z-A)</Radio>
              <Radio value="experience-ascend">Experience (Low to High)</Radio>
              <Radio value="experience-descend">Experience (High to Low)</Radio>
            </Space>
          </Radio.Group>
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      <div className="flex justify-between mt-4">
        <Button onClick={handleResetFilters}>Reset</Button>
        <Button
          type="primary"
          onClick={handleApplyFilters}
        >
          Apply
        </Button>
      </div>
    </div>
  );

  return (
    <div className="interview-reports-page">
      <Card className="shadow-sm mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <Title
              level={2}
              className="mb-1"
            >
              Interview Reports
            </Title>
            <Text type="secondary">Manage and generate reports for all interviews</Text>
          </div>

          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Input.Search
              placeholder="Search candidates..."
              allowClear
              onSearch={handleSearch}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: isMobile ? '100%' : 220 }}
              className="mr-2"
            />

            <Popover
              content={filterPopoverContent}
              title="Filter & Sort"
              trigger="click"
              visible={filterPopoverVisible}
              onVisibleChange={setFilterPopoverVisible}
              placement="bottomRight"
              // overlayStyle={{ width: 350 }}
            >
              <Button
                icon={<FilterOutlined />}
                className="mr-2"
              >
                Filter & Sort <DownOutlined />
              </Button>
            </Popover>

            <Button
              type="primary"
              icon={<DownloadOutlined />}
              className="bg-green-600 hover:bg-green-700"
            >
              Export
            </Button>
          </div>
        </div>

        <div className="stats-summary flex flex-wrap gap-4 mb-6">
          <Card className="bg-blue-50 border-blue-200 flex-1 min-w-[150px]">
            <div className="text-center">
              <Text type="secondary">Total Interviews</Text>
              <div className="text-2xl font-bold text-blue-600">{totalInterviews}</div>
            </div>
          </Card>

          <Card className="bg-green-50 border-green-200 flex-1 min-w-[150px]">
            <div className="text-center">
              <Text type="secondary">Completed</Text>
              <div className="text-2xl font-bold text-green-600">{completedInterviews}</div>
            </div>
          </Card>

          <Card className="bg-yellow-50 border-yellow-200 flex-1 min-w-[150px]">
            <div className="text-center">
              <Text type="secondary">Pending</Text>
              <div className="text-2xl font-bold text-yellow-600">{pendingInterviews}</div>
            </div>
          </Card>

          <Card className="bg-purple-50 border-purple-200 flex-1 min-w-[150px]">
            <div className="text-center">
              <Text type="secondary">Reports Generated</Text>
              <div className="text-2xl font-bold text-purple-600">{reportsGenerated}</div>
            </div>
          </Card>
        </div>

        {/* Active filters display */}
        {(filters.status.length > 0 ||
          filters.experience.length > 0 ||
          filters.resumeAvailable !== null ||
          sortField !== 'id' ||
          sortOrder !== 'ascend') && (
          <div className="mb-4">
            <Text
              type="secondary"
              className="!text-gray-600"
            >
              Active Filters:
            </Text>
            {filters.status.length > 0 && (
              <Tag
                color="blue"
                className="!mr-2"
              >
                Status: {filters.status.join(', ')}
              </Tag>
            )}
            {filters.experience.length > 0 && (
              <Tag
                color="blue"
                className="!mr-2"
              >
                Experience: {filters.experience.join(', ')}
              </Tag>
            )}
            {filters.resumeAvailable !== null && (
              <Tag
                color="blue"
                className="!mr-2"
              >
                Resume: {filters.resumeAvailable ? 'Available' : 'Not Available'}
              </Tag>
            )}
            {sortField !== 'id' && (
              <Tag
                color="blue"
                className="!mr-2"
              >
                Sort: {sortField} ({sortOrder === 'ascend' ? 'Ascending' : 'Descending'})
              </Tag>
            )}
            <Button
              type="link"
              onClick={handleResetFilters}
              className="!text-blue-500"
            >
              Clear All Filters
            </Button>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-12">
            <Spin size="large" />
          </div>
        ) : filteredData.length > 0 ? (
          <>
            <Table
              columns={isMobile ? mobileColumns : columns}
              dataSource={getPaginatedData()}
              pagination={false}
              onChange={handleTableChange}
              rowClassName="hover:bg-gray-50"
              className="interview-reports-table"
              scroll={{ x: 'max-content' }}
            />

            <div className="flex justify-between items-center mt-4 flex-wrap">
              <Text
                type="secondary"
                className="mb-2 md:mb-0"
              >
                Showing {(pagination.current - 1) * pagination.pageSize + 1} to{' '}
                {Math.min(pagination.current * pagination.pageSize, pagination.total)} of{' '}
                {pagination.total} entries
              </Text>

              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                showSizeChanger={false}
                itemRender={(page, type, originalElement) => {
                  if (type === 'prev') {
                    return <Button size="small">← Previous</Button>;
                  }
                  if (type === 'next') {
                    return <Button size="small">Next →</Button>;
                  }
                  return originalElement;
                }}
              />
            </div>
          </>
        ) : (
          <Empty description="No interview reports found" />
        )}
      </Card>

      {/* Filter Drawer */}
      <Drawer
        title="Filter & Sort"
        placement="right"
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        width={isMobile ? '80%' : 400}
        footer={
          <div className="flex justify-end">
            <Button
              onClick={handleResetFilters}
              className="mr-2"
            >
              Reset
            </Button>
            <Button
              type="primary"
              onClick={() => form.submit()}
            >
              Apply
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={filters}
          onFinish={handleFilterSubmit}
        >
          <Form.Item
            name="status"
            label="Interview Status"
          >
            <Select
              mode="multiple"
              placeholder="Select status"
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="Completed">Completed</Option>
              <Option value="Pending">Pending</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="experience"
            label="Experience"
          >
            <Select
              mode="multiple"
              placeholder="Select experience"
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="1+">1+ Years</Option>
              <Option value="2+">2+ Years</Option>
              <Option value="3+">3+ Years</Option>
              <Option value="5+">5+ Years</Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default InterviewReports;
