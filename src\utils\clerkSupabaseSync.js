import { supabase } from '@/utils/supabaseClient';

/**
 * Sync Clerk user with Supabase database
 * This function should be called when a user signs up or their data changes
 */
export const syncClerkUserWithSupabase = async (clerkUser) => {
  try {
    if (!clerkUser) {
      throw new Error('No Clerk user provided');
    }

    // Extract user data from Clerk
    const userData = {
      id: clerkUser.id,
      email: clerkUser.primaryEmailAddress?.emailAddress,
      phone_number: clerkUser.primaryPhoneNumber?.phoneNumber || null,
      username: clerkUser.username || clerkUser.fullName || clerkUser.firstName,
      full_name: clerkUser.fullName,
      first_name: clerkUser.firstName,
      last_name: clerkUser.lastName,
      image_url: clerkUser.imageUrl,
      role: clerkUser.publicMetadata?.role || clerkUser.unsafeMetadata?.role || 'company', // Default to company
      created_at: new Date(clerkUser.createdAt),
      updated_at: new Date(),
    };

    // Check if user already exists in Supabase
    const { data: existingUser, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', clerkUser.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected for new users
      throw fetchError;
    }

    let result;
    if (existingUser) {
      // Update existing user
      const { data, error } = await supabase
        .from('profiles')
        .update({
          email: userData.email,
          phone_number: userData.phone_number,
          username: userData.username,
          full_name: userData.full_name,
          first_name: userData.first_name,
          last_name: userData.last_name,
          image_url: userData.image_url,
          role: userData.role,
          updated_at: userData.updated_at,
        })
        .eq('id', clerkUser.id)
        .select()
        .single();

      if (error) throw error;
      result = data;
    } else {
      // Create new user
      const { data, error } = await supabase
        .from('profiles')
        .insert([userData])
        .select()
        .single();

      if (error) throw error;
      result = data;

      // Create role-specific profile if needed
      await createRoleSpecificProfile(clerkUser.id, userData.role, userData);
    }

    return { success: true, data: result };
  } catch (error) {
    console.error('Error syncing Clerk user with Supabase:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create role-specific profile in Supabase
 */
const createRoleSpecificProfile = async (userId, role, userData) => {
  try {
    const roleTableMap = {
      company: 'company_profiles',
      interviewer: 'interviewer_profiles',
    };

    const tableName = roleTableMap[role];
    if (!tableName) {
      console.warn(`No specific table for role: ${role}`);
      return;
    }

    // Check if role-specific profile already exists
    const { data: existingProfile } = await supabase
      .from(tableName)
      .select('id')
      .eq('id', userId)
      .single();

    if (existingProfile) {
      // Profile already exists, no need to create
      return;
    }

    // Create role-specific profile with basic data
    const roleSpecificData = {
      id: userId,
      email: userData.email,
      phone_number: userData.phone_number,
      created_at: new Date(),
      updated_at: new Date(),
    };

    // Add role-specific fields
    if (role === 'company') {
      roleSpecificData.company_name = userData.full_name || 'New Company';
      roleSpecificData.primary_recruiter_name = userData.full_name;
    } else if (role === 'interviewer') {
      roleSpecificData.full_name = userData.full_name;
      roleSpecificData.years_experience = 0;
    }

    const { error } = await supabase
      .from(tableName)
      .insert([roleSpecificData]);

    if (error) {
      console.error(`Error creating ${role} profile:`, error);
    }
  } catch (error) {
    console.error('Error creating role-specific profile:', error);
  }
};

/**
 * Update user role in Clerk metadata and sync with Supabase
 */
export const updateUserRole = async (clerkUser, newRole) => {
  try {
    // Update role in Supabase first
    const { error: supabaseError } = await supabase
      .from('profiles')
      .update({ role: newRole, updated_at: new Date() })
      .eq('id', clerkUser.id);

    if (supabaseError) throw supabaseError;

    // Create role-specific profile if needed
    await createRoleSpecificProfile(clerkUser.id, newRole, {
      email: clerkUser.primaryEmailAddress?.emailAddress,
      phone_number: clerkUser.primaryPhoneNumber?.phoneNumber,
      full_name: clerkUser.fullName,
    });

    return { success: true };
  } catch (error) {
    console.error('Error updating user role:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete user from Supabase when deleted from Clerk
 */
export const deleteUserFromSupabase = async (userId) => {
  try {
    // Delete from role-specific tables first
    const roleTables = ['company_profiles', 'interviewer_profiles'];
    
    for (const table of roleTables) {
      await supabase.from(table).delete().eq('id', userId);
    }

    // Delete from main profiles table
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error('Error deleting user from Supabase:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get user profile from Supabase with role-specific data
 */
export const getUserProfileFromSupabase = async (userId) => {
  try {
    // Get basic profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) throw profileError;

    if (!profile) {
      return { success: false, error: 'Profile not found' };
    }

    // Get role-specific data
    const roleTableMap = {
      company: 'company_profiles',
      interviewer: 'interviewer_profiles',
    };

    const roleTable = roleTableMap[profile.role];
    if (roleTable) {
      const { data: roleData, error: roleError } = await supabase
        .from(roleTable)
        .select('*')
        .eq('id', userId)
        .single();

      if (!roleError && roleData) {
        // Merge role-specific data with profile
        Object.assign(profile, roleData);
      }
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('Error getting user profile from Supabase:', error);
    return { success: false, error: error.message };
  }
};
