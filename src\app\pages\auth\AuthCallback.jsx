import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Result, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';

const AuthCallback = () => {
  const navigate = useNavigate();
  const { user, role } = useAuth();
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get session data
        const { data, error } = await supabase.auth.getSession();

        if (error) throw error;

        if (data?.session) {
          // Get user data
          const { data: userData, error: userError } = await supabase.auth.getUser();

          if (userError) throw userError;

          if (userData?.user) {
            // Check if user has a role in metadata
            let userRole = userData.user.user_metadata?.role;

            // If no role exists, check if we need to create a profile
            if (!userRole) {
              // Create a default profile with 'candidate' role
              const { error: profileError } = await supabase.from('profiles').upsert({
                id: userData.user.id,
                username: userData.user.user_metadata?.full_name || '',
                role: 'candidate',
                created_at: new Date(),
              });

              if (profileError) throw profileError;

              userRole = 'candidate';
            }

            // Redirect to dashboard based on role
            const rolePathMap = {
              candidate: 'candidate',
              company: 'org',
              interviewer: 'sourcer',
            };
            const rolePath = rolePathMap[userRole] || userRole;
            navigate(`/${rolePath}/dashboard`);
          } else {
            throw new Error('User data not found');
          }
        } else {
          throw new Error('No session found');
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        setError(error.message);

        // Redirect to login after a delay
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  if (error) {
    return (
      <Result
        status="error"
        title="Authentication Failed"
        subTitle={error}
        extra={[<p key="redirect">Redirecting to login page...</p>]}
      />
    );
  }

  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <Spin
        indicator={
          <LoadingOutlined
            style={{ fontSize: 36 }}
            spin
          />
        }
        tip="Completing authentication..."
        size="large"
      />
    </div>
  );
};

export default AuthCallback;
