import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';

const AuthCallback = () => {
  const navigate = useNavigate();
  const { user, role, rolePath, isAuthenticated } = useAuth();

  useEffect(() => {
    // Clerk handles auth callbacks automatically
    // Just redirect to appropriate dashboard if authenticated
    if (isAuthenticated && user && (role || rolePath)) {
      const path =
        rolePath || (role === 'interviewer' ? 'sourcer' : role === 'company' ? 'org' : role);
      if (path) {
        navigate(`/${path}/dashboard`, { replace: true });
      }
    } else {
      // If not authenticated, redirect to login
      navigate('/login', { replace: true });
    }
  }, [isAuthenticated, user, role, rolePath, navigate]);

  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <Spin
        indicator={
          <LoadingOutlined
            style={{ fontSize: 36 }}
            spin
          />
        }
        tip="Completing authentication..."
        size="large"
      />
    </div>
  );
};

export default AuthCallback;
