import React from 'react';
import { Button, Typography } from 'antd';
import { Link } from 'react-router-dom';
import { useColorModeStore } from '@/store/colorMode.store';

const { Title, Paragraph } = Typography;

const NotFound = () => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  return (
    <div className="flex flex-col justify-center items-center min-h-[80vh] px-4 text-center">
      <div className="w-full max-w-md mb-8">
        <svg
          viewBox="0 0 500 400"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-auto"
        >
          <path
            d="M250 150C323.797 150 384 120.751 384 85C384 49.2487 323.797 20 250 20C176.203 20 116 49.2487 116 85C116 120.751 176.203 150 250 150Z"
            fill={isDark ? '#2C2C2C' : '#F0F4FF'}
          />
          <path
            d="M131.547 142.239L107.5 305H392.5L368.453 142.239C363.543 142.747 358.529 143 353.5 143H146.5C141.471 143 136.457 142.747 131.547 142.239Z"
            fill={isDark ? '#3A3A3A' : '#DCE6FF'}
          />
          <path
            d="M169.5 190L154.5 270M330.5 190L345.5 270"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="8"
            strokeLinecap="round"
          />
          <path
            d="M190 230H310"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="8"
            strokeLinecap="round"
          />
          <path
            d="M165 305H335"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="8"
            strokeLinecap="round"
          />
          <path
            d="M212 190V190C212 179.507 220.507 171 231 171H269C279.493 171 288 179.507 288 190V190"
            stroke={isDark ? '#60A5FA' : '#0056D2'}
            strokeWidth="8"
            strokeLinecap="round"
          />
          <text
            x="250"
            y="350"
            fontFamily="Arial"
            fontSize="120"
            fontWeight="bold"
            fill={isDark ? '#60A5FA' : '#0056D2'}
            textAnchor="middle"
          >
            404
          </text>
        </svg>
      </div>

      <Title
        level={2}
        className="mb-2"
      >
        Page Not Found
      </Title>
      <Paragraph className="text-lg mb-8 max-w-lg">
        The page you are looking for doesn't exist or has been moved. Please check the URL or
        navigate back to the homepage.
      </Paragraph>

      <div className="flex gap-4">
        <Button
          type="primary"
          size="large"
          onClick={() => window.history.back()}
        >
          Go Back
        </Button>
        <Link to="/">
          <Button size="large">Home Page</Button>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
