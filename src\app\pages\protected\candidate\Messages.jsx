import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Typography,
  Card,
  List,
  Avatar,
  Input,
  Button,
  Empty,
  Skeleton,
  Divider,
  Badge,
  Space,
  Dropdown,
  Tooltip,
  Modal,
  Popover,
  Upload,
} from 'antd';
import {
  SendOutlined,
  UserOutlined,
  SearchOutlined,
  PaperClipOutlined,
  SmileOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileOutlined,
  UploadOutlined,
  DownloadOutlined,
  DownOutlined,
} from '@ant-design/icons';
// import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import { useColorModeStore } from '@/store/colorMode.store';
import { Building, User, Check, CheckCheck, Clock } from 'lucide-react';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

const { Title, Text } = Typography;
const { Search } = Input;

const Messages = () => {
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [_messageSearchTerm, _setMessageSearchTerm] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [editingMessage, setEditingMessage] = useState(null);
  const [_isModalVisible, _setIsModalVisible] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [messageToDelete, setMessageToDelete] = useState(null);
  const [showAttachmentPreview, setShowAttachmentPreview] = useState(false);
  const [previewAttachment, setPreviewAttachment] = useState(null);

  const messagesEndRef = useRef(null);
  const messageInputRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const [showScrollButton, setShowScrollButton] = useState(false);

  const { user } = useAuth();
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Define callback functions first
  const fetchConversations = useCallback(async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // This is a placeholder - in a real app, you'd fetch from a conversations table
      // For demo purposes, we'll create mock data
      const mockConversations = [
        {
          id: '1',
          company_id: 'comp1',
          company_name: 'ABC Real Estate',
          company_logo_url: null,
          last_message: 'Thanks for your application. We would like to schedule an interview.',
          last_message_time: '2023-05-15T10:30:00',
          unread_count: 2,
        },
        {
          id: '2',
          company_id: 'comp2',
          company_name: 'XYZ Properties',
          company_logo_url: null,
          last_message: 'Your interview is scheduled for next Monday at 2 PM.',
          last_message_time: '2023-05-14T15:45:00',
          unread_count: 0,
        },
        {
          id: '3',
          company_id: 'comp3',
          company_name: 'City Homes',
          company_logo_url: null,
          last_message: 'Do you have any questions about the role?',
          last_message_time: '2023-05-13T09:15:00',
          unread_count: 1,
        },
      ];

      setConversations(mockConversations);

      // If no active conversation, set the first one
      if (!activeConversation && mockConversations.length > 0) {
        setActiveConversation(mockConversations[0]);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      setLoading(false);
    }
  }, [user, activeConversation, setConversations, setActiveConversation, setLoading]);

  const fetchMessages = useCallback(
    async (conversationId) => {
      // This is a placeholder - in a real app, you'd fetch from a messages table
      // For demo purposes, we'll create mock data
      const mockMessages = [
        {
          id: '1',
          conversation_id: '1',
          sender_id: 'comp1',
          sender_type: 'company',
          content: 'Hello! Thanks for applying to the Sales Manager position.',
          created_at: '2023-05-15T10:25:00',
        },
        {
          id: '2',
          conversation_id: '1',
          sender_id: user.id,
          sender_type: 'candidate',
          content: 'Thank you for considering my application!',
          created_at: '2023-05-15T10:27:00',
        },
        {
          id: '3',
          conversation_id: '1',
          sender_id: 'comp1',
          sender_type: 'company',
          content: 'We would like to schedule an interview with you. Are you available next week?',
          created_at: '2023-05-15T10:30:00',
        },
      ];

      setMessages(mockMessages.filter((m) => m.conversation_id === conversationId));
    },
    [user, setMessages]
  );

  // Mark conversation as read
  const markConversationAsRead = useCallback(
    (conversationId) => {
      // In a real app, you'd update the database
      setConversations(
        conversations.map((conv) =>
          conv.id === conversationId ? { ...conv, unread_count: 0 } : conv
        )
      );
    },
    [conversations, setConversations]
  );

  // Track previous message count to determine if new messages were added
  const prevMessagesLengthRef = useRef(0);

  // Handle scroll events to show/hide scroll button
  const handleScroll = useCallback((e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Show button when scrolled up more than 200px from bottom
    const isScrolledUp = scrollHeight - scrollTop - clientHeight > 200;
    setShowScrollButton(isScrolledUp);
  }, []);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messagesEndRef]);

  // Scroll to bottom of messages only when new messages are added
  useEffect(() => {
    // Only auto-scroll if messages were added (not when viewing existing messages)
    if (messages.length > prevMessagesLengthRef.current && messagesEndRef.current) {
      scrollToBottom();
    }

    // Update the previous message count
    prevMessagesLengthRef.current = messages.length;
  }, [messages, scrollToBottom]);

  // Fetch conversations when user changes
  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user, fetchConversations]);

  // Fetch messages when active conversation changes
  useEffect(() => {
    if (activeConversation) {
      fetchMessages(activeConversation.id);

      // Mark conversation as read when selected
      if (activeConversation.unread_count > 0) {
        markConversationAsRead(activeConversation.id);
      }

      // Reset scroll position when changing conversations
      setShowScrollButton(false);

      // Small delay to ensure messages are loaded before scrolling
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [activeConversation, fetchMessages, markConversationAsRead, scrollToBottom]);

  // Handle typing indicator
  const handleTyping = () => {
    // In a real app, you'd send a typing indicator to the backend
    setIsTyping(true);

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a new timeout to stop the typing indicator after 2 seconds
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 2000);
  };

  // Handle emoji selection
  const handleEmojiSelect = (emoji) => {
    setNewMessage((prev) => prev + emoji.native);
    setShowEmojiPicker(false);
    messageInputRef.current?.focus();
  };

  // Handle file upload
  const handleFileUpload = (info) => {
    if (info.file.status === 'done') {
      // In a real app, you'd upload the file to storage and get a URL
      const newAttachment = {
        id: Date.now(),
        name: info.file.name,
        type: info.file.type,
        url: URL.createObjectURL(info.file.originFileObj),
        size: info.file.size,
      };

      setAttachments([...attachments, newAttachment]);
    }
  };

  // Handle message edit
  const handleEditMessage = (messageId, newContent) => {
    setMessages(
      messages.map((msg) =>
        msg.id === messageId ? { ...msg, content: newContent, edited: true } : msg
      )
    );
    setEditingMessage(null);
  };

  // Handle message delete
  const handleDeleteMessage = (messageId) => {
    setMessages(messages.filter((msg) => msg.id !== messageId));
    setMessageToDelete(null);
  };

  // Preview attachment
  const handleAttachmentPreview = (attachment) => {
    setPreviewAttachment(attachment);
    setShowAttachmentPreview(true);
  };

  const handleSendMessage = () => {
    if ((!newMessage.trim() && attachments.length === 0) || !activeConversation) return;

    // In a real app, you'd send this to the backend
    const newMsg = {
      id: `temp-${Date.now()}`,
      conversation_id: activeConversation.id,
      sender_id: user.id,
      sender_type: 'candidate',
      content: newMessage.trim(),
      attachments: [...attachments],
      status: 'sent', // sent, delivered, read
      created_at: new Date().toISOString(),
    };

    // Update conversation with last message
    const updatedConversations = conversations.map((conv) =>
      conv.id === activeConversation.id
        ? {
            ...conv,
            last_message: newMessage.trim() || 'Attachment',
            last_message_time: new Date().toISOString(),
          }
        : conv
    );

    setMessages([...messages, newMsg]);
    setConversations(updatedConversations);
    setNewMessage('');
    setAttachments([]);

    // Simulate message being delivered after 1 second
    setTimeout(() => {
      setMessages((prev) =>
        prev.map((msg) => (msg.id === newMsg.id ? { ...msg, status: 'delivered' } : msg))
      );

      // Simulate message being read after 2 more seconds
      setTimeout(() => {
        setMessages((prev) =>
          prev.map((msg) => (msg.id === newMsg.id ? { ...msg, status: 'read' } : msg))
        );
      }, 2000);
    }, 1000);
  };

  const filteredConversations = searchTerm
    ? conversations.filter((conv) =>
        conv.company_name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : conversations;

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();

    if (date.toDateString() === today.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Group messages by date
  const groupMessagesByDate = (messages) => {
    const groups = {};

    messages.forEach((message) => {
      const date = new Date(message.created_at).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return Object.entries(groups).map(([date, messages]) => ({
      date,
      messages,
    }));
  };

  // Get message status icon
  const getMessageStatusIcon = (status) => {
    switch (status) {
      case 'sent':
        return (
          <Clock
            size={14}
            className="text-gray-400"
          />
        );
      case 'delivered':
        return (
          <Check
            size={14}
            className="text-gray-400"
          />
        );
      case 'read':
        return (
          <CheckCheck
            size={14}
            className="text-blue-500"
          />
        );
      default:
        return null;
    }
  };

  // Get file icon based on type
  const getFileIcon = (fileType) => {
    if (!fileType) {
      return <FileOutlined style={{ fontSize: 24 }} />;
    }

    if (fileType.startsWith('image/')) {
      return <FileImageOutlined style={{ fontSize: 24 }} />;
    } else if (fileType === 'application/pdf') {
      return <FilePdfOutlined style={{ fontSize: 24 }} />;
    } else {
      return <FileOutlined style={{ fontSize: 24 }} />;
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes && bytes !== 0) return 'Unknown size';
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Group messages by date for display
  const groupedMessages = groupMessagesByDate(messages);

  return (
    <div className="messages-page">
      <div className="flex justify-between items-center mb-6">
        <Title
          level={2}
          className="mb-0"
        >
          Messages
        </Title>
        <Tooltip title="Start a new conversation">
          <Button
            type="primary"
            icon={<PaperClipOutlined />}
            className="bg-primary"
          >
            New Message
          </Button>
        </Tooltip>
      </div>

      <div className="flex h-[calc(80vh-120px)] rounded-lg overflow-hidden shadow-md">
        {/* Conversations List */}
        <Card
          className="w-1/3 border-r-0 rounded-r-none"
          style={{ padding: 0, height: '100%' }}
        >
          <div className="p-4 border-b bg-gray-50 dark:bg-gray-800">
            <Search
              placeholder="Search conversations"
              prefix={<SearchOutlined className="text-gray-400" />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="rounded-full"
              allowClear
            />
          </div>

          {loading ? (
            <div className="p-4">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="mb-4"
                >
                  <Skeleton.Avatar
                    active
                    size={48}
                    className="mr-3"
                  />
                  <div className="inline-block w-3/4">
                    <Skeleton.Input
                      active
                      size="small"
                      className="mb-1"
                      style={{ width: '60%' }}
                    />
                    <Skeleton.Input
                      active
                      size="small"
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : filteredConversations.length > 0 ? (
            <div className="overflow-y-auto h-[calc(100%-64px)]">
              <List
                dataSource={filteredConversations}
                renderItem={(conversation) => (
                  <List.Item
                    className={`cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors p-4 ${
                      activeConversation?.id === conversation.id
                        ? 'bg-blue-50 dark:bg-gray-700'
                        : ''
                    }`}
                    onClick={() => setActiveConversation(conversation)}
                  >
                    <List.Item.Meta
                      avatar={
                        <Badge
                          count={conversation.unread_count}
                          size="small"
                          offset={[-5, 5]}
                          dot={conversation.unread_count > 0}
                        >
                          <Avatar
                            size={48}
                            src={conversation.company_logo_url}
                            icon={!conversation.company_logo_url && <Building size={24} />}
                            className="border border-gray-200 dark:border-gray-700"
                          />
                        </Badge>
                      }
                      title={
                        <div className="flex justify-between items-center">
                          <Text
                            strong
                            className={
                              conversation.unread_count > 0
                                ? 'text-blue-600 dark:text-blue-400'
                                : ''
                            }
                          >
                            {conversation.company_name}
                          </Text>
                          <Text
                            type="secondary"
                            className="text-xs"
                          >
                            {formatTime(conversation.last_message_time)}
                          </Text>
                        </div>
                      }
                      description={
                        <Text
                          type="secondary"
                          ellipsis={{ tooltip: conversation.last_message }}
                          className={
                            conversation.unread_count > 0
                              ? 'font-medium text-gray-800 dark:text-gray-200'
                              : ''
                          }
                        >
                          {conversation.last_message}
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          ) : (
            <Empty
              description="No conversations found"
              className="mt-10"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </Card>

        {/* Messages Area */}
        <Card
          className="w-2/3 rounded-l-none flex flex-col"
          style={{ padding: 0, height: '100%', display: 'flex', flexDirection: 'column' }}
        >
          {activeConversation ? (
            <>
              {/* Conversation Header */}
              <div className="p-4 border-b flex items-center justify-between bg-white dark:bg-gray-800">
                <div className="flex items-center">
                  <Avatar
                    size={40}
                    src={activeConversation.company_logo_url}
                    icon={!activeConversation.company_logo_url && <Building size={20} />}
                    className="border border-gray-200 dark:border-gray-700"
                  />
                  <div className="ml-3">
                    <Text strong>{activeConversation.company_name}</Text>
                    <div>
                      <Text
                        type="secondary"
                        className="text-xs"
                      >
                        {isTyping ? 'Typing...' : 'Online'}
                      </Text>
                    </div>
                  </div>
                </div>
                <div>
                  <Tooltip title="Search in conversation">
                    <Button
                      type="text"
                      icon={<SearchOutlined />}
                    />
                  </Tooltip>
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: '1',
                          label: 'Mark as unread',
                        },
                        {
                          key: '2',
                          label: 'Mute notifications',
                        },
                        {
                          key: '3',
                          label: 'Block',
                          danger: true,
                        },
                      ],
                    }}
                  >
                    <Button
                      type="text"
                      icon={<MoreOutlined />}
                    />
                  </Dropdown>
                </div>
              </div>

              {/* Messages */}
              <div
                className="flex-1 overflow-y-auto p-4 bg-gray-50 dark:bg-gray-800 relative"
                ref={messagesContainerRef}
                onScroll={handleScroll}
              >
                {showScrollButton && (
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<DownOutlined />}
                    size="large"
                    className="absolute bottom-6 right-6 z-10 shadow-lg"
                    onClick={scrollToBottom}
                  />
                )}
                {groupedMessages.map((group, groupIndex) => (
                  <div
                    key={groupIndex}
                    className="mb-6"
                  >
                    <div className="flex justify-center mb-4">
                      <div className="bg-gray-200 dark:bg-gray-700 px-3 py-1 rounded-full">
                        <Text
                          type="secondary"
                          className="text-xs"
                        >
                          {new Date(group.date).toLocaleDateString(undefined, {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })}
                        </Text>
                      </div>
                    </div>

                    {group.messages.map((message, messageIndex) => {
                      const isCandidate = message.sender_type === 'candidate';
                      const showAvatar =
                        messageIndex === 0 ||
                        group.messages[messageIndex - 1].sender_type !== message.sender_type;

                      return (
                        <div
                          key={message.id}
                          className={`mb-4 flex ${isCandidate ? 'justify-end' : 'justify-start'}`}
                        >
                          {!isCandidate && showAvatar && (
                            <Avatar
                              size={32}
                              src={activeConversation.company_logo_url}
                              icon={!activeConversation.company_logo_url && <Building size={16} />}
                              className="mr-2 mt-1"
                            />
                          )}

                          <div
                            className={`max-w-[70%] ${!isCandidate && !showAvatar ? 'ml-10' : ''}`}
                          >
                            <Dropdown
                              menu={{
                                items: [
                                  {
                                    key: '1',
                                    icon: <EditOutlined />,
                                    label: 'Edit',
                                    disabled: !isCandidate,
                                    onClick: () => setEditingMessage(message),
                                  },
                                  {
                                    key: '2',
                                    icon: <DeleteOutlined />,
                                    label: 'Delete',
                                    danger: true,
                                    disabled: !isCandidate,
                                    onClick: () => setMessageToDelete(message),
                                  },
                                ],
                              }}
                              trigger={['contextMenu']}
                            >
                              <div
                                className={`p-3 rounded-lg ${
                                  isCandidate
                                    ? 'bg-blue-500 text-white rounded-br-none'
                                    : 'bg-white dark:bg-gray-700 shadow-sm rounded-bl-none'
                                }`}
                              >
                                {message.content && (
                                  <Text className={isCandidate ? 'text-white' : ''}>
                                    {message.content}
                                    {message.edited && (
                                      <Text className="text-xs ml-1 opacity-70">(edited)</Text>
                                    )}
                                  </Text>
                                )}

                                {message.attachments && message.attachments.length > 0 && (
                                  <div className="mt-2">
                                    {message.attachments.map((attachment) => (
                                      <div
                                        key={attachment.id}
                                        className={`flex items-center p-2 rounded-md cursor-pointer ${
                                          isCandidate
                                            ? 'bg-blue-600'
                                            : 'bg-gray-100 dark:bg-gray-600'
                                        }`}
                                        onClick={() => handleAttachmentPreview(attachment)}
                                      >
                                        {getFileIcon(attachment.type)}
                                        <div className="ml-2 flex-1 overflow-hidden">
                                          <div className="truncate">{attachment.name}</div>
                                          <div className="text-xs opacity-70">
                                            {formatFileSize(attachment.size)}
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}

                                <div className="text-right mt-1 flex justify-end items-center">
                                  <Text
                                    className={`text-xs mr-1 ${
                                      isCandidate ? 'text-blue-100' : 'text-gray-400'
                                    }`}
                                  >
                                    {formatTime(message.created_at)}
                                  </Text>

                                  {isCandidate && message.status && (
                                    <span>{getMessageStatusIcon(message.status)}</span>
                                  )}
                                </div>
                              </div>
                            </Dropdown>
                          </div>

                          {isCandidate && showAvatar && (
                            <Avatar
                              size={32}
                              src={user?.user_metadata?.avatar_url}
                              icon={!user?.user_metadata?.avatar_url && <User size={16} />}
                              className="ml-2 mt-1"
                            />
                          )}
                        </div>
                      );
                    })}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="p-3 border-t bg-white dark:bg-gray-800">
                <div className="flex items-center mb-2">
                  <Tooltip title="Attach file">
                    <Upload
                      customRequest={({ onSuccess }) => {
                        setTimeout(() => {
                          onSuccess('ok');
                        }, 0);
                      }}
                      showUploadList={false}
                      onChange={handleFileUpload}
                    >
                      <Button
                        type="text"
                        icon={<PaperClipOutlined />}
                      />
                    </Upload>
                  </Tooltip>

                  <Popover
                    content={
                      <div style={{ width: 300 }}>
                        <Picker
                          data={data}
                          onEmojiSelect={handleEmojiSelect}
                          theme={isDark ? 'dark' : 'light'}
                        />
                      </div>
                    }
                    trigger="click"
                    open={showEmojiPicker}
                    onOpenChange={setShowEmojiPicker}
                  >
                    <Button
                      type="text"
                      icon={<SmileOutlined />}
                    />
                  </Popover>
                </div>

                {attachments.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-2">
                    {attachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="bg-gray-100 dark:bg-gray-700 p-2 rounded-md flex items-center"
                      >
                        {getFileIcon(attachment.type)}
                        <span className="ml-2 text-sm truncate max-w-[150px]">
                          {attachment.name}
                        </span>
                        <Button
                          type="text"
                          size="small"
                          className="ml-1"
                          danger
                          onClick={() =>
                            setAttachments(attachments.filter((a) => a.id !== attachment.id))
                          }
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex">
                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => {
                      setNewMessage(e.target.value);
                      handleTyping();
                    }}
                    onPressEnter={handleSendMessage}
                    className="flex-1 mr-2 rounded-full"
                    ref={messageInputRef}
                    suffix={
                      <Tooltip title="Press Enter to send">
                        <span className="text-gray-400">⏎</span>
                      </Tooltip>
                    }
                  />
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSendMessage}
                    className="bg-primary hover:bg-primary-hover rounded-full"
                  />
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-800">
              <img
                src="/images/message-placeholder.svg"
                alt="Select a conversation"
                className="w-48 h-48 mb-4 opacity-50"
              />
              <Empty
                description={
                  <div>
                    <p>Select a conversation to start messaging</p>
                    <p className="text-xs text-gray-500">Or start a new conversation</p>
                  </div>
                }
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          )}
        </Card>
      </div>

      {/* Edit Message Modal */}
      <Modal
        title="Edit Message"
        open={!!editingMessage}
        onCancel={() => setEditingMessage(null)}
        onOk={() => {
          if (editingMessage?.content) {
            handleEditMessage(editingMessage.id, editingMessage.content);
          }
        }}
      >
        {editingMessage && (
          <Input.TextArea
            value={editingMessage.content}
            onChange={(e) => setEditingMessage({ ...editingMessage, content: e.target.value })}
            rows={4}
          />
        )}
      </Modal>

      {/* Delete Message Modal */}
      <Modal
        title="Delete Message"
        open={!!messageToDelete}
        onCancel={() => setMessageToDelete(null)}
        onOk={() => handleDeleteMessage(messageToDelete.id)}
        okButtonProps={{ danger: true }}
        okText="Delete"
      >
        <p>Are you sure you want to delete this message? This action cannot be undone.</p>
      </Modal>

      {/* Attachment Preview Modal */}
      <Modal
        title={previewAttachment?.name}
        open={showAttachmentPreview}
        onCancel={() => {
          setShowAttachmentPreview(false);
          setPreviewAttachment(null);
        }}
        footer={null}
        width={800}
      >
        {previewAttachment ? (
          previewAttachment.type && previewAttachment.type.startsWith('image/') ? (
            <img
              src={previewAttachment.url}
              alt={previewAttachment.name || 'Image preview'}
              style={{ maxWidth: '100%', maxHeight: '70vh' }}
            />
          ) : (
            <div className="text-center p-10">
              <p>{getFileIcon(previewAttachment?.type)}</p>
              <p className="mt-4">{previewAttachment?.name || 'Unnamed file'}</p>
              <p className="text-gray-500">{formatFileSize(previewAttachment?.size)}</p>
              {previewAttachment?.url && (
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  className="mt-4"
                  onClick={() => window.open(previewAttachment.url)}
                >
                  Download
                </Button>
              )}
            </div>
          )
        ) : (
          <div className="text-center p-10">
            <p>No attachment to preview</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Messages;
