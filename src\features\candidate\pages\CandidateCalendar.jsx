/**
 * Candidate Calendar Page
 * 
 * Calendar page specifically designed for candidates with:
 * - Assessment scheduling
 * - Application deadlines
 * - Follow-up reminders
 * - Meeting scheduling
 * - Google Calendar integration
 */

import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, Button, Alert, Row, Col, Statistic } from 'antd';
import { 
  CalendarOutlined, 
  ClockCircleOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  GoogleOutlined 
} from '@ant-design/icons';
import Calendar from '@/components/shared/Calendar';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';

const { Title, Text } = Typography;

const CandidateCalendar = () => {
  const { user, profile } = useAuth();
  const { appliedJobs, assessments } = useCandidateStore();
  const [upcomingEvents, setUpcomingEvents] = useState([]);

  // Candidate-specific event types
  const candidateEventTypes = [
    { id: 'assessment', name: 'Assessment', color: 'blue' },
    { id: 'application_deadline', name: 'Application Deadline', color: 'red' },
    { id: 'follow_up', name: 'Follow Up', color: 'orange' },
    { id: 'meeting', name: 'Meeting', color: 'green' },
    { id: 'reminder', name: 'Reminder', color: 'purple' },
    { id: 'preparation', name: 'Preparation', color: 'cyan' }
  ];

  // Get participants (recruiters, HR contacts)
  const getParticipants = () => {
    const participants = [];
    
    // Add company contacts from applied jobs
    appliedJobs?.forEach(job => {
      if (job.companies?.contact_email) {
        participants.push({
          name: job.companies.company_name,
          email: job.companies.contact_email
        });
      }
    });

    return participants;
  };

  // Calculate upcoming events from assessments and applications
  useEffect(() => {
    const events = [];
    const now = new Date();

    // Add assessment deadlines
    assessments?.available?.forEach(assessment => {
      if (assessment.deadline) {
        const deadline = new Date(assessment.deadline);
        if (deadline > now) {
          events.push({
            type: 'assessment',
            title: `Assessment: ${assessment.title}`,
            date: deadline,
            priority: 'high'
          });
        }
      }
    });

    // Add application follow-ups (7 days after application)
    appliedJobs?.forEach(job => {
      if (job.applied_at) {
        const followUpDate = new Date(job.applied_at);
        followUpDate.setDate(followUpDate.getDate() + 7);
        
        if (followUpDate > now) {
          events.push({
            type: 'follow_up',
            title: `Follow up: ${job.title}`,
            date: followUpDate,
            priority: 'medium'
          });
        }
      }
    });

    setUpcomingEvents(events.slice(0, 5)); // Show top 5 upcoming events
  }, [assessments, appliedJobs]);

  // Get calendar statistics
  const getCalendarStats = () => {
    const now = new Date();
    const thisWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const upcomingAssessments = assessments?.available?.filter(a => 
      a.deadline && new Date(a.deadline) <= thisWeek
    ).length || 0;

    const pendingApplications = appliedJobs?.filter(job => 
      job.status === 'applied' || job.status === 'under_review'
    ).length || 0;

    return {
      upcomingAssessments,
      pendingApplications,
      totalEvents: upcomingEvents.length,
      completedAssessments: assessments?.completed?.length || 0
    };
  };

  const stats = getCalendarStats();

  if (!user) {
    return (
      <div className="flex justify-center items-center h-64">
        <Alert
          message="Authentication Required"
          description="Please log in to access your calendar."
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className="candidate-calendar p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarOutlined className="text-2xl text-primary mr-3" />
            <div>
              <Title level={2} className="m-0">
                My Calendar
              </Title>
              <Text type="secondary">
                Manage your assessments, deadlines, and meetings
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Statistics */}
      <Row gutter={16} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Upcoming Assessments"
              value={stats.upcomingAssessments}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending Applications"
              value={stats.pendingApplications}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completed Assessments"
              value={stats.completedAssessments}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Events"
              value={stats.totalEvents}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Card className="mb-6" size="small">
        <div className="flex items-center justify-between">
          <Text strong>Quick Actions</Text>
          <Space>
            <Button 
              type="primary" 
              icon={<CalendarOutlined />}
              size="small"
            >
              Schedule Assessment
            </Button>
            <Button 
              icon={<ClockCircleOutlined />}
              size="small"
            >
              Set Reminder
            </Button>
            <Button 
              icon={<GoogleOutlined />}
              size="small"
            >
              Sync Google Calendar
            </Button>
          </Space>
        </div>
      </Card>

      {/* Calendar Component */}
      <Calendar
        userType="candidate"
        eventTypes={candidateEventTypes}
        participants={getParticipants()}
        viewOptions={{
          month: true,
          day: true,
          agenda: true
        }}
      />

      {/* Upcoming Events Summary */}
      {upcomingEvents.length > 0 && (
        <Card className="mt-6" title="Upcoming Events" size="small">
          <div className="space-y-3">
            {upcomingEvents.map((event, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    event.priority === 'high' ? 'bg-red-500' :
                    event.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`} />
                  <div>
                    <Text strong>{event.title}</Text>
                    <div className="text-sm text-gray-500">
                      {event.date.toLocaleDateString()} at {event.date.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
                <Button size="small" type="link">
                  View Details
                </Button>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Help Section */}
      <Card className="mt-6" size="small">
        <Title level={5}>Calendar Tips for Candidates</Title>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Text strong>Assessment Scheduling</Text>
            <div className="text-sm text-gray-600 mt-1">
              Schedule your assessments in advance and set reminders to prepare adequately
            </div>
          </div>
          <div>
            <Text strong>Application Tracking</Text>
            <div className="text-sm text-gray-600 mt-1">
              Keep track of application deadlines and follow-up dates
            </div>
          </div>
          <div>
            <Text strong>Google Calendar Sync</Text>
            <div className="text-sm text-gray-600 mt-1">
              Sync with Google Calendar to access your schedule from anywhere
            </div>
          </div>
          <div>
            <Text strong>Meeting Preparation</Text>
            <div className="text-sm text-gray-600 mt-1">
              Block time for interview preparation and research
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CandidateCalendar;
