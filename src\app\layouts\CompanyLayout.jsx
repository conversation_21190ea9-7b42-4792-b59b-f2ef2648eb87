import React, { useState, useEffect } from 'react';
import { Layout, Switch, Spin } from 'antd';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import {
  DashboardOutlined,
  TeamOutlined,
  FileTextOutlined,
  DollarOutlined,
  UserSwitchOutlined,
  CalendarOutlined,
  UserOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  LogoutOutlined,
  BulbOutlined,
  BulbFilled,
} from '@ant-design/icons';
import PageTitle from '@/components/PageTitle';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useColorModeStore } from '@/store/colorMode.store';
import useAuth from '@/hooks/useAuth';

import useCompanyStore from '@/features/company/store/company.store';
import { logo_lite, logo_dark } from '@/assets';
import SearchModal from '@/components/shared/SearchModal';
import AppSidebar from '@/components/layouts/AppSidebar';
import AppHeader from '@/components/layouts/AppHeader';
import MobileDrawer from '@/components/layouts/MobileDrawer';
import showToast from '@/utils/toast';

const { Content, Footer } = Layout;

// Icon style for consistent sizing
const iconStyle = { fontSize: '18px' };

const CompanyLayout = () => {
  const location = useLocation();
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(max-width: 991px)');
  const { colorMode, toggleColorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Auth state from useAuth hook
  const { profile, logout, loading } = useAuth();

  // Company state from company store
  const { shortlisted, interviewRequests } = useCompanyStore();

  // Local UI state
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [upcomingInterviews, setUpcomingInterviews] = useState([]);

  // Initialize mock data for demo
  useEffect(() => {
    setNotifications([
      { id: 1, title: 'New candidate application', read: false },
      { id: 2, title: 'Interview scheduled', read: false },
      { id: 3, title: 'Payment received', read: true },
      { id: 4, title: 'System update', read: true },
      { id: 5, title: 'New feature available', read: false },
    ]);

    setUpcomingInterviews([
      {
        id: 1,
        title: 'Frontend Developer Interview',
        time: '10:00 AM',
        date: '2023-06-15',
        candidate: 'John Doe',
      },
      {
        id: 2,
        title: 'UX Designer Interview',
        time: '2:30 PM',
        date: '2023-06-15',
        candidate: 'Jane Smith',
      },
      {
        id: 3,
        title: 'Backend Developer Interview',
        time: '11:00 AM',
        date: '2023-06-16',
        candidate: 'Mike Johnson',
      },
    ]);
  }, []);

  // Collapse sidebar on mobile by default
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    }
  }, [isMobile]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileDrawer = () => {
    setMobileDrawerOpen(!mobileDrawerOpen);
  };

  const handleThemeToggle = () => {
    toggleColorMode();
    showToast.success(`Switched to ${isDark ? 'light' : 'dark'} mode`);
  };

  const handleLogout = async () => {
    try {
      const { success } = await logout();
      if (success) {
        showToast.success('Logged out successfully');
      }
    } catch (error) {
      showToast.error('Failed to logout');
    }
  };

  const notificationItems = notifications.map((notification) => ({
    key: notification.id,
    label: (
      <div
        className={`notification-item ${!notification.read ? 'font-bold' : ''} p-2`}
        style={{ fontSize: '16px' }}
      >
        {notification.title}
      </div>
    ),
  }));

  const interviewItems = upcomingInterviews.map((interview) => ({
    key: interview.id,
    label: (
      <div className="p-2">
        <div
          style={{ fontSize: '16px' }}
          className="font-semibold"
        >
          {interview.title}
        </div>
        <div style={{ fontSize: '14px' }}>
          {interview.date} at {interview.time}
        </div>
        <div style={{ fontSize: '14px' }}>Candidate: {interview.candidate}</div>
      </div>
    ),
  }));

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/profile"
          style={{ fontSize: '16px' }}
        >
          Profile
        </Link>
      ),
    },
    {
      key: 'settings',
      icon: <SettingOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/settings"
          style={{ fontSize: '16px' }}
        >
          Settings
        </Link>
      ),
    },
    {
      key: 'theme',
      icon: isDark ? <BulbFilled style={iconStyle} /> : <BulbOutlined style={iconStyle} />,
      label: (
        <div className="flex items-center justify-between">
          <span style={{ fontSize: '16px' }}>Dark Mode</span>
          <Switch
            checked={isDark}
            onChange={handleThemeToggle}
            size="default"
            className="ml-2"
          />
        </div>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined style={iconStyle} />,
      label: (
        <Link
          to="/help"
          style={{ fontSize: '16px' }}
        >
          Help & Support
        </Link>
      ),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined style={iconStyle} />,
      label: <span style={{ fontSize: '16px' }}>{loading ? <Spin size="small" /> : 'Logout'}</span>,
      onClick: handleLogout,
    },
  ];

  const sidebarItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/dashboard"
          style={{ fontSize: '16px' }}
        >
          Dashboard
        </Link>
      ),
    },
    {
      key: 'candidates',
      icon: <TeamOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/candidates"
          style={{ fontSize: '16px' }}
        >
          Candidates
        </Link>
      ),
    },
    {
      key: 'jobs',
      icon: <FileTextOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/jobs"
          style={{ fontSize: '16px' }}
        >
          Job Listings
        </Link>
      ),
    },
    {
      key: 'billing',
      icon: <DollarOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/billing"
          style={{ fontSize: '16px' }}
        >
          Billing
        </Link>
      ),
    },
    {
      key: 'recruiters',
      icon: <UserSwitchOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/recruiters"
          style={{ fontSize: '16px' }}
        >
          Recruiters
        </Link>
      ),
    },
    {
      key: 'calendar',
      icon: <CalendarOutlined style={iconStyle} />,
      label: (
        <Link
          to="/org/calendar"
          style={{ fontSize: '16px' }}
        >
          Calendar
        </Link>
      ),
    },
  ];

  return (
    <Layout className="min-h-screen">
      <PageTitle
        title="Company Dashboard"
        description="Manage your company and candidates"
      />
      <AppSidebar
        collapsed={collapsed}
        sidebarItems={sidebarItems}
        selectedKey={location.pathname.split('/')[3] || 'dashboard'}
        isDark={isDark}
        profile={profile}
        handleLogout={handleLogout}
        isLoggingOut={loading}
      />

      {isMobile && (
        <MobileDrawer
          open={mobileDrawerOpen}
          onClose={toggleMobileDrawer}
          sidebarItems={sidebarItems}
          selectedKey={location.pathname.split('/')[3] || 'dashboard'}
          isDark={isDark}
          profile={profile}
          handleThemeToggle={handleThemeToggle}
          handleLogout={handleLogout}
          isLoggingOut={loading}
        />
      )}

      <Layout
        style={{
          marginLeft: isMobile ? 0 : collapsed ? 80 : 250,
          transition: 'all 0.2s',
          minHeight: '100vh',
        }}
      >
        <AppHeader
          collapsed={collapsed}
          toggleCollapsed={toggleCollapsed}
          toggleMobileDrawer={toggleMobileDrawer}
          isMobile={isMobile}
          isTablet={isTablet}
          isDark={isDark}
          logo_dark={logo_dark}
          logo_lite={logo_lite}
          notificationItems={notificationItems}
          userMenuItems={userMenuItems}
          setSearchModalVisible={setSearchModalVisible}
          notifications={notifications}
          profile={profile}
          interviewItems={interviewItems}
          upcomingInterviews={upcomingInterviews}
          handleThemeToggle={handleThemeToggle}
          showInterviews={true}
        />

        <Content
          className={`p-2 sm:p-4 transition-all`}
          style={{ minHeight: 280 }}
        >
          <div className="bg-card rounded-lg p-3 sm:p-4 shadow-sm">
            <Outlet />
          </div>
        </Content>

        <Footer
          style={{
            textAlign: 'center',
            padding: isMobile ? '8px 16px' : '12px 50px',
            fontSize: isMobile ? '14px' : '16px',
          }}
        >
          InterviewPro ©{new Date().getFullYear()} - Company Dashboard
        </Footer>
      </Layout>

      <SearchModal
        visible={searchModalVisible}
        onClose={() => setSearchModalVisible(!searchModalVisible)}
      />
    </Layout>
  );
};

export default CompanyLayout;
