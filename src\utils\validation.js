import { supabase } from './supabaseClient';
import showToast from './toast';

/**
 * Check if a phone number is already in use across all user types
 * @param {string} phoneNumber - The phone number to check
 * @param {string} userType - The type of user (candidate, company, interviewer)
 * @returns {Promise<{isUnique: boolean, message: string}>} - Result of the check
 */
export const checkUniquePhoneNumber = async (phoneNumber, userType) => {
  try {
    console.log(`Checking if phone number ${phoneNumber} is unique for user type: ${userType}`);

    // Use database function to check phone number existence (bypasses RLS)
    const { data, error } = await supabase.rpc('check_phone_exists_anywhere', {
      phone_to_check: phoneNumber,
    });

    if (error) {
      console.error('Error checking phone number uniqueness:', error);
      return { isUnique: true, message: '' }; // Default to allowing registration if check fails
    }

    console.log('Phone number check result:', data);

    if (data && data.exists === true) {
      console.log('Found existing phone number:', data);
      const roleMessage = data.role
        ? `This phone number is already registered with a ${data.role} account`
        : 'This phone number is already in use';
      return { isUnique: false, message: roleMessage };
    }

    console.log('Phone number appears to be unique');
    return { isUnique: true, message: '' };
  } catch (error) {
    console.error('Error checking phone number uniqueness:', error);
    return { isUnique: true, message: '' }; // Default to allowing registration if check fails
  }
};

/**
 * Check if an email has an incomplete registration (exists in auth but not verified)
 * @param {string} email - The email to check
 * @returns {Promise<{hasIncompleteRegistration: boolean, message: string}>} - Result of the check
 */
export const checkIncompleteRegistration = async (email) => {
  try {
    const { data, error } = await supabase.rpc('check_incomplete_registration', {
      email_to_check: email,
    });

    if (error) {
      return { hasIncompleteRegistration: false, message: '' };
    }

    if (data === true) {
      return {
        hasIncompleteRegistration: true,
        message:
          'Found incomplete registration. Cleaning up and proceeding with fresh registration...',
      };
    }

    return { hasIncompleteRegistration: false, message: '' };
  } catch (error) {
    return { hasIncompleteRegistration: false, message: '' };
  }
};

/**
 * Check if an email is already in use
 * @param {string} email - The email to check
 * @returns {Promise<{isUnique: boolean, message: string, hasIncompleteRegistration: boolean}>} - Result of the check
 */
export const checkUniqueEmail = async (email) => {
  try {
    // First, check for incomplete registration
    const incompleteCheck = await checkIncompleteRegistration(email);
    if (incompleteCheck.hasIncompleteRegistration) {
      return {
        isUnique: false,
        message: incompleteCheck.message,
        hasIncompleteRegistration: true,
      };
    }

    // Check if email exists using database function
    try {
      const { data, error } = await supabase.rpc('check_email_exists_anywhere', {
        email_to_check: email,
      });

      if (error) throw error;

      if (data === true) {
        return {
          isUnique: false,
          message: 'This email address is already registered',
          hasIncompleteRegistration: false,
        };
      }

      if (data === false) {
        return { isUnique: true, message: '', hasIncompleteRegistration: false };
      }
    } catch (rpcError) {
      // Continue to fallback method
    }

    // Fallback method: Quick check in profiles table only
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('email, role')
        .eq('email', email)
        .maybeSingle();

      if (profileError) {
        return { isUnique: true, message: '', hasIncompleteRegistration: false };
      }

      if (profileData) {
        const roleMessage = profileData.role
          ? `This email address is already registered with a ${profileData.role} account`
          : 'This email address is already in use';
        return { isUnique: false, message: roleMessage, hasIncompleteRegistration: false };
      }

      return { isUnique: true, message: '', hasIncompleteRegistration: false };
    } catch (profileError) {
      return { isUnique: true, message: '', hasIncompleteRegistration: false };
    }
  } catch (error) {
    return {
      isUnique: true,
      message: '',
      hasIncompleteRegistration: false,
    };
  }
};
