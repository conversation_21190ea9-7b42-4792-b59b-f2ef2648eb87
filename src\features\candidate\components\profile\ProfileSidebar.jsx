/**
 * Profile Sidebar Component
 * Combines Languages, Education, and Certifications cards
 */
import { Col } from 'antd';
import LanguagesCard from './LanguagesCard';
import EducationCard from './EducationCard';
import CertificationsCard from './CertificationsCard';

const ProfileSidebar = ({ profile, onEditProfile }) => {
  return (
    <Col
      xs={24}
      lg={8}
    >
      <div className="grid grid-cols-1 gap-4">
        <LanguagesCard
          profile={profile}
          onEditProfile={onEditProfile}
        />
        <EducationCard
          profile={profile}
          onEditProfile={onEditProfile}
        />
        <CertificationsCard
          profile={profile}
          onEditProfile={onEditProfile}
        />
      </div>
    </Col>
  );
};

export default ProfileSidebar;
