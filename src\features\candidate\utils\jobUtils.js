/**
 * Job utility functions for candidate features
 */

/**
 * Format salary range for display with rupee symbol
 * @param {Object} salaryRange - Salary range object with min and max
 * @returns {string} Formatted salary range string with rupee symbol
 */
export const formatSalaryRange = (salaryRange) => {
  if (!salaryRange) return 'Not specified';

  const { min, max } = salaryRange;

  if (min && max) {
    return `₹ ${formatCurrency(min)} - ₹ ${formatCurrency(max)}`;
  } else if (min) {
    return `₹ ${formatCurrency(min)}+`;
  } else if (max) {
    return `Up to ₹ ${formatCurrency(max)}`;
  }

  return 'Not specified';
};

/**
 * Format currency with appropriate units (K, L, Cr)
 * @param {number} amount - Amount to format
 * @returns {string} Formatted currency string
 */
const formatCurrency = (amount) => {
  if (amount >= 10000000) {
    return `${(amount / 10000000).toFixed(1)}Cr`;
  } else if (amount >= 100000) {
    return `${(amount / 100000).toFixed(1)}L`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(0)}K`;
  }
  return amount.toString();
};

/**
 * Calculate job match percentage based on candidate profile
 * @param {Object} job - Job object
 * @param {Object} profile - Candidate profile object
 * @returns {number} Match percentage (0-100)
 */
export const calculateJobMatch = (job, profile) => {
  if (!job || !profile) return 0;

  let matchScore = 0;
  let totalCriteria = 0;

  // Experience level match (30% weight)
  totalCriteria += 30;
  if (job.experience_level && profile.years_experience !== undefined) {
    const jobExpLevel = parseExperienceLevel(job.experience_level);
    const candidateExp = profile.years_experience;

    if (candidateExp >= jobExpLevel.min && candidateExp <= jobExpLevel.max) {
      matchScore += 30;
    } else if (Math.abs(candidateExp - jobExpLevel.min) <= 2) {
      matchScore += 20; // Partial match
    }
  }

  // Skills match (40% weight)
  totalCriteria += 40;
  if (job.required_skills && profile.skills) {
    const jobSkills = job.required_skills.map((skill) => skill.toLowerCase());
    const candidateSkills = profile.skills
      .map((skill) => (typeof skill === 'string' ? skill.toLowerCase() : skill.name?.toLowerCase()))
      .filter(Boolean);

    const matchingSkills = jobSkills.filter((skill) =>
      candidateSkills.some(
        (candidateSkill) => candidateSkill.includes(skill) || skill.includes(candidateSkill)
      )
    );

    const skillMatchPercentage = (matchingSkills.length / jobSkills.length) * 40;
    matchScore += skillMatchPercentage;
  }

  // Location match (15% weight)
  totalCriteria += 15;
  if (job.location && profile.city) {
    const jobLocation = job.location.toLowerCase();
    const candidateLocation = profile.city.toLowerCase();

    if (jobLocation.includes('remote') || candidateLocation.includes('remote')) {
      matchScore += 15;
    } else if (jobLocation.includes(candidateLocation) || candidateLocation.includes(jobLocation)) {
      matchScore += 15;
    } else if (jobLocation.includes('anywhere') || jobLocation.includes('pan india')) {
      matchScore += 10;
    }
  }

  // Role match (15% weight)
  totalCriteria += 15;
  if (job.title && profile.role_applied_for) {
    const jobTitle = job.title.toLowerCase();
    const candidateRole = profile.role_applied_for.toLowerCase();

    if (jobTitle.includes(candidateRole) || candidateRole.includes(jobTitle)) {
      matchScore += 15;
    } else {
      // Check for similar roles
      const similarRoles = getSimilarRoles(candidateRole);
      if (similarRoles.some((role) => jobTitle.includes(role))) {
        matchScore += 10;
      }
    }
  }

  return Math.round((matchScore / totalCriteria) * 100);
};

/**
 * Parse experience level string to min/max years
 * @param {string} experienceLevel - Experience level string
 * @returns {Object} Object with min and max years
 */
const parseExperienceLevel = (experienceLevel) => {
  const level = experienceLevel.toLowerCase();

  if (level.includes('fresher') || level.includes('entry')) {
    return { min: 0, max: 2 };
  } else if (level.includes('junior') || level.includes('1-3')) {
    return { min: 1, max: 3 };
  } else if (level.includes('mid') || level.includes('3-5')) {
    return { min: 3, max: 5 };
  } else if (level.includes('senior') || level.includes('5-8')) {
    return { min: 5, max: 8 };
  } else if (level.includes('lead') || level.includes('8+')) {
    return { min: 8, max: 15 };
  } else if (level.includes('manager') || level.includes('10+')) {
    return { min: 10, max: 20 };
  }

  // Try to extract numbers from the string
  const numbers = level.match(/\d+/g);
  if (numbers && numbers.length >= 2) {
    return { min: parseInt(numbers[0]), max: parseInt(numbers[1]) };
  } else if (numbers && numbers.length === 1) {
    const num = parseInt(numbers[0]);
    return { min: num, max: num + 3 };
  }

  return { min: 0, max: 20 }; // Default range
};

/**
 * Get similar roles for role matching
 * @param {string} role - Candidate role
 * @returns {Array} Array of similar role keywords
 */
const getSimilarRoles = (role) => {
  const roleMap = {
    developer: ['engineer', 'programmer', 'coder', 'dev'],
    engineer: ['developer', 'programmer', 'architect'],
    designer: ['ui', 'ux', 'graphic', 'visual'],
    manager: ['lead', 'head', 'director', 'supervisor'],
    analyst: ['associate', 'specialist', 'consultant'],
    tester: ['qa', 'quality', 'testing'],
    devops: ['sre', 'infrastructure', 'cloud', 'deployment'],
    frontend: ['ui', 'react', 'angular', 'vue'],
    backend: ['api', 'server', 'database', 'node'],
    fullstack: ['full-stack', 'full stack', 'frontend', 'backend'],
  };

  for (const [key, values] of Object.entries(roleMap)) {
    if (role.includes(key)) {
      return values;
    }
  }

  return [];
};

/**
 * Format job posting date
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date string
 */
export const formatJobDate = (dateString) => {
  if (!dateString) return 'Recently';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years} year${years > 1 ? 's' : ''} ago`;
  }
};

/**
 * Get match color based on percentage
 * @param {number} percentage - Match percentage
 * @returns {string} Color class or hex code
 */
export const getMatchColor = (percentage) => {
  if (percentage >= 80) return '#52c41a'; // Green
  if (percentage >= 60) return '#faad14'; // Orange
  if (percentage >= 40) return '#1890ff'; // Blue
  return '#d9d9d9'; // Gray
};

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 150) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};
