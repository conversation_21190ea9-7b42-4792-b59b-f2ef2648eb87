/**
 * Profile Overview Component
 * Main content area with professional summary and skills
 */
import { Col, Row } from 'antd';
import ProfessionalSummaryCard from './ProfessionalSummaryCard';
import SkillsCard from './SkillsCard';
import ProfileSidebar from './ProfileSidebar';

const ProfileOverview = ({ profile, onEditProfile }) => {
  return (
    <Row gutter={[24, 24]}>
      {/* Main Content */}
      <Col
        xs={24}
        lg={16}
      >
        <ProfessionalSummaryCard
          profile={profile}
          onEditProfile={onEditProfile}
        />
        <SkillsCard
          profile={profile}
          onEditProfile={onEditProfile}
        />
      </Col>

      {/* Sidebar */}
      <ProfileSidebar
        profile={profile}
        onEditProfile={onEditProfile}
      />
    </Row>
  );
};

export default ProfileOverview;
