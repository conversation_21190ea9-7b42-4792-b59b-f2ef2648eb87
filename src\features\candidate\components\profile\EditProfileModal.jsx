/**
 * Edit Profile Modal Component
 * Reusable modal for editing candidate profile information
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Divider,
  Upload,
  Select,
  InputNumber,
  message,
  Row,
  Col,
  Space,
  Tag,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  UploadOutlined,
  LinkedinOutlined,
  BankOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons';
import { CANDIDATE_ROLES } from '@/features/candidate/constants';

const { Option } = Select;
const { TextArea } = Input;

const EditProfileModal = ({
  visible,
  onCancel,
  onSave,
  profile,
  loading = false,
  onPhotoUpload,
  onResumeUpload,
}) => {
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (visible && profile) {
      form.setFieldsValue({
        ...profile,
        skills: profile.skills || [],
        languages: profile.languages || [],
        education: profile.education || [],
        certifications: profile.certifications || [],
        experience: profile.experience || [],
      });
    }
  }, [visible, profile, form]);

  const handleSubmit = async (values) => {
    setSaving(true);
    try {
      await onSave(values);
      message.success('Profile updated successfully');
      onCancel();
    } catch (error) {
      message.error(error.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const beforePhotoUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG files!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must be smaller than 2MB!');
      return false;
    }
    if (onPhotoUpload) {
      onPhotoUpload(file);
    }
    return false; // Prevent auto upload
  };

  const beforeResumeUpload = (file) => {
    const isPdfOrDoc =
      file.type === 'application/pdf' ||
      file.type === 'application/msword' ||
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    if (!isPdfOrDoc) {
      message.error('You can only upload PDF or Word documents!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('File must be smaller than 5MB!');
      return false;
    }
    if (onResumeUpload) {
      onResumeUpload(file);
    }
    return false; // Prevent auto upload
  };

  return (
    <Modal
      title="Edit Your Profile"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button
          key="cancel"
          onClick={onCancel}
        >
          Cancel
        </Button>,
        <Button
          key="save"
          type="primary"
          loading={saving}
          onClick={() => form.submit()}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Save Changes
        </Button>,
      ]}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={profile || {}}
      >
        <Divider orientation="left">Basic Information</Divider>
        <Row gutter={16}>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="full_name"
              label="Full Name"
              rules={[{ required: true, message: 'Please enter your full name' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Full Name"
              />
            </Form.Item>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter your email' },
                { type: 'email', message: 'Please enter a valid email' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Email"
                disabled
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="phone_number"
              label="Mobile Number"
              rules={[
                { required: true, message: 'Please enter your mobile number' },
                {
                  pattern: /^[0-9]{10}$/,
                  message: 'Please enter a valid 10-digit mobile number',
                },
              ]}
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="Mobile Number"
              />
            </Form.Item>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="city"
              label="City"
              rules={[{ required: true, message: 'Please enter your city' }]}
            >
              <Input
                prefix={<HomeOutlined />}
                placeholder="City"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="bio"
          label="Professional Bio"
          rules={[{ max: 500, message: 'Bio must be less than 500 characters' }]}
        >
          <TextArea
            rows={4}
            placeholder="Write a brief professional summary about yourself..."
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Divider orientation="left">Professional Information</Divider>
        <Row gutter={16}>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="role_applied_for"
              label="Role Applied For"
              rules={[{ required: true, message: 'Please select a role' }]}
            >
              <Select placeholder="Select Role">
                {CANDIDATE_ROLES.map((role) => (
                  <Option
                    key={role}
                    value={role}
                  >
                    {role}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="years_experience"
              label="Years of Experience"
              rules={[{ required: true, message: 'Please enter your experience' }]}
            >
              <InputNumber
                min={0}
                max={50}
                placeholder="Years"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="current_job_title"
              label="Current Job Title"
            >
              <Input
                prefix={<BankOutlined />}
                placeholder="Current Job Title"
              />
            </Form.Item>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="current_company"
              label="Current Company"
            >
              <Input
                prefix={<BankOutlined />}
                placeholder="Current Company"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="expected_ctc"
              label="Expected CTC"
            >
              <Input
                prefix={<DollarOutlined />}
                placeholder="e.g., 10-15 LPA"
              />
            </Form.Item>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item
              name="notice_period"
              label="Notice Period"
            >
              <Select placeholder="Select Notice Period">
                <Option value="Immediate">Immediate</Option>
                <Option value="15 days">15 days</Option>
                <Option value="1 month">1 month</Option>
                <Option value="2 months">2 months</Option>
                <Option value="3 months">3 months</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="linkedin_url"
          label="LinkedIn Profile"
        >
          <Input
            prefix={<LinkedinOutlined />}
            placeholder="https://linkedin.com/in/yourprofile"
          />
        </Form.Item>

        <Divider orientation="left">Files</Divider>
        <Row gutter={16}>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item label="Profile Photo">
              <Upload
                beforeUpload={beforePhotoUpload}
                showUploadList={false}
                accept="image/*"
              >
                <Button icon={<UploadOutlined />}>Upload Photo</Button>
              </Upload>
              {profile?.profile_photo_url && (
                <div className="mt-2">
                  <img
                    src={profile.profile_photo_url}
                    alt="Profile"
                    className="w-16 h-16 rounded-full object-cover"
                  />
                </div>
              )}
            </Form.Item>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <Form.Item label="Resume">
              <Upload
                beforeUpload={beforeResumeUpload}
                showUploadList={false}
                accept=".pdf,.doc,.docx"
              >
                <Button icon={<UploadOutlined />}>Upload Resume</Button>
              </Upload>
              {profile?.resume_url && (
                <div className="mt-2">
                  <a
                    href={profile.resume_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    View Current Resume
                  </a>
                </div>
              )}
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">Skills</Divider>
        <Form.List name="skills">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Row
                  key={key}
                  gutter={16}
                  align="middle"
                >
                  <Col
                    xs={24}
                    md={8}
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'name']}
                      rules={[{ required: true, message: 'Skill name required' }]}
                    >
                      <Input placeholder="Skill name" />
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={6}
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'category']}
                      rules={[{ required: true, message: 'Category required' }]}
                    >
                      <Select placeholder="Category">
                        <Option value="Technical">Technical</Option>
                        <Option value="Programming">Programming</Option>
                        <Option value="Design">Design</Option>
                        <Option value="Management">Management</Option>
                        <Option value="Communication">Communication</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={6}
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'years_experience']}
                    >
                      <InputNumber
                        placeholder="Years"
                        min={0}
                        max={20}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={4}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<MinusCircleOutlined />}
                      onClick={() => remove(name)}
                    />
                  </Col>
                </Row>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  style={{ width: '100%' }}
                >
                  Add Skill
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>

        <Divider orientation="left">Languages</Divider>
        <Form.List name="languages">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Row
                  key={key}
                  gutter={16}
                  align="middle"
                >
                  <Col
                    xs={24}
                    md={10}
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'name']}
                      rules={[{ required: true, message: 'Language name required' }]}
                    >
                      <Input placeholder="Language name" />
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={10}
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'proficiency']}
                      rules={[{ required: true, message: 'Proficiency required' }]}
                    >
                      <Select placeholder="Proficiency">
                        <Option value="Beginner">Beginner</Option>
                        <Option value="Intermediate">Intermediate</Option>
                        <Option value="Fluent">Fluent</Option>
                        <Option value="Native">Native</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={4}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<MinusCircleOutlined />}
                      onClick={() => remove(name)}
                    />
                  </Col>
                </Row>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  style={{ width: '100%' }}
                >
                  Add Language
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default EditProfileModal;
