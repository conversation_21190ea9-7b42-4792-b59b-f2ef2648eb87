import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Switch,
  Divider,
  Select,
  Space,
  Alert,
  message,
  Spin,
} from 'antd';
import {
  LockOutlined,
  BellOutlined,
  UserOutlined,
  MailOutlined,
  GlobalOutlined,
  SecurityScanOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';
import useCandidateProfile from '@/features/candidate/hooks/useCandidateProfile';
import PageTitle from '@/components/PageTitle';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const Settings = () => {
  const [activeTab, setActiveTab] = useState('account');
  const [passwordForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [privacyForm] = Form.useForm();
  const [preferencesForm] = Form.useForm();
  const [deleteConfirm, setDeleteConfirm] = useState('');
  const [saving, setSaving] = useState(false);

  const { user, resetAuth, loading } = useAuth();
  const {
    profile,
    fetchCandidateProfile,
    updateAccountSettings,
    updatePassword,
    deactivateAccount,
  } = useCandidateProfile();

  // Fetch candidate profile on component mount
  useEffect(() => {
    fetchCandidateProfile();
  }, [fetchCandidateProfile]);

  // Initialize form values when profile data is loaded
  useEffect(() => {
    if (profile) {
      // Set notification settings
      if (profile.notification_settings) {
        notificationForm.setFieldsValue(profile.notification_settings);
      } else {
        // Default notification settings
        notificationForm.setFieldsValue({
          email_notifications: true,
          job_alerts: true,
          interview_reminders: true,
          application_updates: true,
          marketing_emails: false,
        });
      }

      // Set privacy settings
      if (profile.privacy_settings) {
        privacyForm.setFieldsValue(profile.privacy_settings);
      } else {
        // Default privacy settings
        privacyForm.setFieldsValue({
          profile_visibility: 'public',
          show_contact_info: true,
          allow_messages: true,
          data_sharing: false,
        });
      }

      // Set preferences
      if (profile.preferences) {
        preferencesForm.setFieldsValue(profile.preferences);
      } else {
        // Default preferences
        preferencesForm.setFieldsValue({
          theme: 'light',
          language: 'en',
          timezone: 'UTC',
          job_preferences: [],
        });
      }
    }
  }, [profile, notificationForm, privacyForm, preferencesForm]);

  const handlePasswordChange = async (values) => {
    setSaving(true);
    try {
      await updatePassword({
        newPassword: values.newPassword,
      });

      message.success('Password updated successfully');
      passwordForm.resetFields();
    } catch (error) {
      message.error(error.message || 'Failed to update password');
    } finally {
      setSaving(false);
    }
  };

  const handleNotificationSettingsUpdate = async (values) => {
    try {
      await updateAccountSettings(values, 'notifications');
      message.success('Notification settings updated successfully');
    } catch (error) {
      message.error(error.message || 'Failed to update notification settings');
    }
  };

  const handlePrivacySettingsUpdate = async (values) => {
    try {
      await updateAccountSettings(values, 'privacy');
      message.success('Privacy settings updated successfully');
    } catch (error) {
      message.error(error.message || 'Failed to update privacy settings');
    }
  };

  const handlePreferencesUpdate = async (values) => {
    try {
      await updateAccountSettings(values, 'preferences');
      message.success('Preferences updated successfully');
    } catch (error) {
      message.error(error.message || 'Failed to update preferences');
    }
  };

  const handleDeleteAccount = async () => {
    if (deleteConfirm !== user?.email) {
      message.error('Email confirmation does not match');
      return;
    }

    try {
      await deactivateAccount();
      message.success('Your account has been deactivated');
      resetAuth(); // Log the user out
    } catch (error) {
      message.error(error.message || 'Failed to delete account');
    }
  };

  return (
    <div className="settings-page">
      <PageTitle title="Account Settings" />
      <Divider />
      {!profile ? (
        <div className="flex justify-center items-center p-10">
          <Spin
            size="large"
            tip="Loading settings..."
          />
        </div>
      ) : (
        <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            className="mb-4"
          >
            <TabPane
              tab={
                <span className="flex items-center">
                  <UserOutlined className="mr-2" /> Account
                </span>
              }
              key="account"
            />
            <TabPane
              tab={
                <span className="flex items-center">
                  <LockOutlined className="mr-2" /> Security
                </span>
              }
              key="security"
            />
            <TabPane
              tab={
                <span className="flex items-center">
                  <BellOutlined className="mr-2" /> Notifications
                </span>
              }
              key="notifications"
            />
            <TabPane
              tab={
                <span className="flex items-center">
                  <SecurityScanOutlined className="mr-2" /> Privacy
                </span>
              }
              key="privacy"
            />
          </Tabs>

          {activeTab === 'account' && (
            <div>
              <Title level={4}>Account Information</Title>
              <Text
                type="secondary"
                className="mb-6 block"
              >
                Manage your account details and preferences
              </Text>

              <Form
                layout="vertical"
                initialValues={{
                  email: user?.email,
                  language: 'english',
                  timezone: 'UTC',
                }}
              >
                <Form.Item
                  name="email"
                  label="Email Address"
                  rules={[
                    { required: true, message: 'Please enter your email' },
                    { type: 'email', message: 'Please enter a valid email' },
                  ]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    disabled
                    className="max-w-md"
                  />
                </Form.Item>

                <Form.Item
                  name="language"
                  label="Language"
                >
                  <Select className="max-w-md">
                    <Option value="english">English</Option>
                    <Option value="spanish">Spanish</Option>
                    <Option value="french">French</Option>
                    <Option value="german">German</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="timezone"
                  label="Timezone"
                >
                  <Select className="max-w-md">
                    <Option value="UTC">UTC (Coordinated Universal Time)</Option>
                    <Option value="EST">EST (Eastern Standard Time)</Option>
                    <Option value="CST">CST (Central Standard Time)</Option>
                    <Option value="PST">PST (Pacific Standard Time)</Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    className="bg-primary hover:bg-primary-hover"
                  >
                    Save Changes
                  </Button>
                </Form.Item>
              </Form>

              <Divider />

              <Title
                level={4}
                className="text-red-500"
              >
                Delete Account
              </Title>
              <Text
                type="secondary"
                className="mb-4 block"
              >
                Once you delete your account, there is no going back. Please be certain.
              </Text>

              <Alert
                message="Warning"
                description="Deleting your account will permanently remove all your data, including profile information, applications, and messages."
                type="warning"
                showIcon
                className="mb-4"
              />

              <Form layout="vertical">
                <Form.Item
                  label="Confirm by typing your email address"
                  rules={[
                    { required: true, message: 'Please confirm your email' },
                    {
                      validator: (_, value) =>
                        value === user?.email
                          ? Promise.resolve()
                          : Promise.reject('Email does not match'),
                    },
                  ]}
                >
                  <Input
                    placeholder="Enter your email address"
                    value={deleteConfirm}
                    onChange={(e) => setDeleteConfirm(e.target.value)}
                    className="max-w-md"
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    danger
                    type="primary"
                    icon={<DeleteOutlined />}
                    onClick={handleDeleteAccount}
                    loading={loading}
                    disabled={deleteConfirm !== user?.email}
                  >
                    Delete Account
                  </Button>
                </Form.Item>
              </Form>
            </div>
          )}

          {activeTab === 'security' && (
            <div>
              <Title level={4}>Password</Title>
              <Text
                type="secondary"
                className="mb-6 block"
              >
                Update your password to keep your account secure
              </Text>

              <Form
                form={passwordForm}
                layout="vertical"
                onFinish={handlePasswordChange}
                className="max-w-md"
              >
                <Form.Item
                  name="newPassword"
                  label="New Password"
                  rules={[
                    { required: true, message: 'Please enter your new password' },
                    { min: 8, message: 'Password must be at least 8 characters' },
                  ]}
                >
                  <Input.Password prefix={<LockOutlined />} />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  label="Confirm New Password"
                  dependencies={['newPassword']}
                  rules={[
                    { required: true, message: 'Please confirm your new password' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newPassword') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject('The two passwords do not match');
                      },
                    }),
                  ]}
                >
                  <Input.Password prefix={<LockOutlined />} />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    className="bg-primary hover:bg-primary-hover"
                  >
                    Update Password
                  </Button>
                </Form.Item>
              </Form>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div>
              <Title level={4}>Notification Preferences</Title>
              <Text
                type="secondary"
                className="mb-6 block"
              >
                Manage how and when you receive notifications
              </Text>

              <Form
                form={notificationForm}
                layout="vertical"
                onFinish={handleNotificationSettingsUpdate}
                initialValues={{
                  emailNotifications: true,
                  applicationUpdates: true,
                  interviewReminders: true,
                  jobRecommendations: true,
                  marketingEmails: false,
                }}
              >
                <Form.Item
                  name="emailNotifications"
                  valuePropName="checked"
                  label="Email Notifications"
                >
                  <Switch />
                </Form.Item>

                <Divider />

                <Form.Item
                  name="applicationUpdates"
                  valuePropName="checked"
                  label="Application Updates"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name="interviewReminders"
                  valuePropName="checked"
                  label="Interview Reminders"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name="jobRecommendations"
                  valuePropName="checked"
                  label="Job Recommendations"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  name="marketingEmails"
                  valuePropName="checked"
                  label="Marketing Emails"
                >
                  <Switch />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="bg-primary hover:bg-primary-hover"
                  >
                    Save Preferences
                  </Button>
                </Form.Item>
              </Form>
            </div>
          )}

          {activeTab === 'privacy' && (
            <div>
              <Title level={4}>Privacy Settings</Title>
              <Text
                type="secondary"
                className="mb-6 block"
              >
                Control your privacy and data sharing preferences
              </Text>

              <Form
                form={privacyForm}
                layout="vertical"
                onFinish={handlePrivacySettingsUpdate}
                initialValues={{
                  profileVisibility: 'all',
                  resumeVisibility: 'approved',
                  dataSharing: true,
                  activityTracking: true,
                }}
              >
                <Form.Item
                  name="profileVisibility"
                  label="Profile Visibility"
                >
                  <Select>
                    <Option value="all">Visible to All Companies</Option>
                    <Option value="applied">Only Companies I've Applied To</Option>
                    <Option value="approved">Only Approved Companies</Option>
                    <Option value="none">Hidden</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="resumeVisibility"
                  label="Resume Visibility"
                >
                  <Select>
                    <Option value="all">Visible to All Companies</Option>
                    <Option value="applied">Only Companies I've Applied To</Option>
                    <Option value="approved">Only Approved Companies</Option>
                    <Option value="none">Hidden</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="dataSharing"
                  valuePropName="checked"
                  label="Data Sharing"
                >
                  <Switch />
                </Form.Item>
                <Text
                  type="secondary"
                  className="mb-4 block -mt-4"
                >
                  Allow us to share anonymized data to improve our services
                </Text>

                <Form.Item
                  name="activityTracking"
                  valuePropName="checked"
                  label="Activity Tracking"
                >
                  <Switch />
                </Form.Item>
                <Text
                  type="secondary"
                  className="mb-4 block -mt-4"
                >
                  Allow us to track your activity to provide personalized recommendations
                </Text>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="bg-primary hover:bg-primary-hover"
                  >
                    Save Privacy Settings
                  </Button>
                </Form.Item>
              </Form>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default Settings;
