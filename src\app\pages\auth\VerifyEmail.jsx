import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import { getDashboardPath } from '@/utils/constants';
import OTPVerification from '@/components/auth/OTPVerification';

const VerifyEmail = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, role } = useAuth();
  const [email, setEmail] = useState('');

  useEffect(() => {
    // Get email from location state or user object
    if (location.state?.email) {
      setEmail(location.state.email);
    } else if (user?.email) {
      setEmail(user.email);
    } else {
      // If no email available, redirect to login
      navigate('/login');
    }
  }, [location, navigate, user]);

  // Handle successful OTP verification
  const handleOTPSuccess = async (result) => {
    try {
      // The OTP verification already marks email as verified through Supabase auth
      // We just need to mark it in our database and redirect
      if (result.user?.id) {
        await supabase.rpc('mark_email_verified', { user_id: result.user.id });
      }

      // Redirect to dashboard
      const dashboardPath = getDashboardPath(role);
      navigate(dashboardPath, { replace: true });
    } catch (error) {
      console.error('Error after OTP verification:', error);
      // Still redirect even if database update fails
      const dashboardPath = getDashboardPath(role);
      navigate(dashboardPath, { replace: true });
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <OTPVerification
        email={email}
        onSuccess={handleOTPSuccess}
      />
    </div>
  );
};

export default VerifyEmail;
