/**
 * useSaved<PERSON>obs Hook for Candidates
 *
 * <PERSON>les saved jobs operations and integrates with candidate store.
 * Uses optimized caching for better performance.
 */

import { useState, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import showToast from '@/utils/toast';

const useSavedJobs = () => {
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get store methods
  const { savedJobs, fetchSavedJobs } = useCandidateStore();

  /**
   * Save a job
   * Uses optimized caching for better performance
   */
  const saveJob = useCallback(
    async (jobId) => {
      if (!user || !profile) {
        showToast.error('Please login to save jobs');
        return { success: false, error: 'User not authenticated' };
      }

      setLoading(true);
      setError(null);

      try {
        // Check if already saved
        const existingSavedJob = savedJobs.find((saved) => saved.job_id === jobId);
        if (existingSavedJob) {
          showToast.warning('Job is already saved');
          return { success: false, error: 'Already saved' };
        }

        // Save job directly
        const { data, error } = await supabase
          .from('saved_jobs')
          .insert({
            candidate_id: profile.id,
            job_id: jobId,
          })
          .select()
          .single();

        if (error) throw error;

        // Update store with new saved job
        await fetchSavedJobs(profile.id, true);

        showToast.success('Job saved successfully!');
        return { success: true, data };
      } catch (error) {
        console.error('Error saving job:', error);
        const errorMessage = error.message || 'Failed to save job';
        setError(errorMessage);
        showToast.error(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [user, profile, savedJobs, fetchSavedJobs]
  );

  /**
   * Unsave a job
   * Uses optimized caching for better performance
   */
  const unsaveJob = useCallback(
    async (jobId) => {
      if (!user || !profile) {
        showToast.error('Please login to unsave jobs');
        return { success: false, error: 'User not authenticated' };
      }

      setLoading(true);
      setError(null);

      try {
        // Find the saved job record
        const savedJob = savedJobs.find((saved) => saved.job_id === jobId);
        if (!savedJob) {
          return { success: false, error: 'Job not saved' };
        }

        // Unsave job directly
        const { error } = await supabase
          .from('saved_jobs')
          .delete()
          .match({ candidate_id: profile.id, job_id: jobId });

        if (error) throw error;

        // Update store
        await fetchSavedJobs(profile.id, true);

        showToast.success('Job removed from saved jobs');
        return { success: true };
      } catch (error) {
        console.error('Error unsaving job:', error);
        const errorMessage = error.message || 'Failed to unsave job';
        setError(errorMessage);
        showToast.error(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [user, profile, savedJobs, fetchSavedJobs]
  );

  /**
   * Toggle save status of a job
   */
  const toggleSaveJob = useCallback(
    async (jobId) => {
      const isSaved = savedJobs.some((saved) => saved.job_id === jobId);

      if (isSaved) {
        return await unsaveJob(jobId);
      } else {
        return await saveJob(jobId);
      }
    },
    [savedJobs, saveJob, unsaveJob]
  );

  /**
   * Check if a job is saved
   */
  const isJobSaved = useCallback(
    (jobId) => {
      return savedJobs.some((saved) => saved.job_id === jobId);
    },
    [savedJobs]
  );

  /**
   * Get saved job details
   */
  const getSavedJobDetails = useCallback(
    (jobId) => {
      return savedJobs.find((saved) => saved.job_id === jobId) || null;
    },
    [savedJobs]
  );

  /**
   * Clear all saved jobs
   */
  const clearAllSavedJobs = useCallback(async () => {
    if (!user || !profile) {
      showToast.error('Please login to manage saved jobs');
      return { success: false, error: 'User not authenticated' };
    }

    setLoading(true);
    setError(null);

    try {
      const { error } = await supabase.from('saved_jobs').delete().eq('candidate_id', profile.id);

      if (error) throw error;

      // Update store
      await fetchSavedJobs(profile.id);

      showToast.success('All saved jobs cleared');
      return { success: true };
    } catch (err) {
      console.error('Error clearing saved jobs:', err);
      const errorMessage = err.message || 'Failed to clear saved jobs';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user, profile, fetchSavedJobs]);

  /**
   * Get saved jobs by category/filter
   */
  const getSavedJobsByFilter = useCallback(
    (filterFn) => {
      return savedJobs.filter(filterFn);
    },
    [savedJobs]
  );

  /**
   * Get saved jobs statistics
   */
  const getSavedJobsStats = useCallback(() => {
    const stats = {
      total: savedJobs.length,
      byExperienceLevel: {},
      byLocation: {},
      byCompanyType: {},
      recentlySaved: 0, // Last 7 days
    };

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    savedJobs.forEach((savedJob) => {
      const job = savedJob.jobs;
      if (!job) return;

      // Count by experience level
      if (job.experience_level) {
        stats.byExperienceLevel[job.experience_level] =
          (stats.byExperienceLevel[job.experience_level] || 0) + 1;
      }

      // Count by location
      if (job.location) {
        stats.byLocation[job.location] = (stats.byLocation[job.location] || 0) + 1;
      }

      // Count by company type
      if (job.companies?.company_type) {
        stats.byCompanyType[job.companies.company_type] =
          (stats.byCompanyType[job.companies.company_type] || 0) + 1;
      }

      // Count recently saved
      if (new Date(savedJob.created_at) > oneWeekAgo) {
        stats.recentlySaved++;
      }
    });

    return stats;
  }, [savedJobs]);

  /**
   * Search saved jobs
   */
  const searchSavedJobs = useCallback(
    (keyword) => {
      if (!keyword) return savedJobs;

      return savedJobs.filter((savedJob) => {
        const job = savedJob.jobs;
        if (!job) return false;

        return (
          job.title?.toLowerCase().includes(keyword.toLowerCase()) ||
          job.description?.toLowerCase().includes(keyword.toLowerCase()) ||
          job.companies?.company_name?.toLowerCase().includes(keyword.toLowerCase()) ||
          job.location?.toLowerCase().includes(keyword.toLowerCase())
        );
      });
    },
    [savedJobs]
  );

  return {
    // Data
    savedJobs,

    // UI State
    loading,
    error,

    // Actions
    saveJob,
    unsaveJob,
    toggleSaveJob,
    clearAllSavedJobs,

    // Utilities
    isJobSaved,
    getSavedJobDetails,
    getSavedJobsByFilter,
    getSavedJobsStats,
    searchSavedJobs,

    // Refresh
    refetch: () => fetchSavedJobs(profile?.id),
  };
};

export default useSavedJobs;
