/**
 * useJobs Hook for Candidates
 *
 * Handles fetching and managing jobs data for candidates.
 * Integrates with candidate store for state management.
 * Uses optimized caching for better performance.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import { calculateJobMatch } from '@/features/candidate/utils/jobUtils';

const useJobs = () => {
  const { user, profile } = useAuth();
  const [jobs, setJobs] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0,
  });

  // Ref to track if component is mounted
  const isMountedRef = useRef(true);

  // Get applied and saved jobs from store
  const { appliedJobs, savedJobs } = useCandidateStore();

  /**
   * Fetch all available jobs with optional filters
   * Uses optimized caching for better performance
   */
  const fetchJobs = useCallback(
    async (filterParams = {}, page = 1, pageSize = 12, forceRefresh = false) => {
      // Check if component is still mounted
      if (!isMountedRef.current) return { data: [], totalCount: 0 };

      setLoading(true);
      setError(null);

      try {
        // Prepare filter params
        const validFilters = {};
        Object.entries(filterParams)
          .filter(([_, value]) => value !== undefined && value !== null)
          .forEach(([key, value]) => {
            validFilters[key] = value;
          });

        // Fetch from database
        let query = supabase
          .from('jobs')
          .select(
            `
          *,
          companies:company_id (
            id,
            company_name,
            company_logo_url,
            company_type,
            company_size,
            office_locations,
            website_url
          )
        `
          )
          .eq('status', 'active')
          .order('created_at', { ascending: false });

        // Apply filters
        if (validFilters.location) {
          query = query.ilike('location', `%${validFilters.location}%`);
        }

        if (validFilters.experienceLevel) {
          query = query.eq('experience_level', validFilters.experienceLevel);
        }

        if (validFilters.jobType) {
          query = query.eq('job_type', validFilters.jobType);
        }

        if (validFilters.keyword) {
          query = query.or(
            `title.ilike.%${validFilters.keyword}%,description.ilike.%${validFilters.keyword}%`
          );
        }

        // Calculate pagination
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;

        // Apply pagination
        query = query.range(from, to);

        // Execute query
        const { data, error } = await query.limit(pageSize);

        if (error) throw error;

        // Check if component is still mounted before continuing
        if (!isMountedRef.current) return { data: [], totalCount: 0 };

        // Get total count for pagination
        const { count: totalCount, error: countError } = await supabase
          .from('jobs')
          .select('id', { count: 'exact' })
          .eq('status', 'active');

        if (countError) throw countError;

        // Check if component is still mounted before processing
        if (!isMountedRef.current) return { data: [], totalCount: 0 };

        // Process jobs to add computed fields
        const processedJobs = (data || []).map((job) => ({
          ...job,
          matchPercentage: calculateJobMatch(job, profile),
          isApplied: appliedJobs.some((app) => app.job_id === job.id),
          isSaved: savedJobs.some((saved) => saved.job_id === job.id),
        }));

        // Update state only if component is still mounted
        if (isMountedRef.current) {
          setJobs(data || []);
          setFilteredJobs(processedJobs);
          setPagination((prev) => ({
            ...prev,
            current: page,
            pageSize,
            total: totalCount,
          }));
          setFilters(filterParams);
        }

        return { data: processedJobs, totalCount };
      } catch (error) {
        console.error('Error fetching jobs:', error);
        if (isMountedRef.current) {
          setError(error.message);
        }
        return { data: [], totalCount: 0 };
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    },
    [user, profile, appliedJobs, savedJobs]
  );

  /**
   * Get job by ID
   * Uses optimized caching for better performance
   */
  const getJobById = useCallback(
    async (jobId, forceRefresh = false) => {
      if (!jobId) return null;

      setLoading(true);
      setError(null);

      try {
        // Fetch from database
        const { data, error: fetchError } = await supabase
          .from('jobs')
          .select(
            `
            *,
            companies:company_id (
              id,
              company_name,
              company_logo_url,
              company_type,
              company_size,
              office_locations,
              website_url
            )
          `
          )
          .eq('id', jobId)
          .single();

        if (fetchError) throw fetchError;

        // Process job data to add computed fields
        const processedJob = {
          ...data,
          matchPercentage: calculateJobMatch(data, profile),
          isApplied: appliedJobs.some((app) => app.job_id === data.id),
          isSaved: savedJobs.some((saved) => saved.job_id === data.id),
        };

        return processedJob;
      } catch (error) {
        console.error('Error fetching job:', error);
        setError(error.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [profile, appliedJobs, savedJobs, user]
  );

  // Initial fetch on mount
  useEffect(() => {
    if (user) {
      fetchJobs();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]); // fetchJobs intentionally excluded to prevent infinite loop

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Mark component as unmounted
      isMountedRef.current = false;
    };
  }, []);

  return {
    // Data
    jobs,
    filteredJobs,
    pagination,
    filters,

    // UI states
    loading,
    error,

    // Actions
    fetchJobs,
    getJobById,

    // Utilities
    calculateJobMatch,
    refetch: () => fetchJobs(filters, pagination.current, pagination.pageSize),
    clearFilters: () => {
      setFilters({});
      fetchJobs({}, 1, pagination.pageSize);
    },
  };
};

export default useJobs;
