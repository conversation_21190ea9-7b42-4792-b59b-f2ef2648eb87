/**
 * Professional Summary Card Component
 * Shows bio and key professional information
 */
import React from 'react';
import { Card, Typography, Divider, Row, Col, Al<PERSON>, Button } from 'antd';
import { DollarOutlined } from '@ant-design/icons';
import { User, Briefcase, MapPin, Zap } from 'lucide-react';

const { Title, Text, Paragraph } = Typography;

const ProfessionalSummaryCard = ({ profile, onEditProfile }) => {
  return (
    <Card
      title={
        <span className="flex items-center">
          <User
            size={18}
            className="mr-2 text-blue-500"
          />
          Professional Summary
        </span>
      }
      className="shadow-sm rounded-lg"
      style={{ marginBottom: '1.5rem' }}
    >
      <div className="space-y-6">
        {/* Bio Section */}
        <div>
          <Title
            level={5}
            className="mb-3"
          >
            About Me
          </Title>
          <Paragraph className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
            {profile?.bio || (
              <Alert
                message="Add a professional summary"
                description="A compelling bio helps employers understand your background and career goals."
                type="info"
                showIcon
                action={
                  <Button
                    size="small"
                    type="primary"
                    onClick={onEditProfile}
                  >
                    Add Bio
                  </Button>
                }
              />
            )}
          </Paragraph>
        </div>

        <Divider />

        {/* Key Information Grid */}
        <Row gutter={[24, 24]}>
          <Col
            xs={24}
            md={12}
          >
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Briefcase
                  size={20}
                  className="text-blue-500 mt-1"
                />
                <div>
                  <Title
                    level={5}
                    className="mb-1"
                  >
                    Current Role
                  </Title>
                  <Text className="text-gray-600">
                    {profile?.current_job_title ? (
                      <>
                        {profile.current_job_title}
                        {profile?.current_company && (
                          <span className="block text-sm text-gray-500">
                            at {profile.current_company}
                          </span>
                        )}
                      </>
                    ) : (
                      <Text
                        type="secondary"
                        italic
                      >
                        Not specified
                      </Text>
                    )}
                  </Text>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <MapPin
                  size={20}
                  className="text-green-500 mt-1"
                />
                <div>
                  <Title
                    level={5}
                    className="mb-1"
                  >
                    Location
                  </Title>
                  <Text className="text-gray-600">
                    {profile?.city || (
                      <Text
                        type="secondary"
                        italic
                      >
                        Not specified
                      </Text>
                    )}
                  </Text>
                </div>
              </div>
            </div>
          </Col>

          <Col
            xs={24}
            md={12}
          >
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Zap
                  size={20}
                  className="text-yellow-500 mt-1"
                />
                <div>
                  <Title
                    level={5}
                    className="mb-1"
                  >
                    Experience
                  </Title>
                  <Text className="text-gray-600">
                    {profile?.years_experience >= 0 ? (
                      <>
                        {profile.years_experience} years
                        {profile?.role_applied_for && (
                          <span className="block text-sm text-gray-500">
                            in {profile.role_applied_for}
                          </span>
                        )}
                      </>
                    ) : (
                      <Text
                        type="secondary"
                        italic
                      >
                        Not specified
                      </Text>
                    )}
                  </Text>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <DollarOutlined
                  className="text-red-500 mt-1"
                  style={{ fontSize: 20 }}
                />
                <div>
                  <Title
                    level={5}
                    className="mb-1"
                  >
                    Expected CTC
                  </Title>
                  <Text className="text-gray-600">
                    {profile?.expected_ctc || (
                      <Text
                        type="secondary"
                        italic
                      >
                        Not specified
                      </Text>
                    )}
                  </Text>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </Card>
  );
};

export default ProfessionalSummaryCard;
