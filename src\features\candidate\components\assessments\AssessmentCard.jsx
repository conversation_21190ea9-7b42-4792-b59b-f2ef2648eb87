import React from 'react';
import { Card, Typography, Tag, Button, Progress } from 'antd';
import { ClockCircleOutlined, CheckCircleOutlined, RightOutlined } from '@ant-design/icons';
import { SCORE_LEVELS } from '@/features/candidate/constants';

const { Title, Text } = Typography;

const AssessmentCard = ({ assessment, onStart, onView, completed = false }) => {
  const getScoreColor = (score) => {
    if (score >= SCORE_LEVELS.HIGH) return 'success';
    if (score >= SCORE_LEVELS.MEDIUM) return 'warning';
    return 'exception';
  };

  return (
    <Card
      hoverable
      className="mb-4 shadow-sm"
      actions={[
        completed ? (
          <Button
            type="link"
            onClick={() => onView(assessment)}
          >
            View Results <RightOutlined />
          </Button>
        ) : (
          <Button
            type="primary"
            onClick={() => onStart(assessment)}
          >
            Start Assessment
          </Button>
        ),
      ]}
    >
      <div className="flex flex-col">
        <Title level={4}>{assessment.title}</Title>
        <Text
          type="secondary"
          className="mb-2"
        >
          {assessment.description}
        </Text>

        <div className="flex items-center mb-2">
          <ClockCircleOutlined className="mr-2" />
          <Text>{assessment.duration} minutes</Text>
        </div>

        <div className="flex flex-wrap gap-2 mb-3">
          <Tag color="blue">{assessment.category}</Tag>
          <Tag color="purple">{assessment.questions_count} questions</Tag>
          {completed && (
            <Tag
              color="green"
              icon={<CheckCircleOutlined />}
            >
              Completed
            </Tag>
          )}
        </div>

        {completed && (
          <div className="mt-2">
            <Text strong>Your Score:</Text>
            <Progress
              percent={assessment.score}
              status={getScoreColor(assessment.score)}
              format={(percent) => `${percent}%`}
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default AssessmentCard;
