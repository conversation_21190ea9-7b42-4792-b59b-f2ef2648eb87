/**
 * Candidate Service
 *
 * Service for managing candidate-related operations including:
 * - Fetching candidate profiles
 * - Search and filtering
 * - Candidate management for interviewers
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Get candidates with search and filtering
 * @param {Object} params - Search and filter parameters
 * @returns {Promise<Object>} Response with candidates data
 */
export const getCandidates = async (params = {}) => {
  try {
    const {
      page = 1,
      limit = 12,
      search = '',
      experience = '',
      location = '',
      skills = '',
      availability = '',
    } = params;

    let query = supabase.from('candidate_profiles').select(`
        *,
        profiles!inner(
          id,
          email,
          full_name,
          avatar_url,
          created_at
        )
      `);

    // Apply search filter
    if (search) {
      query = query.or(`
        profiles.full_name.ilike.%${search}%,
        title.ilike.%${search}%,
        skills.cs.{${search}}
      `);
    }

    // Apply experience filter
    if (experience) {
      const [min, max] = experience.split('-');
      if (max === '+') {
        query = query.gte('years_of_experience', parseInt(min));
      } else {
        query = query
          .gte('years_of_experience', parseInt(min))
          .lte('years_of_experience', parseInt(max));
      }
    }

    // Apply location filter
    if (location) {
      query = query.ilike('location', `%${location}%`);
    }

    // Apply availability filter
    if (availability) {
      query = query.eq('availability_status', availability);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    // Order by created date
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching candidates:', error);
      return { success: false, error: error.message };
    }

    // Transform data to match expected format
    const transformedData =
      data?.map((candidate) => ({
        id: candidate.profiles.id,
        name: candidate.profiles.full_name,
        email: candidate.profiles.email,
        avatar: candidate.profiles.avatar_url,
        title: candidate.title || 'Professional',
        experience: candidate.years_of_experience
          ? `${candidate.years_of_experience} years`
          : 'Not specified',
        location: candidate.location || 'Not specified',
        expectedSalary: candidate.expected_salary
          ? `₹${candidate.expected_salary}`
          : 'Not specified',
        skills: candidate.skills || [],
        rating: candidate.rating || null,
        availability: candidate.availability_status || 'Available',
        bio: candidate.bio,
        phone: candidate.phone,
        linkedin_url: candidate.linkedin_url,
        github_url: candidate.github_url,
        portfolio_url: candidate.portfolio_url,
        created_at: candidate.created_at,
      })) || [];

    return {
      success: true,
      data: transformedData,
      total: count || 0,
      page,
      limit,
    };
  } catch (error) {
    console.error('Error in getCandidates:', error);
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Get candidate profile by ID
 * @param {string} candidateId - Candidate ID
 * @returns {Promise<Object>} Response with candidate data
 */
export const getCandidateById = async (candidateId) => {
  try {
    const { data, error } = await supabase
      .from('candidate_profiles')
      .select(
        `
        *,
        profiles!inner(
          id,
          email,
          full_name,
          avatar_url,
          created_at
        )
      `
      )
      .eq('profiles.id', candidateId)
      .single();

    if (error) {
      console.error('Error fetching candidate:', error);
      return { success: false, error: error.message };
    }

    // Transform data
    const transformedData = {
      id: data.profiles.id,
      name: data.profiles.full_name,
      email: data.profiles.email,
      avatar: data.profiles.avatar_url,
      title: data.title || 'Professional',
      experience: data.years_of_experience ? `${data.years_of_experience} years` : 'Not specified',
      location: data.location || 'Not specified',
      expectedSalary: data.expected_salary ? `₹${data.expected_salary}` : 'Not specified',
      skills: data.skills || [],
      rating: data.rating || null,
      availability: data.availability_status || 'Available',
      bio: data.bio,
      phone: data.phone,
      linkedin_url: data.linkedin_url,
      github_url: data.github_url,
      portfolio_url: data.portfolio_url,
      created_at: data.created_at,
    };

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    console.error('Error in getCandidateById:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Update candidate availability status
 * @param {string} candidateId - Candidate ID
 * @param {string} status - New availability status
 * @returns {Promise<Object>} Response with success status
 */
export const updateCandidateAvailability = async (candidateId, status) => {
  try {
    const { error } = await supabase
      .from('candidate_profiles')
      .update({
        availability_status: status,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', candidateId);

    if (error) {
      console.error('Error updating availability:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updateCandidateAvailability:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Get candidate statistics
 * @returns {Promise<Object>} Response with candidate statistics
 */
export const getCandidateStats = async () => {
  try {
    const { data, error } = await supabase
      .from('candidate_profiles')
      .select('availability_status, created_at');

    if (error) {
      console.error('Error fetching candidate stats:', error);
      return { success: false, error: error.message };
    }

    const stats = {
      total: data.length,
      available: data.filter((c) => c.availability_status === 'available').length,
      busy: data.filter((c) => c.availability_status === 'busy').length,
      interviewing: data.filter((c) => c.availability_status === 'interviewing').length,
      newThisMonth: data.filter((c) => {
        const created = new Date(c.created_at);
        const now = new Date();
        return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
      }).length,
    };

    return {
      success: true,
      data: stats,
    };
  } catch (error) {
    console.error('Error in getCandidateStats:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Search candidates by skills
 * @param {Array} skills - Array of skills to search for
 * @returns {Promise<Object>} Response with matching candidates
 */
export const searchCandidatesBySkills = async (skills) => {
  try {
    const { data, error } = await supabase
      .from('candidate_profiles')
      .select(
        `
        *,
        profiles!inner(
          id,
          email,
          full_name,
          avatar_url
        )
      `
      )
      .overlaps('skills', skills)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching candidates by skills:', error);
      return { success: false, error: error.message };
    }

    // Transform data
    const transformedData =
      data?.map((candidate) => ({
        id: candidate.profiles.id,
        name: candidate.profiles.full_name,
        email: candidate.profiles.email,
        avatar: candidate.profiles.avatar_url,
        title: candidate.title || 'Professional',
        skills: candidate.skills || [],
        experience: candidate.years_of_experience
          ? `${candidate.years_of_experience} years`
          : 'Not specified',
        location: candidate.location || 'Not specified',
        rating: candidate.rating || null,
      })) || [];

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    console.error('Error in searchCandidatesBySkills:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};
