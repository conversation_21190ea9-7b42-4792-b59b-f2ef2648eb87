-- SQL script to remove all candidate-related data from Supabase
-- Run this script in your Supabase SQL editor

-- 1. Drop candidate-related views first (if they exist)
DROP VIEW IF EXISTS candidate_profiles_complete CASCADE;

-- 2. Remove candidate-related foreign key constraints and references
-- Update interviews table to remove candidate references
ALTER TABLE IF EXISTS interviews 
DROP CONSTRAINT IF EXISTS interviews_candidate_id_fkey CASCADE;

-- Update applications table to remove candidate references  
ALTER TABLE IF EXISTS applications
DROP CONSTRAINT IF EXISTS applications_candidate_id_fkey CASCADE;

-- 3. Delete candidate data from related tables
-- Delete applications from candidates
DELETE FROM applications WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

-- Delete interviews with candidates
DELETE FROM interviews WHERE candidate_id IN (
  SELECT id FROM candidate_profiles  
);

-- Delete any job applications or saved jobs
DELETE FROM job_applications WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

DELETE FROM saved_jobs WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

-- Delete candidate assessments
DELETE FROM assessments WHERE candidate_id IN (
  SELECT id FROM candidate_profiles
);

-- Delete candidate notifications
DELETE FROM notifications WHERE user_id IN (
  SELECT id FROM candidate_profiles
) AND user_type = 'candidate';

-- Delete candidate messages
DELETE FROM messages WHERE sender_id IN (
  SELECT id FROM candidate_profiles
) AND sender_type = 'candidate';

DELETE FROM messages WHERE recipient_id IN (
  SELECT id FROM candidate_profiles
) AND recipient_type = 'candidate';

-- 4. Delete candidate profiles
DELETE FROM candidate_profiles;

-- 5. Remove candidate role from profiles table
DELETE FROM profiles WHERE role = 'candidate';

-- 6. Drop candidate-related tables
DROP TABLE IF EXISTS candidate_profiles CASCADE;
DROP TABLE IF EXISTS job_applications CASCADE;
DROP TABLE IF EXISTS saved_jobs CASCADE;
DROP TABLE IF EXISTS candidate_assessments CASCADE;
DROP TABLE IF EXISTS candidate_skills CASCADE;
DROP TABLE IF EXISTS candidate_experience CASCADE;
DROP TABLE IF EXISTS candidate_education CASCADE;
DROP TABLE IF EXISTS candidate_certifications CASCADE;

-- 7. Drop candidate-related functions
DROP FUNCTION IF EXISTS get_candidate_profile(uuid) CASCADE;
DROP FUNCTION IF EXISTS update_candidate_profile(uuid, jsonb) CASCADE;
DROP FUNCTION IF EXISTS create_candidate_profile(uuid, jsonb) CASCADE;
DROP FUNCTION IF EXISTS get_candidate_applications(uuid) CASCADE;
DROP FUNCTION IF EXISTS get_candidate_interviews(uuid) CASCADE;

-- 8. Drop candidate-related triggers
DROP TRIGGER IF EXISTS candidate_profile_updated_at ON candidate_profiles CASCADE;
DROP TRIGGER IF EXISTS candidate_application_updated_at ON job_applications CASCADE;

-- 9. Update any remaining tables to remove candidate references
-- Update interviews table structure if needed
ALTER TABLE IF EXISTS interviews 
DROP COLUMN IF EXISTS candidate_id CASCADE,
DROP COLUMN IF EXISTS candidate_name CASCADE,
DROP COLUMN IF EXISTS candidate_email CASCADE,
DROP COLUMN IF EXISTS candidate_phone CASCADE,
DROP COLUMN IF EXISTS candidate_photo CASCADE;

-- Update applications table structure if needed  
ALTER TABLE IF EXISTS applications
DROP COLUMN IF EXISTS candidate_id CASCADE;

-- 10. Clean up any candidate-related indexes
DROP INDEX IF EXISTS idx_candidate_profiles_email;
DROP INDEX IF EXISTS idx_candidate_profiles_phone;
DROP INDEX IF EXISTS idx_job_applications_candidate_id;
DROP INDEX IF EXISTS idx_saved_jobs_candidate_id;
DROP INDEX IF EXISTS idx_interviews_candidate_id;

-- 11. Remove candidate-related RLS policies
DROP POLICY IF EXISTS "Candidates can view own profile" ON candidate_profiles;
DROP POLICY IF EXISTS "Candidates can update own profile" ON candidate_profiles;
DROP POLICY IF EXISTS "Candidates can view own applications" ON job_applications;
DROP POLICY IF EXISTS "Candidates can create applications" ON job_applications;
DROP POLICY IF EXISTS "Candidates can view own saved jobs" ON saved_jobs;
DROP POLICY IF EXISTS "Candidates can manage saved jobs" ON saved_jobs;

-- 12. Update any enum types to remove candidate values
-- Note: Be careful with this as it might affect other parts of the system
-- ALTER TYPE user_role DROP VALUE IF EXISTS 'candidate';

-- 13. Clean up any candidate-related storage buckets (if using Supabase Storage)
-- This would need to be done through the Supabase dashboard or storage API
-- DELETE FROM storage.objects WHERE bucket_id = 'candidate-resumes';
-- DELETE FROM storage.objects WHERE bucket_id = 'candidate-photos';

-- 14. Verify cleanup
-- Run these queries to check if cleanup was successful:
-- SELECT COUNT(*) FROM candidate_profiles; -- Should return 0 or error if table doesn't exist
-- SELECT COUNT(*) FROM profiles WHERE role = 'candidate'; -- Should return 0
-- SELECT COUNT(*) FROM job_applications; -- Should return 0 or error if table doesn't exist

COMMIT;
