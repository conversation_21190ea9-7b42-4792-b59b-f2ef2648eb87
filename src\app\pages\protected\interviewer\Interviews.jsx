/**
 * Interviewer Interviews Page
 *
 * Comprehensive interview management interface for interviewers with:
 * - Interview requests management (accept/decline)
 * - Scheduled interviews with calendar integration
 * - Completed interviews with feedback
 * - Real-time updates and notifications
 * - Responsive design with theme support
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Tabs,
  List,
  Avatar,
  Button,
  Tag,
  Space,
  Badge,
  Empty,
  Skeleton,
  Modal,
  message,
  Row,
  Col,
  Statistic,
  Tooltip,
  Divider,
  Input,
  Rate,
  Alert,
  Spin,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  StarOutlined,
  EyeOutlined,
  MessageOutlined,
  VideoCameraOutlined,
  TeamOutlined,
  BankOutlined,
  BookOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import useAuth from '@/hooks/useAuth';
import {
  getInterviewRequests,
  acceptInterviewRequest,
  declineInterviewRequest,
} from '@/services/interviewRequest.service';

dayjs.extend(relativeTime);

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

const InterviewerInterviews = () => {
  const { user, loading: authLoading, isAuthenticated } = useAuth();

  // State management
  const [activeTab, setActiveTab] = useState('requests');
  const [interviews, setInterviews] = useState({
    requests: [],
    scheduled: [],
    completed: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState({});

  // Modal states
  const [detailsModal, setDetailsModal] = useState({
    visible: false,
    interview: null,
  });
  const [declineModal, setDeclineModal] = useState({
    visible: false,
    interview: null,
    reason: '',
  });

  // Statistics
  const [stats, setStats] = useState({
    pending: 0,
    scheduled: 0,
    completed: 0,
    thisWeek: 0,
  });

  // Load interviews data
  const loadInterviews = useCallback(async () => {
    if (!user?.id || !isAuthenticated) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch all interview requests
      const { success, data, error: serviceError } = await getInterviewRequests(user.id);

      if (success) {
        // Categorize interviews by status (mapping database status to UI status)
        const requests = data.filter((interview) => interview.status === 'requested');
        const scheduled = data.filter((interview) => interview.status === 'scheduled');
        const completed = data.filter((interview) => interview.status === 'completed');

        setInterviews({
          requests,
          scheduled,
          completed,
        });

        // Calculate statistics
        const thisWeekStart = dayjs().startOf('week');
        const thisWeekScheduled = scheduled.filter((interview) =>
          dayjs(interview.interviewDate).isAfter(thisWeekStart)
        ).length;

        setStats({
          pending: requests.length,
          scheduled: scheduled.length,
          completed: completed.length,
          thisWeek: thisWeekScheduled,
        });
      } else {
        throw new Error(serviceError || 'Failed to load interviews');
      }
    } catch (error) {
      console.error('Error loading interviews:', error);
      setError(error.message || 'Failed to load interviews');
      message.error('Failed to load interviews');
    } finally {
      setLoading(false);
    }
  }, [user?.id, isAuthenticated]);

  // Load data on component mount and auth changes
  useEffect(() => {
    if (user && isAuthenticated && !authLoading) {
      loadInterviews();
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false);
      setError('Authentication required');
    }
  }, [user, isAuthenticated, authLoading, loadInterviews]);

  // Handle accept interview request
  const handleAcceptRequest = async (interview) => {
    try {
      setActionLoading((prev) => ({ ...prev, [interview.id]: true }));

      const { success, error: serviceError } = await acceptInterviewRequest(interview.id);

      if (success) {
        message.success('Interview request accepted successfully!');
        await loadInterviews(); // Reload data
      } else {
        throw new Error(serviceError || 'Failed to accept interview request');
      }
    } catch (error) {
      console.error('Error accepting interview:', error);
      message.error(error.message || 'Failed to accept interview request');
    } finally {
      setActionLoading((prev) => ({ ...prev, [interview.id]: false }));
    }
  };

  // Handle decline interview request
  const handleDeclineRequest = async () => {
    const { interview, reason } = declineModal;

    try {
      setActionLoading((prev) => ({ ...prev, [interview.id]: true }));

      const { success, error: serviceError } = await declineInterviewRequest(interview.id, reason);

      if (success) {
        message.success('Interview request declined');
        setDeclineModal({ visible: false, interview: null, reason: '' });
        await loadInterviews(); // Reload data
      } else {
        throw new Error(serviceError || 'Failed to decline interview request');
      }
    } catch (error) {
      console.error('Error declining interview:', error);
      message.error(error.message || 'Failed to decline interview request');
    } finally {
      setActionLoading((prev) => ({ ...prev, [interview.id]: false }));
    }
  };

  // Show interview details modal
  const showDetailsModal = (interview) => {
    setDetailsModal({
      visible: true,
      interview,
    });
  };

  // Show decline modal
  const showDeclineModal = (interview) => {
    setDeclineModal({
      visible: true,
      interview,
      reason: '',
    });
  };

  // Format interview date/time
  const formatDateTime = (dateTime) => {
    if (!dateTime) return 'Not scheduled';
    return dayjs(dateTime).format('MMM DD, YYYY at HH:mm');
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'requested':
        return 'orange';
      case 'scheduled':
        return 'blue';
      case 'completed':
        return 'green';
      case 'declined':
        return 'red';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  // Get interview type icon
  const getInterviewTypeIcon = (type) => {
    switch (type) {
      case 'video':
        return <VideoCameraOutlined />;
      case 'phone':
        return <PhoneOutlined />;
      case 'in-person':
        return <TeamOutlined />;
      default:
        return <MessageOutlined />;
    }
  };

  // Render interview item
  const renderInterviewItem = (interview) => {
    const isRequest = interview.status === 'requested';
    const isScheduled = interview.status === 'scheduled';
    const isCompleted = interview.status === 'completed';

    return (
      <List.Item
        key={interview.id}
        actions={[
          <Button
            key="details"
            type="text"
            icon={<EyeOutlined />}
            onClick={() => showDetailsModal(interview)}
          >
            Details
          </Button>,
          ...(isRequest
            ? [
                <Button
                  key="accept"
                  type="primary"
                  size="small"
                  loading={actionLoading[interview.id]}
                  onClick={() => handleAcceptRequest(interview)}
                >
                  Accept
                </Button>,
                <Button
                  key="decline"
                  danger
                  size="small"
                  loading={actionLoading[interview.id]}
                  onClick={() => showDeclineModal(interview)}
                >
                  Decline
                </Button>,
              ]
            : []),
        ]}
      >
        <List.Item.Meta
          avatar={
            <Avatar
              size={48}
              src={interview.candidatePhoto}
              icon={<UserOutlined />}
            />
          }
          title={
            <div className="flex items-center justify-between">
              <div>
                <Text strong>{interview.candidateName}</Text>
                <Tag
                  color={getStatusColor(interview.status)}
                  className="ml-2"
                >
                  {interview.status.toUpperCase()}
                </Tag>
              </div>
              <div className="text-right">
                <Text
                  type="secondary"
                  className="text-sm"
                >
                  {dayjs(interview.createdAt).fromNow()}
                </Text>
              </div>
            </div>
          }
          description={
            <div className="space-y-2">
              <div className="flex items-center space-x-4">
                <Space size="small">
                  <BankOutlined />
                  <Text>{interview.companyName}</Text>
                </Space>
                <Space size="small">
                  <BookOutlined />
                  <Text>{interview.jobTitle}</Text>
                </Space>
                <Space size="small">
                  {getInterviewTypeIcon(interview.interviewType)}
                  <Text className="capitalize">{interview.interviewType || 'Not specified'}</Text>
                </Space>
              </div>

              <div className="flex items-center space-x-4">
                <Space size="small">
                  <CalendarOutlined />
                  <Text>
                    {isScheduled || isCompleted
                      ? formatDateTime(interview.interviewDate)
                      : formatDateTime(interview.preferredDate)}
                  </Text>
                </Space>
                <Space size="small">
                  <ClockCircleOutlined />
                  <Text>{interview.durationMinutes} minutes</Text>
                </Space>
              </div>

              {interview.candidateExperience && (
                <div className="flex items-center space-x-4">
                  <Space size="small">
                    <StarOutlined />
                    <Text>{interview.candidateExperience} years experience</Text>
                  </Space>
                  {interview.candidateLocation && (
                    <Space size="small">
                      <EnvironmentOutlined />
                      <Text>{interview.candidateLocation}</Text>
                    </Space>
                  )}
                </div>
              )}

              {interview.message && (
                <div className="mt-2">
                  <Text
                    type="secondary"
                    className="text-sm"
                  >
                    <MessageOutlined className="mr-1" />
                    {interview.message}
                  </Text>
                </div>
              )}

              {interview.candidateSkills && interview.candidateSkills.length > 0 && (
                <div className="mt-2">
                  <Space wrap>
                    {interview.candidateSkills.slice(0, 3).map((skill, index) => (
                      <Tag
                        key={index}
                        size="small"
                        color="blue"
                      >
                        {skill}
                      </Tag>
                    ))}
                    {interview.candidateSkills.length > 3 && (
                      <Tag
                        size="small"
                        color="default"
                      >
                        +{interview.candidateSkills.length - 3} more
                      </Tag>
                    )}
                  </Space>
                </div>
              )}
            </div>
          }
        />
      </List.Item>
    );
  };

  // Render details modal
  const renderDetailsModal = () => {
    const { interview } = detailsModal;
    if (!interview) return null;

    return (
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <Avatar
              src={interview.candidatePhoto}
              icon={<UserOutlined />}
            />
            <div>
              <Text strong>{interview.candidateName}</Text>
              <br />
              <Text
                type="secondary"
                className="text-sm"
              >
                {interview.jobTitle} at {interview.companyName}
              </Text>
            </div>
          </div>
        }
        open={detailsModal.visible}
        onCancel={() => setDetailsModal({ visible: false, interview: null })}
        footer={null}
        width={600}
      >
        <div className="space-y-4">
          {/* Status and Basic Info */}
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small">
                <Statistic
                  title="Status"
                  value={interview.status.toUpperCase()}
                  valueStyle={{
                    color:
                      getStatusColor(interview.status) === 'green'
                        ? '#52c41a'
                        : getStatusColor(interview.status) === 'blue'
                          ? '#1890ff'
                          : getStatusColor(interview.status) === 'orange'
                            ? '#fa8c16'
                            : '#ff4d4f',
                  }}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small">
                <Statistic
                  title="Interview Type"
                  value={interview.interviewType || 'Not specified'}
                  prefix={getInterviewTypeIcon(interview.interviewType)}
                />
              </Card>
            </Col>
          </Row>

          {/* Contact Information */}
          <Card
            title="Contact Information"
            size="small"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Space
                  direction="vertical"
                  size="small"
                >
                  <div>
                    <Text strong>Email:</Text>
                    <br />
                    <Text copyable>{interview.candidateEmail}</Text>
                  </div>
                  {interview.candidatePhone && (
                    <div>
                      <Text strong>Phone:</Text>
                      <br />
                      <Text copyable>{interview.candidatePhone}</Text>
                    </div>
                  )}
                </Space>
              </Col>
              <Col span={12}>
                <Space
                  direction="vertical"
                  size="small"
                >
                  {interview.candidateLocation && (
                    <div>
                      <Text strong>Location:</Text>
                      <br />
                      <Text>{interview.candidateLocation}</Text>
                    </div>
                  )}
                  {interview.candidateExperience && (
                    <div>
                      <Text strong>Experience:</Text>
                      <br />
                      <Text>{interview.candidateExperience} years</Text>
                    </div>
                  )}
                </Space>
              </Col>
            </Row>
          </Card>

          {/* Interview Details */}
          <Card
            title="Interview Details"
            size="small"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Space
                  direction="vertical"
                  size="small"
                >
                  <div>
                    <Text strong>Preferred Date:</Text>
                    <br />
                    <Text>{formatDateTime(interview.preferredDate)}</Text>
                  </div>
                  <div>
                    <Text strong>Duration:</Text>
                    <br />
                    <Text>{interview.durationMinutes} minutes</Text>
                  </div>
                </Space>
              </Col>
              <Col span={12}>
                <Space
                  direction="vertical"
                  size="small"
                >
                  {interview.interviewDate && (
                    <div>
                      <Text strong>Scheduled Date:</Text>
                      <br />
                      <Text>{formatDateTime(interview.interviewDate)}</Text>
                    </div>
                  )}
                  <div>
                    <Text strong>Created:</Text>
                    <br />
                    <Text>{dayjs(interview.createdAt).format('MMM DD, YYYY')}</Text>
                  </div>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* Skills */}
          {interview.candidateSkills && interview.candidateSkills.length > 0 && (
            <Card
              title="Skills"
              size="small"
            >
              <Space wrap>
                {interview.candidateSkills.map((skill, index) => (
                  <Tag
                    key={index}
                    color="blue"
                  >
                    {skill}
                  </Tag>
                ))}
              </Space>
            </Card>
          )}

          {/* Message */}
          {interview.message && (
            <Card
              title="Message"
              size="small"
            >
              <Paragraph>{interview.message}</Paragraph>
            </Card>
          )}

          {/* Required Skills */}
          {interview.requiredSkills && interview.requiredSkills.length > 0 && (
            <Card
              title="Required Skills for Position"
              size="small"
            >
              <Space wrap>
                {interview.requiredSkills.map((skill, index) => (
                  <Tag
                    key={index}
                    color="green"
                  >
                    {skill}
                  </Tag>
                ))}
              </Space>
            </Card>
          )}
        </div>
      </Modal>
    );
  };

  // Render decline modal
  const renderDeclineModal = () => (
    <Modal
      title="Decline Interview Request"
      open={declineModal.visible}
      onOk={handleDeclineRequest}
      onCancel={() => setDeclineModal({ visible: false, interview: null, reason: '' })}
      confirmLoading={actionLoading[declineModal.interview?.id]}
      okText="Decline"
      okButtonProps={{ danger: true }}
    >
      <div className="space-y-4">
        <Alert
          message="Are you sure you want to decline this interview request?"
          description="This action cannot be undone. The candidate will be notified."
          type="warning"
          showIcon
        />

        <div>
          <Text strong>Reason for declining (optional):</Text>
          <TextArea
            rows={3}
            placeholder="Please provide a reason for declining this interview request..."
            value={declineModal.reason}
            onChange={(e) => setDeclineModal((prev) => ({ ...prev, reason: e.target.value }))}
            className="mt-2"
          />
        </div>
      </div>
    </Modal>
  );

  // Main render
  return (
    <div className="interviewer-interviews-page p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Title
              level={2}
              className="m-0"
            >
              Interviews
            </Title>
            <Text type="secondary">Manage your interview requests and scheduled interviews</Text>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadInterviews}
              loading={loading}
            >
              Refresh
            </Button>
            <Button
              icon={<FilterOutlined />}
              size="large"
            >
              Filters
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row
        gutter={16}
        className="mb-6"
      >
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Pending Requests"
              value={stats.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Scheduled"
              value={stats.scheduled}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Completed"
              value={stats.completed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="This Week"
              value={stats.thisWeek}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      {authLoading || loading ? (
        <Card>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton
                key={index}
                avatar
                active
                paragraph={{ rows: 3 }}
              />
            ))}
          </div>
        </Card>
      ) : error ? (
        <Card>
          <Empty
            description={error}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button
              type="primary"
              onClick={loadInterviews}
              loading={loading}
              icon={<ReloadOutlined />}
            >
              Retry
            </Button>
          </Empty>
        </Card>
      ) : (
        <Card>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            size="large"
            tabBarStyle={{ marginBottom: 24 }}
          >
            <TabPane
              tab={
                <span>
                  <ClockCircleOutlined />
                  Requests
                  {interviews.requests.length > 0 && (
                    <Badge
                      count={interviews.requests.length}
                      offset={[5, -5]}
                    />
                  )}
                </span>
              }
              key="requests"
            >
              <Spin spinning={loading}>
                {interviews.requests.length > 0 ? (
                  <List
                    itemLayout="horizontal"
                    dataSource={interviews.requests}
                    renderItem={renderInterviewItem}
                    pagination={{
                      pageSize: 5,
                      hideOnSinglePage: true,
                    }}
                  />
                ) : (
                  <Empty
                    description="No pending interview requests"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Spin>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CalendarOutlined />
                  Scheduled
                  {interviews.scheduled.length > 0 && (
                    <Badge
                      count={interviews.scheduled.length}
                      offset={[5, -5]}
                    />
                  )}
                </span>
              }
              key="scheduled"
            >
              <Spin spinning={loading}>
                {interviews.scheduled.length > 0 ? (
                  <List
                    itemLayout="horizontal"
                    dataSource={interviews.scheduled}
                    renderItem={renderInterviewItem}
                    pagination={{
                      pageSize: 5,
                      hideOnSinglePage: true,
                    }}
                  />
                ) : (
                  <Empty
                    description="No scheduled interviews"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Spin>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CheckCircleOutlined />
                  Completed
                </span>
              }
              key="completed"
            >
              <Spin spinning={loading}>
                {interviews.completed.length > 0 ? (
                  <List
                    itemLayout="horizontal"
                    dataSource={interviews.completed}
                    renderItem={renderInterviewItem}
                    pagination={{
                      pageSize: 5,
                      hideOnSinglePage: true,
                    }}
                  />
                ) : (
                  <Empty
                    description="No completed interviews"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Spin>
            </TabPane>
          </Tabs>
        </Card>
      )}

      {/* Modals */}
      {renderDetailsModal()}
      {renderDeclineModal()}
    </div>
  );
};

export default InterviewerInterviews;
