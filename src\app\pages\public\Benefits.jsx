import React from 'react';
import { Typo<PERSON>, <PERSON>, Col, Card, Button, Space, Divider } from 'antd';
import {
  RocketOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  SafetyOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph, Text } = Typography;

const Benefits = () => {
  const { isMobile } = useDeviceDetect();

  const keyBenefits = [
    {
      icon: <ClockCircleOutlined className="text-primary text-2xl" />,
      title: 'Time Savings',
      description:
        'Reduce your hiring time by up to 50% with streamlined scheduling and automated workflows.',
    },
    {
      icon: <TeamOutlined className="text-primary text-2xl" />,
      title: 'Better Matches',
      description:
        'Our intelligent matching algorithm connects the right candidates with the right opportunities.',
    },
    {
      icon: <BarChartOutlined className="text-primary text-2xl" />,
      title: 'Data-Driven Decisions',
      description:
        'Gain insights from comprehensive analytics to continuously improve your hiring process.',
    },
    {
      icon: <CheckCircleOutlined className="text-primary text-2xl" />,
      title: 'Improved Quality',
      description:
        'Structured interviews and collaborative feedback lead to better hiring decisions.',
    },
  ];

  const companyBenefits = [
    'Reduce time-to-hire by up to 50%',
    'Lower recruitment costs by 35%',
    'Improve candidate experience and employer brand',
    'Standardize your interview process',
    'Eliminate scheduling headaches',
    'Enhance collaboration among hiring teams',
  ];

  const candidateBenefits = [
    'Schedule interviews at your convenience',
    'Prepare effectively with tailored resources',
    'Reduce interview anxiety with practice sessions',
    'Showcase your skills more effectively',
    'Receive timely feedback and updates',
    'Connect with companies that match your values',
  ];

  return (
    <div className="benefits-page max-w-5xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-12 mb-8">
        <Title
          level={1}
          className="mb-4"
        >
          Platform Benefits
        </Title>
        <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
          Our interview platform delivers tangible benefits for both companies and candidates,
          creating a more efficient, effective, and equitable hiring process.
        </Paragraph>
      </div>

      {/* Key Benefits Section */}
      <div className="key-benefits mb-16">
        <Row gutter={[24, 24]}>
          {keyBenefits.map((benefit, index) => (
            <Col
              xs={24}
              sm={12}
              key={index}
            >
              <Card
                className="benefit-card h-full hover:shadow-md transition-shadow"
                bordered={false}
              >
                <div className="inline-block p-3 bg-secondary rounded-full mb-4">
                  {benefit.icon}
                </div>
                <Title level={4}>{benefit.title}</Title>
                <Paragraph className="text-muted">{benefit.description}</Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* ROI Section */}
      <Card
        className="roi-section mb-16"
        bordered={false}
        style={{ background: 'var(--primary)', color: 'white' }}
      >
        <Row
          gutter={[32, 32]}
          align="middle"
        >
          <Col
            xs={24}
            md={12}
          >
            <div className="p-6">
              <Title
                level={2}
                style={{ color: 'white' }}
                className="mb-4"
              >
                Measurable ROI
              </Title>
              <Paragraph
                style={{ color: 'rgba(255, 255, 255, 0.8)' }}
                className="mb-6"
              >
                Our customers report significant improvements in their hiring metrics after
                implementing our platform:
              </Paragraph>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="text-center">
                    <Title
                      level={1}
                      style={{ color: 'white', margin: 0 }}
                    >
                      50%
                    </Title>
                    <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Reduction in Time-to-Hire
                    </Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="text-center">
                    <Title
                      level={1}
                      style={{ color: 'white', margin: 0 }}
                    >
                      35%
                    </Title>
                    <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Lower Recruitment Costs
                    </Text>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <div className="p-6">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="text-center">
                    <Title
                      level={1}
                      style={{ color: 'white', margin: 0 }}
                    >
                      40%
                    </Title>
                    <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Improved Hiring Quality
                    </Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="text-center">
                    <Title
                      level={1}
                      style={{ color: 'white', margin: 0 }}
                    >
                      90%
                    </Title>
                    <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Candidate Satisfaction
                    </Text>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Card>

      {/* For Companies Section */}
      <div className="for-companies mb-16">
        <Row
          gutter={[32, 32]}
          align="middle"
        >
          <Col
            xs={24}
            md={12}
          >
            <img
              src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
              alt="Companies using the platform"
              className="w-full h-auto rounded-lg shadow-lg"
            />
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <div className="company-benefits">
              <div className="inline-block p-3 bg-secondary rounded-full mb-4">
                <DollarOutlined className="text-primary text-2xl" />
              </div>
              <Title
                level={2}
                className="mb-4"
              >
                For Companies
              </Title>
              <Paragraph className="text-muted mb-6">
                Our platform helps companies streamline their hiring process, reduce costs, and make
                better hiring decisions.
              </Paragraph>
              <ul className="space-y-3 mb-8">
                {companyBenefits.map((benefit, index) => (
                  <li
                    className="flex items-start"
                    key={index}
                  >
                    <CheckCircleOutlined className="mt-1 mr-3 text-primary" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
              <Button
                type="primary"
                size="large"
              >
                Learn More
              </Button>
            </div>
          </Col>
        </Row>
      </div>

      {/* For Candidates Section */}
      <div className="for-candidates mb-16">
        <Row
          gutter={[32, 32]}
          align="middle"
        >
          <Col
            xs={24}
            md={12}
            order={isMobile ? 1 : 2}
          >
            <img
              src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
              alt="Candidates using the platform"
              className="w-full h-auto rounded-lg shadow-lg"
            />
          </Col>
          <Col
            xs={24}
            md={12}
            order={isMobile ? 2 : 1}
          >
            <div className="candidate-benefits">
              <div className="inline-block p-3 bg-secondary rounded-full mb-4">
                <SafetyOutlined className="text-primary text-2xl" />
              </div>
              <Title
                level={2}
                className="mb-4"
              >
                For Candidates
              </Title>
              <Paragraph className="text-muted mb-6">
                Candidates benefit from a streamlined interview experience, better preparation, and
                improved matching with potential employers.
              </Paragraph>
              <ul className="space-y-3 mb-8">
                {candidateBenefits.map((benefit, index) => (
                  <li
                    className="flex items-start"
                    key={index}
                  >
                    <CheckCircleOutlined className="mt-1 mr-3 text-primary" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
              <Button
                type="primary"
                size="large"
              >
                Learn More
              </Button>
            </div>
          </Col>
        </Row>
      </div>

      {/* Global Benefits */}
      <Card
        className="global-benefits mb-16"
        bordered={false}
      >
        <div className="text-center mb-8">
          <div className="inline-block p-3 bg-secondary rounded-full mb-4">
            <GlobalOutlined className="text-primary text-2xl" />
          </div>
          <Title level={2}>Global Impact</Title>
          <Paragraph className="text-muted max-w-2xl mx-auto">
            Our platform is making interviews better worldwide, with users across 50+ countries.
          </Paragraph>
        </div>

        <Row
          gutter={[24, 24]}
          className="text-center"
        >
          <Col
            xs={24}
            sm={8}
          >
            <Title
              level={2}
              className="text-primary"
            >
              25,000+
            </Title>
            <Paragraph className="text-muted">Interviews Conducted</Paragraph>
          </Col>
          <Col
            xs={24}
            sm={8}
          >
            <Title
              level={2}
              className="text-primary"
            >
              500+
            </Title>
            <Paragraph className="text-muted">Companies</Paragraph>
          </Col>
          <Col
            xs={24}
            sm={8}
          >
            <Title
              level={2}
              className="text-primary"
            >
              10,000+
            </Title>
            <Paragraph className="text-muted">Candidates</Paragraph>
          </Col>
        </Row>
      </Card>

      {/* CTA Section */}
      <div className="cta-section text-center mb-12">
        <Title
          level={3}
          className="mb-4"
        >
          Ready to experience these benefits?
        </Title>
        <Paragraph className="text-muted mb-6 max-w-2xl mx-auto">
          Join thousands of companies and candidates who have already transformed their interview
          process.
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
          >
            Get Started Free
          </Button>
          <Button size="large">Schedule a Demo</Button>
        </Space>
      </div>
    </div>
  );
};

export default Benefits;
