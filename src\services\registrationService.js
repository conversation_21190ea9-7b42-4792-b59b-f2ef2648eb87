import { supabase } from '@/utils/supabaseClient';

/**
 * Registration service to handle the new OTP-based registration flow
 */
export class RegistrationService {
  /**
   * Step 1: Sign up user with email and password, sends OTP automatically
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} - Registration result
   */
  static async signUpWithOTP(userData) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            full_name: userData.fullName,
            phone_number: userData.phoneNumber,
            role: userData.role,
            username: userData.fullName,
          },
        },
      });

      if (error) throw error;

      return {
        success: true,
        user: data.user,
        session: data.session,
        message: 'Registration initiated. Please check your email for verification code.',
      };
    } catch (error) {
      if (error.message.includes('User already registered')) {
        throw new Error(
          'This email address is already registered. Please use the login page instead.'
        );
      }

      throw new Error(error.message || 'Registration failed. Please try again.');
    }
  }

  /**
   * Step 2: Verify OTP and complete registration
   * @param {string} email - User's email
   * @param {string} otp - 6-digit OTP code
   * @returns {Promise<Object>} - Verification result
   */
  static async verifyOTPAndCompleteRegistration(email, otp) {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email: email,
        token: otp,
        type: 'email',
      });

      if (error) throw error;

      // After successful verification, create the user profile and mark email as verified
      if (data.user) {
        await this.createUserProfile(data.user);
        // Mark email as verified in our database
        await this.markEmailVerified(data.user.id);
      }

      return {
        success: true,
        user: data.user,
        session: data.session,
        message: 'Email verified successfully!',
      };
    } catch (error) {
      if (error.message.includes('expired')) {
        throw new Error('Verification code has expired. Please request a new one.');
      } else if (error.message.includes('invalid')) {
        throw new Error('Invalid verification code. Please check and try again.');
      }

      throw new Error(error.message || 'Verification failed. Please try again.');
    }
  }

  /**
   * Create user profile after successful email verification
   * @param {Object} user - Supabase user object
   * @returns {Promise<void>}
   */
  static async createUserProfile(user) {
    try {
      console.log('Creating user profile for:', user.id);

      const userData = user.user_metadata || {};
      const role = userData.role || 'company';

      // Create main profile
      const { error: profileError } = await supabase.from('profiles').upsert({
        id: user.id,
        email: user.email,
        username: userData.full_name || userData.username || '',
        phone_number: userData.phone_number || '',
        role: role,
        created_at: new Date(),
      });

      if (profileError) {
        console.error('Error creating main profile:', profileError);
        throw profileError;
      }

      // Create role-specific profile
      await this.createRoleSpecificProfile(user.id, role, userData);

      console.log('User profile created successfully');
    } catch (error) {
      console.error('Error creating user profile:', error);
      // Don't throw here as the main registration was successful
    }
  }

  /**
   * Create role-specific profile
   * @param {string} userId - User ID
   * @param {string} role - User role
   * @param {Object} userData - User metadata
   * @returns {Promise<void>}
   */
  static async createRoleSpecificProfile(userId, role, userData) {
    try {
      // Step 1: Create/update the main profiles table entry
      const { error: profileError } = await supabase.from('profiles').upsert({
        id: userId,
        email: userData.email,
        phone_number: userData.phone_number,
        role: role,
        username: userData.full_name || userData.username,
        email_verified: false, // Will be set to true when user verifies email
        phone_verified: false, // For future implementation
        created_at: new Date(),
      });

      if (profileError) {
        console.error('Error creating main profile:', profileError);
        throw profileError;
      }

      // Step 2: Create role-specific profile entry (without email/phone)
      switch (role) {
        case 'company':
          const { error: companyError } = await supabase.from('company_profiles').upsert({
            id: userId,
            company_name: userData.company_name || null, // Will be filled later in profile completion
            primary_recruiter_name: userData.full_name || null, // Will be filled later in profile completion
            created_at: new Date(),
          });

          if (companyError) {
            console.error('Error creating company profile:', companyError);
            throw companyError;
          }
          break;

        case 'interviewer':
          const { error: interviewerError } = await supabase.from('interviewer_profiles').upsert({
            id: userId,
            full_name: userData.full_name || '',
            created_at: new Date(),
          });

          if (interviewerError) {
            console.error('Error creating interviewer profile:', interviewerError);
            throw interviewerError;
          }
          break;

        default:
          console.warn('Unknown role:', role);
      }
    } catch (error) {
      console.error('Error creating role-specific profile:', error);
      throw error;
    }
  }

  /**
   * Resend OTP to user's email
   * @param {string} email - User's email
   * @returns {Promise<Object>} - Resend result
   */
  static async resendOTP(email) {
    try {
      console.log('Resending OTP to email:', email);

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        console.error('Resend OTP error:', error);
        throw error;
      }

      return {
        success: true,
        message: 'Verification code sent successfully!',
      };
    } catch (error) {
      console.error('Failed to resend OTP:', error);
      throw new Error('Failed to resend verification code. Please try again.');
    }
  }

  /**
   * Mark user's email as verified
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Verification result
   */
  static async markEmailVerified(userId) {
    try {
      console.log('Marking email as verified for user:', userId);

      const { data, error } = await supabase.rpc('mark_email_verified', { user_id: userId });

      if (error) {
        console.error('Mark email verified error:', error);
        throw error;
      }

      return {
        success: true,
        verified: data,
        message: 'Email verified successfully!',
      };
    } catch (error) {
      console.error('Failed to mark email as verified:', error);
      throw new Error('Failed to verify email. Please try again.');
    }
  }

  /**
   * Check if user's email is verified
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Verification status
   */
  static async checkEmailVerification(userId) {
    try {
      const { data, error } = await supabase.rpc('is_email_verified', { user_id: userId });

      if (error) {
        console.error('Check email verification error:', error);
        throw error;
      }

      return {
        success: true,
        isVerified: data || false,
      };
    } catch (error) {
      console.error('Failed to check email verification:', error);
      return {
        success: false,
        isVerified: false,
        error: error.message,
      };
    }
  }

  /**
   * Clean up incomplete registration and allow re-registration
   * @param {string} email - User's email
   * @returns {Promise<Object>} - Cleanup result
   */
  static async cleanupIncompleteRegistration(email) {
    try {
      console.log('Cleaning up incomplete registration for email:', email);

      // Call the cleanup function
      const { data, error } = await supabase.rpc('cleanup_incomplete_registration', {
        email_to_cleanup: email,
      });

      if (error) {
        console.error('Cleanup incomplete registration error:', error);
        throw error;
      }

      return {
        success: true,
        message: 'Incomplete registration cleaned up successfully. You can now register again.',
      };
    } catch (error) {
      console.error('Failed to cleanup incomplete registration:', error);
      throw new Error('Failed to cleanup incomplete registration. Please try again.');
    }
  }
}

export default RegistrationService;
