/**
 * useCalendar Hook
 * 
 * Custom hook for managing calendar operations including:
 * - Google Calendar integration
 * - Local event management
 * - Sync operations
 * - Event CRUD operations
 */

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import {
  initializeGoogleAPI,
  signInToGoogle,
  signOutFromGoogle,
  isSignedInToGoogle,
  getCurrentGoogleUser
} from '@/utils/googleApi';
import {
  getGoogleCalendars,
  getGoogleCalendarEvents,
  createGoogleCalendarEvent,
  updateGoogleCalendarEvent,
  deleteGoogleCalendarEvent,
  formatGoogleEventForLocal
} from '@/services/calendar.service';
import {
  createGoogleMeet,
  updateGoogleMeet,
  deleteGoogleMeet,
  createInstantGoogleMeet
} from '@/services/googleMeet.service';
import useAuth from '@/hooks/useAuth';
import showToast from '@/utils/toast';

const useCalendar = () => {
  const { user } = useAuth();
  
  // State management
  const [events, setEvents] = useState([]);
  const [googleCalendars, setGoogleCalendars] = useState([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState(null);
  const [googleUser, setGoogleUser] = useState(null);
  const [isGoogleConnected, setIsGoogleConnected] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);

  // Initialize Google API on mount
  useEffect(() => {
    const initGoogle = async () => {
      try {
        const initialized = await initializeGoogleAPI();
        if (initialized && isSignedInToGoogle()) {
          const currentUser = getCurrentGoogleUser();
          setGoogleUser(currentUser);
          setIsGoogleConnected(true);
        }
      } catch (error) {
        console.error('Failed to initialize Google API:', error);
      }
    };

    initGoogle();
  }, []);

  // Load events when user changes
  useEffect(() => {
    if (user) {
      loadEvents();
    }
  }, [user]);

  /**
   * Load events from local database
   */
  const loadEvents = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error: supabaseError } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: true });

      if (supabaseError) throw supabaseError;

      setEvents(data || []);
    } catch (err) {
      console.error('Error loading events:', err);
      setError(err.message);
      showToast.error('Failed to load calendar events');
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Connect to Google Calendar
   */
  const connectGoogleCalendar = useCallback(async () => {
    try {
      setLoading(true);
      
      const result = await signInToGoogle();
      if (result.success) {
        setGoogleUser(result.user);
        setIsGoogleConnected(true);
        
        // Load Google calendars
        await loadGoogleCalendars();
        
        showToast.success('Successfully connected to Google Calendar');
        return { success: true };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error connecting to Google Calendar:', error);
      setError(error.message);
      showToast.error('Failed to connect to Google Calendar');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Disconnect from Google Calendar
   */
  const disconnectGoogleCalendar = useCallback(async () => {
    try {
      setLoading(true);
      
      const success = await signOutFromGoogle();
      if (success) {
        setGoogleUser(null);
        setIsGoogleConnected(false);
        setGoogleCalendars([]);
        
        showToast.success('Disconnected from Google Calendar');
        return { success: true };
      } else {
        throw new Error('Failed to sign out');
      }
    } catch (error) {
      console.error('Error disconnecting from Google Calendar:', error);
      setError(error.message);
      showToast.error('Failed to disconnect from Google Calendar');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Load Google calendars
   */
  const loadGoogleCalendars = useCallback(async () => {
    if (!isGoogleConnected) return;

    try {
      const result = await getGoogleCalendars();
      if (result.success) {
        setGoogleCalendars(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error loading Google calendars:', error);
      setError(error.message);
    }
  }, [isGoogleConnected]);

  /**
   * Sync with Google Calendar
   */
  const syncWithGoogleCalendar = useCallback(async () => {
    if (!isGoogleConnected || !user) return;

    setSyncing(true);
    setError(null);

    try {
      // Get events from Google Calendar
      const result = await getGoogleCalendarEvents();
      if (!result.success) {
        throw new Error(result.error);
      }

      const googleEvents = result.data;
      const localEvents = [];

      // Convert Google events to local format
      for (const googleEvent of googleEvents) {
        const localEvent = formatGoogleEventForLocal(googleEvent);
        localEvents.push({
          ...localEvent,
          user_id: user.id,
          google_event_id: googleEvent.id,
          synced_at: new Date().toISOString()
        });
      }

      // Upsert events to local database
      if (localEvents.length > 0) {
        const { error: upsertError } = await supabase
          .from('calendar_events')
          .upsert(localEvents, { 
            onConflict: 'google_event_id',
            ignoreDuplicates: false 
          });

        if (upsertError) throw upsertError;
      }

      // Reload local events
      await loadEvents();
      
      setLastSyncTime(new Date());
      showToast.success('Calendar synced successfully');
      
      return { success: true };
    } catch (error) {
      console.error('Error syncing with Google Calendar:', error);
      setError(error.message);
      showToast.error('Failed to sync calendar');
      return { success: false, error: error.message };
    } finally {
      setSyncing(false);
    }
  }, [isGoogleConnected, user, loadEvents]);

  /**
   * Create a new event
   */
  const createEvent = useCallback(async (eventData, options = {}) => {
    if (!user) return { success: false, error: 'User not authenticated' };

    setLoading(true);
    setError(null);

    try {
      const { syncToGoogle = false, createMeeting = false } = options;
      
      let googleEventId = null;
      let meetingLink = null;

      // Create in Google Calendar if requested and connected
      if (syncToGoogle && isGoogleConnected) {
        if (createMeeting) {
          // Create Google Meet
          const meetResult = await createGoogleMeet({
            title: eventData.title,
            description: eventData.description,
            startTime: new Date(`${eventData.date}T${eventData.time}`).toISOString(),
            endTime: new Date(new Date(`${eventData.date}T${eventData.time}`).getTime() + (eventData.duration * 60000)).toISOString(),
            attendees: eventData.participants || []
          });

          if (meetResult.success) {
            googleEventId = meetResult.data.eventId;
            meetingLink = meetResult.data.meetingLink;
          }
        } else {
          // Create regular calendar event
          const calResult = await createGoogleCalendarEvent(eventData);
          if (calResult.success) {
            googleEventId = calResult.data.id;
          }
        }
      }

      // Save to local database
      const localEvent = {
        ...eventData,
        user_id: user.id,
        google_event_id: googleEventId,
        meeting_link: meetingLink,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error: insertError } = await supabase
        .from('calendar_events')
        .insert(localEvent)
        .select()
        .single();

      if (insertError) throw insertError;

      // Reload events
      await loadEvents();
      
      showToast.success('Event created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('Error creating event:', error);
      setError(error.message);
      showToast.error('Failed to create event');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [user, isGoogleConnected, loadEvents]);

  /**
   * Update an existing event
   */
  const updateEvent = useCallback(async (eventId, eventData, options = {}) => {
    if (!user) return { success: false, error: 'User not authenticated' };

    setLoading(true);
    setError(null);

    try {
      const { syncToGoogle = false } = options;

      // Get existing event
      const { data: existingEvent, error: fetchError } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('id', eventId)
        .eq('user_id', user.id)
        .single();

      if (fetchError) throw fetchError;

      // Update in Google Calendar if synced
      if (syncToGoogle && isGoogleConnected && existingEvent.google_event_id) {
        const result = await updateGoogleCalendarEvent(existingEvent.google_event_id, eventData);
        if (!result.success) {
          console.warn('Failed to update Google Calendar event:', result.error);
        }
      }

      // Update in local database
      const { data, error: updateError } = await supabase
        .from('calendar_events')
        .update({
          ...eventData,
          updated_at: new Date().toISOString()
        })
        .eq('id', eventId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (updateError) throw updateError;

      // Reload events
      await loadEvents();
      
      showToast.success('Event updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('Error updating event:', error);
      setError(error.message);
      showToast.error('Failed to update event');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [user, isGoogleConnected, loadEvents]);

  /**
   * Delete an event
   */
  const deleteEvent = useCallback(async (eventId, options = {}) => {
    if (!user) return { success: false, error: 'User not authenticated' };

    setLoading(true);
    setError(null);

    try {
      const { syncToGoogle = false } = options;

      // Get existing event
      const { data: existingEvent, error: fetchError } = await supabase
        .from('calendar_events')
        .select('*')
        .eq('id', eventId)
        .eq('user_id', user.id)
        .single();

      if (fetchError) throw fetchError;

      // Delete from Google Calendar if synced
      if (syncToGoogle && isGoogleConnected && existingEvent.google_event_id) {
        const result = await deleteGoogleCalendarEvent(existingEvent.google_event_id);
        if (!result.success) {
          console.warn('Failed to delete Google Calendar event:', result.error);
        }
      }

      // Delete from local database
      const { error: deleteError } = await supabase
        .from('calendar_events')
        .delete()
        .eq('id', eventId)
        .eq('user_id', user.id);

      if (deleteError) throw deleteError;

      // Reload events
      await loadEvents();
      
      showToast.success('Event deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting event:', error);
      setError(error.message);
      showToast.error('Failed to delete event');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [user, isGoogleConnected, loadEvents]);

  /**
   * Create instant meeting
   */
  const createInstantMeeting = useCallback(async (meetingData) => {
    if (!isGoogleConnected) {
      return { success: false, error: 'Google Calendar not connected' };
    }

    try {
      setLoading(true);
      
      const result = await createInstantGoogleMeet(meetingData);
      if (result.success) {
        showToast.success('Instant meeting created');
        return result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error creating instant meeting:', error);
      showToast.error('Failed to create instant meeting');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [isGoogleConnected]);

  return {
    // State
    events,
    googleCalendars,
    loading,
    syncing,
    error,
    googleUser,
    isGoogleConnected,
    lastSyncTime,

    // Actions
    loadEvents,
    connectGoogleCalendar,
    disconnectGoogleCalendar,
    syncWithGoogleCalendar,
    createEvent,
    updateEvent,
    deleteEvent,
    createInstantMeeting,

    // Utilities
    clearError: () => setError(null)
  };
};

export default useCalendar;
