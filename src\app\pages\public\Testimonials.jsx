import React from 'react';
import { Typo<PERSON>, Row, Col, Card, Avatar, Rate, Tabs, Button } from 'antd';
import { UserOutlined, StarOutlined, MessageOutlined, PlayCircleOutlined } from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const Testimonials = () => {
  const { isMobile } = useDeviceDetect();

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'HR Director, TechCorp',
      content:
        'This platform has cut our hiring time in half while improving the quality of our interviews. The collaborative feedback tools have transformed how our hiring teams make decisions.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      rating: 5,
      category: 'company',
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Software Engineer',
      content:
        'As a candidate, I loved how streamlined the interview process was. The preparation resources were invaluable and helped me feel confident during my interviews.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      rating: 5,
      category: 'candidate',
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      role: 'Talent Acquisition, StartupX',
      content:
        "The collaborative feedback tools have transformed how our hiring teams make decisions. We've seen a 40% improvement in our hiring success rate since implementing this platform.",
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      rating: 5,
      category: 'company',
    },
    {
      id: 4,
      name: '<PERSON>',
      role: 'Product Manager',
      content:
        'The interview scheduling feature alone saved me countless hours of back-and-forth emails. The platform is intuitive and made the entire process stress-free.',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      rating: 4,
      category: 'candidate',
    },
    {
      id: 5,
      name: 'Jennifer Lopez',
      role: 'Recruiting Manager, Enterprise Co.',
      content:
        "We've interviewed over 200 candidates using this platform, and it has completely transformed our hiring process. The analytics and reporting features help us continuously improve.",
      avatar: 'https://randomuser.me/api/portraits/women/28.jpg',
      rating: 5,
      category: 'company',
    },
    {
      id: 6,
      name: 'Robert Kim',
      role: 'UX Designer',
      content:
        'The practice interviews and feedback helped me improve my interview skills dramatically. I landed my dream job after using this platform for just two weeks!',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      rating: 5,
      category: 'candidate',
    },
  ];

  const featuredTestimonials = testimonials.filter((t) => t.rating === 5).slice(0, 3);

  const renderTestimonialCard = (testimonial) => (
    <Card
      className="testimonial-card h-full hover:shadow-lg transition-shadow"
      bordered={false}
      key={testimonial.id}
    >
      <div className="flex items-center mb-4">
        <Rate
          disabled
          defaultValue={testimonial.rating}
          className="text-warning"
        />
      </div>
      <Paragraph className="text-lg mb-6">"{testimonial.content}"</Paragraph>
      <div className="flex items-center">
        <Avatar
          src={testimonial.avatar}
          size={48}
          className="mr-4"
        />
        <div>
          <Text
            strong
            className="block"
          >
            {testimonial.name}
          </Text>
          <Text className="text-muted">{testimonial.role}</Text>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="testimonials-page max-w-5xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-12 mb-8">
        <Title
          level={1}
          className="mb-4"
        >
          What Our Users Say
        </Title>
        <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
          Hear from the companies and candidates who have transformed their interview process with
          our platform.
        </Paragraph>
      </div>

      {/* Featured Testimonials */}
      <div className="featured-testimonials mb-16">
        <Row gutter={[24, 24]}>
          {featuredTestimonials.map((testimonial) => (
            <Col
              xs={24}
              md={8}
              key={testimonial.id}
            >
              {renderTestimonialCard(testimonial)}
            </Col>
          ))}
        </Row>
      </div>

      {/* Video Testimonial */}
      <Card
        className="video-testimonial mb-16"
        bordered={false}
        style={{ background: 'var(--primary)', color: 'white' }}
      >
        <Row
          gutter={[32, 32]}
          align="middle"
        >
          <Col
            xs={24}
            md={12}
          >
            <div className="p-6">
              <Title
                level={3}
                style={{ color: 'white' }}
                className="mb-4"
              >
                See Our Platform in Action
              </Title>
              <Paragraph
                style={{ color: 'rgba(255, 255, 255, 0.8)' }}
                className="mb-6"
              >
                Watch how TechCorp transformed their interview process and improved hiring outcomes
                by 35% using our platform.
              </Paragraph>
              <Button
                size="large"
                icon={<PlayCircleOutlined />}
                style={{
                  background: 'white',
                  color: 'var(--primary)',
                  borderColor: 'white',
                  fontWeight: 500,
                }}
              >
                Watch Video
              </Button>
            </div>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <div className="video-thumbnail relative cursor-pointer">
              <img
                src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
                alt="Video thumbnail"
                className="w-full h-auto rounded-lg shadow-lg"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-white bg-opacity-80 rounded-full p-4">
                  <PlayCircleOutlined style={{ fontSize: 48, color: 'var(--primary)' }} />
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Categorized Testimonials */}
      <div className="categorized-testimonials mb-16">
        <Title
          level={2}
          className="text-center mb-8"
        >
          Success Stories
        </Title>

        <Tabs
          defaultActiveKey="all"
          centered
          className="testimonial-tabs"
        >
          <TabPane
            tab="All Testimonials"
            key="all"
          >
            <Row gutter={[24, 24]}>
              {testimonials.map((testimonial) => (
                <Col
                  xs={24}
                  sm={12}
                  lg={8}
                  key={testimonial.id}
                >
                  {renderTestimonialCard(testimonial)}
                </Col>
              ))}
            </Row>
          </TabPane>
          <TabPane
            tab="Companies"
            key="company"
          >
            <Row gutter={[24, 24]}>
              {testimonials
                .filter((t) => t.category === 'company')
                .map((testimonial) => (
                  <Col
                    xs={24}
                    sm={12}
                    lg={8}
                    key={testimonial.id}
                  >
                    {renderTestimonialCard(testimonial)}
                  </Col>
                ))}
            </Row>
          </TabPane>
          <TabPane
            tab="Candidates"
            key="candidate"
          >
            <Row gutter={[24, 24]}>
              {testimonials
                .filter((t) => t.category === 'candidate')
                .map((testimonial) => (
                  <Col
                    xs={24}
                    sm={12}
                    lg={8}
                    key={testimonial.id}
                  >
                    {renderTestimonialCard(testimonial)}
                  </Col>
                ))}
            </Row>
          </TabPane>
        </Tabs>
      </div>

      {/* CTA Section */}
      <Card
        className="cta-section mb-12"
        bordered={false}
      >
        <div className="text-center py-8">
          <Title
            level={3}
            className="mb-4"
          >
            Ready to Transform Your Interview Process?
          </Title>
          <Paragraph className="text-muted mb-8 max-w-2xl mx-auto">
            Join thousands of companies and candidates who have already improved their interview
            experience.
          </Paragraph>
          <Button
            type="primary"
            size="large"
          >
            Get Started Free
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default Testimonials;
