import React, { useState } from 'react';
import { Card, Form, Select, Input, Button, Slider, Space, Checkbox, Divider } from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  EnvironmentOutlined,
  BookOutlined,
  BankOutlined,
} from '@ant-design/icons';

const { Option } = Select;

// Rupee Icon Component
const RupeeIcon = () => <span style={{ fontWeight: 'bold', fontSize: '14px' }}>₹</span>;

// Constants for filter options
const EXPERIENCE_LEVELS = [
  'Fresher (0-1 years)',
  'Junior (1-3 years)',
  'Mid-level (3-5 years)',
  'Senior (5-8 years)',
  'Lead (8+ years)',
  'Manager (10+ years)',
];

const EMPLOYMENT_TYPES = [
  'Full-time',
  'Part-time',
  'Contract',
  'Freelance',
  'Internship',
  'Remote',
];

const COMPANY_TYPES = [
  'Startup',
  'MNC',
  'Product Company',
  'Service Company',
  'Government',
  'Non-profit',
];

const COMPANY_SIZES = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '501-1000 employees',
  '1000+ employees',
];

const POPULAR_LOCATIONS = [
  'Bangalore',
  'Mumbai',
  'Delhi',
  'Hyderabad',
  'Chennai',
  'Pune',
  'Kolkata',
  'Ahmedabad',
  'Remote',
  'Work from Home',
];

const JobFilters = ({ onFilter, initialValues = {}, loading = false, isMobile = false }) => {
  const [form] = Form.useForm();
  const [salaryRange, setSalaryRange] = useState([0, 50]);
  const [activeFilters, setActiveFilters] = useState(0);

  const handleReset = () => {
    form.resetFields();
    setSalaryRange([0, 50]);
    setActiveFilters(0);
    onFilter({});
  };

  const handleFinish = (values) => {
    // Convert salary range to actual values
    const filters = {
      ...values,
      salary_min: salaryRange[0] * 100000, // Convert to actual salary
      salary_max: salaryRange[1] * 100000,
    };

    // Count active filters
    const count = Object.values(filters).filter(
      (value) =>
        value !== undefined &&
        value !== null &&
        value !== '' &&
        (Array.isArray(value) ? value.length > 0 : true)
    ).length;

    setActiveFilters(count);
    onFilter(filters);
  };

  return (
    <Card className="mb-4 shadow-sm">
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleFinish}
      >
        <Form.Item
          name="keyword"
          label="Search"
        >
          <Input
            prefix={<SearchOutlined />}
            placeholder="Job title, skills, or keywords"
            allowClear
          />
        </Form.Item>

        <Form.Item
          name="location"
          label="Location"
        >
          <Input
            placeholder="City, state, or remote"
            allowClear
          />
        </Form.Item>

        <Divider />

        <Form.Item
          name="experience_level"
          label="Experience Level"
        >
          <Select
            placeholder="Select experience level"
            allowClear
          >
            {EXPERIENCE_LEVELS.map((level) => (
              <Option
                key={level}
                value={level}
              >
                {level}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="employment_type"
          label={
            <span className="flex items-center gap-2">
              <BookOutlined />
              Employment Type
            </span>
          }
        >
          <Select
            placeholder="Select employment type"
            allowClear
          >
            {EMPLOYMENT_TYPES.map((type) => (
              <Option
                key={type}
                value={type}
              >
                {type}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label={
            <span className="flex items-center gap-2">
              <RupeeIcon />
              Salary Range (LPA)
            </span>
          }
        >
          <div className="px-2">
            <Slider
              range
              min={0}
              max={50}
              value={salaryRange}
              onChange={setSalaryRange}
              marks={{
                0: '₹0L',
                10: '₹10L',
                20: '₹20L',
                30: '₹30L',
                40: '₹40L',
                50: '₹50L+',
              }}
              tooltip={{
                formatter: (value) => `₹${value}L`,
              }}
            />
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>₹{salaryRange[0]}L</span>
              <span>₹{salaryRange[1]}L+</span>
            </div>
          </div>
        </Form.Item>

        <Form.Item
          name="company_type"
          label={
            <span className="flex items-center gap-2">
              <BankOutlined />
              Company Type
            </span>
          }
        >
          <Select
            placeholder="Select company type"
            allowClear
          >
            {COMPANY_TYPES.map((type) => (
              <Option
                key={type}
                value={type}
              >
                {type}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="posted_within"
          label="Posted Within"
        >
          <Select
            placeholder="Select time period"
            allowClear
          >
            <Option value="24h">Last 24 hours</Option>
            <Option value="3d">Last 3 days</Option>
            <Option value="7d">Last 7 days</Option>
            <Option value="14d">Last 14 days</Option>
            <Option value="30d">Last 30 days</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="remote_friendly"
          valuePropName="checked"
        >
          <Checkbox>Remote-friendly companies only</Checkbox>
        </Form.Item>

        <div className="mt-6">
          <Space
            className="w-full"
            direction={isMobile ? 'horizontal' : 'vertical'}
          >
            <Button
              type="primary"
              htmlType="submit"
              icon={<FilterOutlined />}
              loading={loading}
              block={!isMobile}
              size="large"
            >
              Apply Filters {activeFilters > 0 && `(${activeFilters})`}
            </Button>
            <Button
              onClick={handleReset}
              icon={<ClearOutlined />}
              block={!isMobile}
              size="large"
            >
              Clear All
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  );
};

export default JobFilters;
