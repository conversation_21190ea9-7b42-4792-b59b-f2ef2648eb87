import React, { useState, useEffect, useRef } from 'react';
import { Typography, Table, Tag, Space, Button, Card, Tabs, Empty, Skeleton, Badge } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  EyeOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// Simple cache with 5-minute TTL
const applicationsCache = {
  data: null,
  timestamp: null,
  isValid: () => {
    if (!applicationsCache.timestamp) return false;
    return Date.now() - applicationsCache.timestamp < 5 * 60 * 1000; // 5 minutes
  },
  set: (data) => {
    applicationsCache.data = data;
    applicationsCache.timestamp = Date.now();
  },
  get: () => applicationsCache.data,
  clear: () => {
    applicationsCache.data = null;
    applicationsCache.timestamp = null;
  },
};

const Applications = () => {
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const { user } = useAuth();
  const fetchingRef = useRef(false);

  const fetchApplications = async () => {
    if (!user?.id) return;

    // Use cache if valid
    if (applicationsCache.isValid()) {
      setApplications(applicationsCache.get());
      setLoading(false);
      return;
    }

    // Prevent duplicate fetches
    if (fetchingRef.current) return;
    fetchingRef.current = true;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('applications')
        .select(
          `
          *,
          jobs:job_id (
            id,
            title,
            company_id,
            location,
            status,
            company_profiles:company_id (
              company_name,
              company_logo_url
            )
          )
        `
        )
        .eq('candidate_id', user.id)
        .order('application_date', { ascending: false });

      if (error) throw error;

      const apps = data || [];
      setApplications(apps);
      applicationsCache.set(apps);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  };

  useEffect(() => {
    fetchApplications();
  }, [user]);

  const getStatusTag = (status) => {
    switch (status) {
      case 'applied':
        return (
          <Tag
            icon={<ClockCircleOutlined />}
            color="blue"
          >
            Applied
          </Tag>
        );
      case 'shortlisted':
        return (
          <Tag
            icon={<FileTextOutlined />}
            color="orange"
          >
            Shortlisted
          </Tag>
        );
      case 'interviewed':
        return (
          <Tag
            icon={<EyeOutlined />}
            color="purple"
          >
            Interviewed
          </Tag>
        );
      case 'hired':
        return (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
          >
            Hired
          </Tag>
        );
      case 'rejected':
        return (
          <Tag
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Rejected
          </Tag>
        );
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const columns = [
    {
      title: 'Job Title',
      dataIndex: ['jobs', 'title'],
      key: 'title',
      render: (text, record) => (
        <Space
          direction="vertical"
          size={0}
        >
          <Text strong>{text}</Text>
          <Text type="secondary">{record.jobs?.company_profiles?.company_name}</Text>
        </Space>
      ),
    },
    {
      title: 'Location',
      dataIndex: ['jobs', 'location'],
      key: 'location',
    },
    {
      title: 'Applied On',
      dataIndex: 'application_date',
      key: 'application_date',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
          >
            View Details
          </Button>
          {record.status === 'shortlisted' && (
            <Button
              type="primary"
              size="small"
            >
              Schedule Interview
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const filteredApplications =
    activeTab === 'all' ? applications : applications.filter((app) => app.status === activeTab);

  return (
    <div className="applications-page">
      <Title
        level={2}
        className="mb-6"
      >
        My Applications
      </Title>

      <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="mb-4"
        >
          <TabPane
            tab={
              <Badge
                count={applications.length}
                offset={[10, 0]}
              >
                All Applications
              </Badge>
            }
            key="all"
          />
          <TabPane
            tab="Applied"
            key="applied"
          />
          <TabPane
            tab="Shortlisted"
            key="shortlisted"
          />
          <TabPane
            tab="Interviewed"
            key="interviewed"
          />
          <TabPane
            tab="Hired"
            key="hired"
          />
          <TabPane
            tab="Rejected"
            key="rejected"
          />
        </Tabs>

        {loading ? (
          <Skeleton
            active
            paragraph={{ rows: 5 }}
          />
        ) : filteredApplications.length > 0 ? (
          <Table
            columns={columns}
            dataSource={filteredApplications}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        ) : (
          <Empty
            description={
              <span>
                No applications found. <a href="/candidate/jobs">Browse jobs</a> to apply.
              </span>
            }
          />
        )}
      </Card>
    </div>
  );
};

export default Applications;
