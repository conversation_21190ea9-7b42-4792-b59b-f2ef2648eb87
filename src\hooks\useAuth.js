import { useCallback, useEffect } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-react';
import useAuthStore from '@/store/auth.store';
import { supabase } from '@/utils/supabaseClient';

/**
 * Optimized useAuth Hook with Clerk Authentication
 *
 * This hook handles all authentication services and provides a clean interface
 * to the auth store for state management.
 *
 * Architecture:
 * - Auth Store: Pure state management using Zustand (no service calls)
 * - This Hook: Handles all Clerk authentication and profile logic
 * - Role Stores: Handle role-specific business logic (company, interviewer)
 *
 * Responsibilities:
 * - Authentication services (login, logout, session management) via Clerk
 * - Profile services (fetch, save, update) via Supabase
 * - User role detection and management
 * - Interface to auth store for state management
 */
export const useAuth = () => {
  // Clerk hooks
  const { user: clerkUser, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const { signOut } = useClerkAuth();

  // All state from auth store (pure state management)
  const authStore = useAuthStore();
  const {
    user,
    role,
    rolePath,
    profile,
    profileCompletion,
    loading,
    error,
    // State setters
    setUser,
    setRole,
    setProfile,
    updateProfile: updateProfileState,
    setLoading,
    setError,
    clearError,
    // Cache management
    updateProfileCache,
    isProfileCacheValid,
    // Initialization
    setInitialized,
    isInitialized,
    // Reset
    resetAuth,
  } = authStore;

  // === AUTHENTICATION SERVICES ===
  // Handle authenticated user with Clerk
  const handleAuthenticatedUser = useCallback(
    async (clerkUser) => {
      if (!clerkUser) return;

      // Convert Clerk user to our user format
      const user = {
        id: clerkUser.id,
        email: clerkUser.primaryEmailAddress?.emailAddress,
        full_name: clerkUser.fullName,
        first_name: clerkUser.firstName,
        last_name: clerkUser.lastName,
        image_url: clerkUser.imageUrl,
      };

      setUser(user);

      // Get role from Clerk metadata or database
      let userRole = clerkUser.publicMetadata?.role || clerkUser.unsafeMetadata?.role;

      if (!userRole) {
        const { data: profileData } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', clerkUser.id)
          .single();

        userRole = profileData?.role;
      }

      if (userRole) {
        setRole(userRole);

        // Fetch profile data after setting role
        try {
          const viewName = `${userRole}_profiles_complete`;

          // Try complete profile first
          const { data: completeProfile, error: viewError } = await supabase
            .from(viewName)
            .select('*')
            .eq('id', clerkUser.id)
            .single();

          let profileData = completeProfile;

          // Fallback to basic profile if view fails
          if (viewError && viewError.code === 'PGRST116') {
            const { data: basicProfile } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', clerkUser.id)
              .single();

            profileData = basicProfile || {
              id: clerkUser.id,
              email: user.email,
              role: userRole,
              created_at: new Date(),
            };
          }

          if (profileData) {
            setProfile(profileData);
            updateProfileCache();
          }
        } catch (error) {
          console.error('Error fetching profile during auth:', error);
          // Don't throw here, just log the error
        }
      } else {
        // Default role if none found
        userRole = 'company';
        setRole(userRole);
      }

      setLoading(false);
    },
    [setUser, setRole, setProfile, updateProfileCache, setLoading]
  );

  // Handle Clerk user changes
  useEffect(() => {
    if (!clerkLoaded) return;

    if (isSignedIn && clerkUser) {
      handleAuthenticatedUser(clerkUser);
    } else if (!isSignedIn) {
      // User signed out
      resetAuth();
    }
  }, [clerkLoaded, isSignedIn, clerkUser, handleAuthenticatedUser, resetAuth]);

  // Session validation is handled by Clerk automatically
  const validateSession = useCallback(async () => {
    // Clerk handles session validation automatically
    return isSignedIn;
  }, [isSignedIn]);

  // Initialize authentication with Clerk
  const initializeAuth = useCallback(async () => {
    // Skip if already initialized
    if (isInitialized()) return;

    // Clerk handles initialization automatically
    // We just need to mark as initialized once Clerk is loaded
    if (clerkLoaded) {
      setInitialized(true);
      setLoading(false);
    }
  }, [clerkLoaded, isInitialized, setInitialized, setLoading]);

  // Login function - Clerk handles this via components
  const login = useCallback(async (email, password) => {
    // Clerk handles login via SignIn component
    // This is kept for compatibility but should use Clerk components
    console.warn('Use Clerk SignIn component for authentication');
    return { success: false, message: 'Use Clerk SignIn component for authentication' };
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    setLoading(true);
    try {
      await signOut();
      resetAuth();
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      setError(error.message);
      resetAuth();
      return { success: false, message: error.message };
    } finally {
      setLoading(false);
    }
  }, [signOut, resetAuth, setError, setLoading]);

  // === PROFILE SERVICES ===

  // Fetch profile
  const fetchProfile = useCallback(
    async (userId, forceRefresh = false, skipLoadingState = false) => {
      if (!userId || !role) return null;

      // Check cache validity unless force refresh
      if (!forceRefresh && profile && isProfileCacheValid()) {
        return profile;
      }

      if (!skipLoadingState) {
        setLoading(true);
        clearError();
      }

      try {
        const viewName = `${role}_profiles_complete`;

        // Try complete profile first
        const { data: completeProfile, error: viewError } = await supabase
          .from(viewName)
          .select('*')
          .eq('id', userId)
          .single();

        let profileData = completeProfile;

        // Fallback to basic profile if view fails
        if (viewError && viewError.code === 'PGRST116') {
          const { data: basicProfile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

          profileData = basicProfile || {
            id: userId,
            email: user?.email,
            role: role,
            created_at: new Date(),
          };
        }

        setProfile(profileData);
        updateProfileCache();

        if (!skipLoadingState) {
          setLoading(false);
        }

        return profileData;
      } catch (error) {
        console.error('Error fetching profile:', error);
        if (!skipLoadingState) {
          setError(error.message);
          setLoading(false);
        }
        return null;
      }
    },
    [
      role,
      profile,
      isProfileCacheValid,
      setLoading,
      clearError,
      setProfile,
      updateProfileCache,
      setError,
      user?.email,
    ]
  );

  // Save profile
  const saveProfile = useCallback(
    async (userId, profileData) => {
      if (!userId || !role) return { success: false, error: 'Missing user ID or role' };

      setLoading(true);
      clearError();

      try {
        // Step 1: Update/insert profiles table (common fields)
        const { error: profileError } = await supabase.from('profiles').upsert({
          id: userId,
          email: profileData.email,
          phone_number: profileData.phone_number,
          role: role,
          username: profileData.username || profileData.full_name || profileData.company_name,
          updated_at: new Date(),
        });

        if (profileError) throw profileError;

        // Step 2: Update/insert role-specific data
        const roleTableMap = {
          company: 'company_profiles',
          interviewer: 'interviewer_profiles',
        };

        const roleTableName = roleTableMap[role];
        if (!roleTableName) throw new Error(`Unknown role: ${role}`);

        const roleData = { ...profileData };
        // Remove common fields from role-specific data
        delete roleData.email;
        delete roleData.phone_number;
        delete roleData.role;
        delete roleData.username;

        // Check if role-specific profile exists
        const { data: existingProfile } = await supabase
          .from(roleTableName)
          .select('id')
          .eq('id', userId)
          .single();

        const roleOperation = existingProfile
          ? supabase
              .from(roleTableName)
              .update({ ...roleData, updated_at: new Date() })
              .eq('id', userId)
          : supabase
              .from(roleTableName)
              .insert({ id: userId, ...roleData, created_at: new Date() });

        const { error: roleError } = await roleOperation;
        if (roleError) throw roleError;

        // Refresh profile data from database (without triggering loading state)
        const refreshedProfile = await fetchProfile(userId, true, true);

        setLoading(false);
        return { success: true, data: refreshedProfile };
      } catch (error) {
        console.error('Error saving profile:', error);
        setError(error.message);
        setLoading(false);
        return { success: false, error: error.message };
      }
    },
    [role, setLoading, clearError, setError, fetchProfile]
  );

  // Update profile (local state only)
  const updateProfile = useCallback(
    (updates) => {
      updateProfileState(updates);
    },
    [updateProfileState]
  );

  return {
    // Auth state
    user,
    role,
    isAuthenticated: isSignedIn && !!user,
    clerkUser, // Expose Clerk user for advanced usage
    clerkLoaded,

    // User profile state
    profile,
    profileCompletion,
    rolePath,

    // Combined UI state
    loading: loading || !clerkLoaded,
    error,

    // Initialization state
    isInitialized: isInitialized() && clerkLoaded,

    // Auth methods
    login,
    logout,
    initializeAuth,
    validateSession,

    // Profile methods
    fetchProfile,
    saveProfile,
    updateProfile,

    // Store methods for advanced usage
    resetAuth,
  };
};

export default useAuth;
