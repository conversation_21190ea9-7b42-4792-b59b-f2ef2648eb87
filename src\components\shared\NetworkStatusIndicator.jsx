/**
 * Simple network status indicator that appears at the bottom center of the screen
 * Shows when network is offline and hides 5 seconds after coming back online
 * @returns {Object} Network status information
 * @returns {boolean} isOnline - Whether the user is currently online
 * @returns {boolean} showStatus - Whether to show the status indicator
 */

import { useEffect, useState } from 'react';
import { WifiOutlined, DisconnectOutlined } from '@ant-design/icons';
import { useColorModeStore } from '@/store/colorMode.store';

const NetworkStatusIndicator = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showStatus, setShowStatus] = useState(false);
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  useEffect(() => {
    // Function to handle online status change
    const handleOnline = () => {
      setIsOnline(true);
      // Keep showing the status for 5 seconds after coming back online
      setTimeout(() => {
        setShowStatus(false);
      }, 5000);
    };

    // Function to handle offline status change
    const handleOffline = () => {
      setIsOnline(false);
      setShowStatus(true);
    };

    // Initialize showStatus based on current online status
    setShowStatus(!navigator.onLine);

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // // If we shouldn't show the status, don't render anything
  if (!showStatus) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1000,
        padding: '8px 16px',
        borderRadius: '20px',
        backgroundColor: isOnline
          ? isDark
            ? '#162312'
            : '#f6ffed'
          : isDark
            ? '#2a0d0d'
            : '#fff1f0',
        color: isOnline ? (isDark ? '#52c41a' : '#389e0d') : isDark ? '#ff4d4f' : '#cf1322',
        border: `1px solid ${
          isOnline ? (isDark ? '#274916' : '#b7eb8f') : isDark ? '#5c2626' : '#ffccc7'
        }`,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '8px',
        transition: 'all 0.3s ease-in-out',
        maxWidth: '90%',
        fontWeight: 500,
      }}
    >
      {isOnline ? (
        <>
          <WifiOutlined style={{ fontSize: '16px' }} />
          <span>Connection restored</span>
        </>
      ) : (
        <>
          <DisconnectOutlined style={{ fontSize: '16px' }} />
          <span>You are currently offline</span>
        </>
      )}
    </div>
  );
};

export default NetworkStatusIndicator;
