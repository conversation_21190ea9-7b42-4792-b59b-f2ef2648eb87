/**
 * Recommended Jobs Section Component for Candidate Dashboard
 */
import React from 'react';
import { Card, Row, Col, Button, Empty, Skeleton, Typography, Progress } from 'antd';
import { Target } from 'lucide-react';
import JobCard from '../jobs/JobCard';

const { Title } = Typography;

/**
 * RecommendedJobsSection component for displaying jobs recommended based on profile
 * @param {Object} props
 * @param {Array} props.jobs - Array of recommended job objects
 * @param {boolean} props.loading - Loading state
 * @param {Function} props.onViewJob - Handler for viewing job details
 * @param {Function} props.onApplyJob - Handler for applying to job
 * @param {Function} props.onSaveJob - Handler for saving job
 * @param {Function} props.onViewAllJobs - Handler for viewing all jobs
 * @param {Array} props.appliedJobs - Array of applied job IDs
 * @param {Array} props.savedJobs - Array of saved job IDs
 * @returns {JSX.Element}
 */
const RecommendedJobsSection = ({
  jobs = [],
  loading = false,
  onView<PERSON>ob,
  onApply<PERSON><PERSON>,
  onSave<PERSON><PERSON>,
  onViewAllJobs,
  appliedJobs = [],
  savedJobs = [],
}) => {
  const isJobApplied = (jobId) => {
    return appliedJobs.some((app) => app.job_id === jobId);
  };

  const isJobSaved = (jobId) => {
    return savedJobs.some((saved) => saved.job_id === jobId);
  };

  const getMatchColor = (percentage) => {
    if (percentage >= 90) return '#52c41a';
    if (percentage >= 80) return '#1890ff';
    if (percentage >= 70) return '#faad14';
    return '#f5222d';
  };

  if (loading) {
    return (
      <Card
        title={
          <div className="flex items-center gap-2">
            <Target
              size={20}
              className="text-blue-600"
            />
            <Title
              level={4}
              className="mb-0"
            >
              Recommended for You
            </Title>
          </div>
        }
        extra={<Button type="link">View All Jobs</Button>}
        className="mb-6"
      >
        <Row gutter={[16, 16]}>
          {[1, 2, 3].map((i) => (
            <Col
              xs={24}
              sm={12}
              lg={8}
              key={i}
            >
              <Card>
                <Skeleton
                  active
                  paragraph={{ rows: 4 }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div className="flex items-center gap-2">
          <Target
            size={20}
            className="text-blue-600"
          />
          <Title
            level={4}
            className="mb-0"
          >
            Recommended for You
          </Title>
        </div>
      }
      extra={
        <Button
          type="link"
          onClick={onViewAllJobs}
        >
          View All Jobs
        </Button>
      }
      style={{ marginBottom: '1.5rem' }}
    >
      {jobs.length === 0 ? (
        <Empty
          description="No recommended jobs available"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <Row gutter={[16, 16]}>
          {jobs.slice(0, 6).map((job) => (
            <Col
              xs={24}
              sm={12}
              lg={8}
              key={job.id}
            >
              <div className="relative">
                {/* Match Percentage Badge */}
                {job.match_percentage && (
                  <div className="absolute top-2 right-2 z-10">
                    <div className="bg-white rounded-full p-1 shadow-md">
                      <Progress
                        type="circle"
                        size={40}
                        percent={job.match_percentage}
                        strokeColor={getMatchColor(job.match_percentage)}
                        format={(percent) => (
                          <span className="text-xs font-medium">{percent}%</span>
                        )}
                        strokeWidth={8}
                      />
                    </div>
                  </div>
                )}

                <JobCard
                  job={job}
                  onView={onViewJob}
                  onApply={onApplyJob}
                  onSave={onSaveJob}
                  isApplied={isJobApplied(job.id)}
                  isSaved={isJobSaved(job.id)}
                  showMatchPercentage={false} // We're showing it as a badge instead
                />
              </div>
            </Col>
          ))}
        </Row>
      )}
    </Card>
  );
};

export default RecommendedJobsSection;
