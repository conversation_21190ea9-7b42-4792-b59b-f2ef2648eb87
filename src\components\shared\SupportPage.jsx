import React, { useState } from 'react';
import {
  Typography,
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  List,
  Collapse,
  Divider,
  Space,
  message,
  Upload,
} from 'antd';
import {
  FaQuestionCircle,
  FaHeadset,
  FaBook,
  FaTicketAlt,
  FaSearch,
  FaChevronRight,
  FaPlus,
  FaPaperPlane,
  FaFileAlt,
  FaExclamationCircle,
  FaInfoCircle,
  FaLightbulb,
  FaUserCircle,
  FaEnvelope,
  FaPhone,
  FaComments,
} from 'react-icons/fa';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;
const { TextArea } = Input;

/**
 * Reusable Support component that can be used for different user types
 * @param {Object} props
 * @param {Function} props.submitTicket - Function to submit a support ticket
 * @param {Array} props.faqItems - FAQ items to display
 * @param {Array} props.supportTickets - User's support tickets
 * @param {boolean} props.loading - Loading state
 * @param {Array} props.additionalTabs - Additional tabs to render
 * @param {Array} props.resourceCategories - Resource categories to display
 */
const SupportPage = ({
  submitTicket,
  faqItems = [],
  supportTickets = [],
  loading = false,
  additionalTabs = [],
  resourceCategories = [],
}) => {
  const { isMobile } = useDeviceDetect();
  const [ticketForm] = Form.useForm();
  const [searchQuery, setSearchQuery] = useState('');

  // Default FAQ items if none provided
  const defaultFaqItems = [
    {
      question: 'How do I schedule an interview?',
      answer:
        'You can schedule an interview by navigating to the Calendar section and clicking on "Schedule Interview". Follow the prompts to select a date, time, and candidate.',
    },
    {
      question: 'How can I update my profile information?',
      answer:
        'To update your profile, go to the Profile page from the sidebar menu. Click on "Edit Profile" to make changes to your information.',
    },
    {
      question: 'What payment methods do you accept?',
      answer:
        'We accept all major credit cards, PayPal, and bank transfers for premium subscriptions and services.',
    },
    {
      question: 'How do I reset my password?',
      answer:
        'You can reset your password by clicking on "Forgot Password" on the login page. Follow the instructions sent to your email to create a new password.',
    },
    {
      question: 'Can I reschedule an interview?',
      answer:
        'Yes, you can reschedule an interview by going to the Calendar section, finding the scheduled interview, and clicking on "Reschedule". Make sure to do this at least 24 hours before the scheduled time.',
    },
  ];

  // Default resource categories if none provided
  const defaultResourceCategories = [
    {
      title: 'Getting Started',
      icon: <FaLightbulb />,
      resources: [
        { title: 'Platform Overview', link: '/resources/overview' },
        { title: 'Creating Your Profile', link: '/resources/profile-setup' },
        { title: 'Finding Opportunities', link: '/resources/opportunities' },
      ],
    },
    {
      title: 'Interviews',
      icon: <FaHeadset />,
      resources: [
        { title: 'Scheduling Interviews', link: '/resources/scheduling' },
        { title: 'Interview Best Practices', link: '/resources/interview-tips' },
        { title: 'Technical Requirements', link: '/resources/tech-requirements' },
      ],
    },
    {
      title: 'Account Management',
      icon: <FaUserCircle />,
      resources: [
        { title: 'Subscription Plans', link: '/resources/subscriptions' },
        { title: 'Billing & Payments', link: '/resources/billing' },
        { title: 'Security Settings', link: '/resources/security' },
      ],
    },
  ];

  const displayFaqItems = faqItems.length > 0 ? faqItems : defaultFaqItems;
  const displayResourceCategories =
    resourceCategories.length > 0 ? resourceCategories : defaultResourceCategories;

  // Filter FAQ items based on search query
  const filteredFaqItems = displayFaqItems.filter(
    (item) =>
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleTicketSubmit = async (values) => {
    try {
      if (submitTicket) {
        await submitTicket(values);
        message.success('Support ticket submitted successfully');
        ticketForm.resetFields();
      }
    } catch (error) {
      message.error('Failed to submit support ticket');
    }
  };

  return (
    <div className="support-page">
      <Title
        level={2}
        className="mb-6"
      >
        Support Center
      </Title>

      <Tabs defaultActiveKey="faq">
        <TabPane
          tab={
            <span className="flex items-center">
              <FaQuestionCircle className="mr-2" />
              FAQ
            </span>
          }
          key="faq"
        >
          <Card
            bordered={false}
            className="mb-6"
          >
            <div className="mb-6">
              <Input
                placeholder="Search for answers..."
                prefix={<FaSearch className="text-muted" />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                size="large"
              />
            </div>

            {filteredFaqItems.length > 0 ? (
              <Collapse
                bordered={false}
                expandIconPosition="end"
                expandIcon={({ isActive }) => (
                  <FaChevronRight
                    style={{
                      transform: isActive ? 'rotate(90deg)' : 'rotate(0deg)',
                      transition: 'transform 0.3s',
                    }}
                  />
                )}
              >
                {filteredFaqItems.map((item, index) => (
                  <Panel
                    header={
                      <div className="flex items-center">
                        <FaQuestionCircle className="mr-2 text-primary" />
                        <Text strong>{item.question}</Text>
                      </div>
                    }
                    key={index}
                  >
                    <Paragraph>{item.answer}</Paragraph>
                  </Panel>
                ))}
              </Collapse>
            ) : (
              <div className="text-center py-6">
                <FaExclamationCircle
                  style={{ fontSize: '32px', color: '#faad14', marginBottom: '16px' }}
                />
                <Title level={4}>No results found</Title>
                <Paragraph>
                  We couldn't find any FAQ items matching your search. Try different keywords or
                  submit a support ticket.
                </Paragraph>
              </div>
            )}
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span className="flex items-center">
              <FaTicketAlt className="mr-2" />
              Support Tickets
            </span>
          }
          key="tickets"
        >
          <Row gutter={[24, 24]}>
            <Col
              xs={24}
              lg={16}
            >
              <Card
                bordered={false}
                className="mb-6"
              >
                <Title level={4}>Submit a Support Ticket</Title>
                <Paragraph className="text-muted mb-4">
                  Can't find what you're looking for? Submit a ticket and our support team will
                  assist you.
                </Paragraph>

                <Form
                  form={ticketForm}
                  layout="vertical"
                  onFinish={handleTicketSubmit}
                >
                  <Form.Item
                    name="subject"
                    label="Subject"
                    rules={[{ required: true, message: 'Please enter a subject' }]}
                  >
                    <Input placeholder="Brief description of your issue" />
                  </Form.Item>

                  <Form.Item
                    name="category"
                    label="Category"
                    rules={[{ required: true, message: 'Please select a category' }]}
                  >
                    <Select placeholder="Select the category that best matches your issue">
                      <Option value="account">Account Issues</Option>
                      <Option value="billing">Billing & Payments</Option>
                      <Option value="technical">Technical Problems</Option>
                      <Option value="feature">Feature Requests</Option>
                      <Option value="other">Other</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="priority"
                    label="Priority"
                    rules={[{ required: true, message: 'Please select a priority' }]}
                  >
                    <Select placeholder="Select the priority of your issue">
                      <Option value="low">Low - General question or feedback</Option>
                      <Option value="medium">Medium - Issue affecting functionality</Option>
                      <Option value="high">High - Critical issue preventing work</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="description"
                    label="Description"
                    rules={[{ required: true, message: 'Please describe your issue' }]}
                  >
                    <TextArea
                      rows={6}
                      placeholder="Please provide as much detail as possible about your issue"
                    />
                  </Form.Item>

                  <Form.Item
                    name="attachments"
                    label="Attachments (Optional)"
                  >
                    <Upload>
                      <Button icon={<FaPlus className="mr-2" />}>Add File</Button>
                    </Upload>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<FaPaperPlane className="mr-2" />}
                      loading={loading}
                    >
                      Submit Ticket
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            <Col
              xs={24}
              lg={8}
            >
              <Card
                bordered={false}
                className="mb-6"
              >
                <Title level={4}>Your Tickets</Title>
                <Paragraph className="text-muted mb-4">
                  Track the status of your support requests
                </Paragraph>

                {supportTickets.length > 0 ? (
                  <List
                    itemLayout="horizontal"
                    dataSource={supportTickets}
                    renderItem={(ticket) => (
                      <List.Item actions={[<Button type="link">View</Button>]}>
                        <List.Item.Meta
                          avatar={<FaTicketAlt />}
                          title={ticket.subject}
                          description={
                            <div>
                              <Text type="secondary">
                                {new Date(ticket.createdAt).toLocaleDateString()}
                              </Text>
                              <Tag
                                color={
                                  ticket.status === 'open'
                                    ? 'blue'
                                    : ticket.status === 'in-progress'
                                      ? 'orange'
                                      : ticket.status === 'resolved'
                                        ? 'green'
                                        : 'default'
                                }
                                className="ml-2"
                              >
                                {ticket.status}
                              </Tag>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <div className="text-center py-4">
                    <FaTicketAlt
                      style={{ fontSize: '24px', color: '#d9d9d9', marginBottom: '16px' }}
                    />
                    <Paragraph>You haven't submitted any support tickets yet.</Paragraph>
                  </div>
                )}
              </Card>

              <Card bordered={false}>
                <Title level={4}>Contact Information</Title>
                <Paragraph className="text-muted mb-4">
                  Alternative ways to reach our support team
                </Paragraph>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <FaEnvelope className="mr-2 text-primary" />
                    <Text strong>Email Support</Text>
                  </div>
                  <Paragraph><EMAIL></Paragraph>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <FaPhone className="mr-2 text-primary" />
                    <Text strong>Phone Support</Text>
                  </div>
                  <Paragraph>+****************</Paragraph>
                  <Text type="secondary">Monday - Friday, 9am - 5pm EST</Text>
                </div>

                <div>
                  <div className="flex items-center mb-2">
                    <FaComments className="mr-2 text-primary" />
                    <Text strong>Live Chat</Text>
                  </div>
                  <Paragraph>Available on the bottom right of your screen</Paragraph>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane
          tab={
            <span className="flex items-center">
              <FaBook className="mr-2" />
              Resources
            </span>
          }
          key="resources"
        >
          <Card bordered={false}>
            <Title level={4}>Help Resources</Title>
            <Paragraph className="text-muted mb-6">
              Browse our collection of guides, tutorials, and documentation
            </Paragraph>

            <Row gutter={[24, 24]}>
              {displayResourceCategories.map((category, index) => (
                <Col
                  xs={24}
                  md={12}
                  lg={8}
                  key={index}
                >
                  <Card
                    className="resource-card h-full"
                    bordered={true}
                    hoverable
                  >
                    <div className="flex items-center mb-4">
                      <div className="resource-icon mr-3 text-primary">{category.icon}</div>
                      <Title
                        level={5}
                        className="mb-0"
                      >
                        {category.title}
                      </Title>
                    </div>

                    <List
                      itemLayout="horizontal"
                      dataSource={category.resources}
                      renderItem={(resource) => (
                        <List.Item>
                          <List.Item.Meta
                            avatar={<FaFileAlt className="text-muted" />}
                            title={<a href={resource.link}>{resource.title}</a>}
                          />
                        </List.Item>
                      )}
                    />

                    <div className="mt-4">
                      <Button
                        type="link"
                        className="p-0"
                      >
                        View All{' '}
                        <FaChevronRight
                          className="ml-1"
                          style={{ fontSize: '12px' }}
                        />
                      </Button>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </TabPane>

        {/* Render additional tabs if provided */}
        {additionalTabs.map((tab) => (
          <TabPane
            tab={
              <span className="flex items-center">
                {tab.icon && <span className="mr-2">{tab.icon}</span>}
                {tab.title}
              </span>
            }
            key={tab.key}
          >
            {tab.content}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default SupportPage;
