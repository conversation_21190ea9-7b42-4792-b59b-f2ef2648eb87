import React, { useState } from 'react';
import { Card, Typography, Radio, Space, Button, Checkbox } from 'antd';

const { Title, Text } = Typography;

const Question = ({ question, onAnswer, onNext, isLast }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [selectedOptions, setSelectedOptions] = useState([]);

  const handleSingleOptionChange = (e) => {
    setSelectedOption(e.target.value);
  };

  const handleMultipleOptionChange = (checkedValues) => {
    setSelectedOptions(checkedValues);
  };

  const handleSubmit = () => {
    if (question.type === 'multiple_choice') {
      onAnswer(question.id, selectedOption);
    } else if (question.type === 'multiple_select') {
      onAnswer(question.id, selectedOptions);
    }

    setSelectedOption(null);
    setSelectedOptions([]);
    onNext();
  };

  const isSubmitDisabled = () => {
    if (question.type === 'multiple_choice') {
      return selectedOption === null;
    } else if (question.type === 'multiple_select') {
      return selectedOptions.length === 0;
    }
    return true;
  };

  return (
    <Card className="mb-4 shadow-sm">
      <div className="mb-4">
        <Title level={4}>{question.text}</Title>
        {question.description && <Text type="secondary">{question.description}</Text>}
      </div>

      {question.type === 'multiple_choice' && (
        <Radio.Group
          onChange={handleSingleOptionChange}
          value={selectedOption}
          className="w-full"
        >
          <Space
            direction="vertical"
            className="w-full"
          >
            {question.options.map((option) => (
              <Radio
                key={option.id}
                value={option.id}
                className="w-full p-3 border rounded hover:bg-gray-50"
              >
                {option.text}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      )}

      {question.type === 'multiple_select' && (
        <Checkbox.Group
          onChange={handleMultipleOptionChange}
          className="w-full"
        >
          <Space
            direction="vertical"
            className="w-full"
          >
            {question.options.map((option) => (
              <Checkbox
                key={option.id}
                value={option.id}
                className="w-full p-3 border rounded hover:bg-gray-50"
              >
                {option.text}
              </Checkbox>
            ))}
          </Space>
        </Checkbox.Group>
      )}

      <div className="mt-6 flex justify-end">
        <Button
          type="primary"
          onClick={handleSubmit}
          disabled={isSubmitDisabled()}
        >
          {isLast ? 'Submit' : 'Next Question'}
        </Button>
      </div>
    </Card>
  );
};

export default Question;
