import React from 'react';
import { useColorModeStore } from '@/store/colorMode.store';
import { theme } from '@/styles/theme';

const PageLoader = ({ message = 'Loading' }) => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Get theme colors based on current mode
  const primaryColor = theme.colors.primary;
  const backgroundColor = isDark ? theme.colors.dark.background : theme.colors.light.background;

  return (
    <div className="page-loader-container">
      <div className="loader-text">
        {message}
        <span className="dot-animation">
          <span className="dot">.</span>
          <span className="dot">.</span>
          <span className="dot">.</span>
        </span>
      </div>

      {/* Inline styles for the loader to use theme colors */}
      <style>{`
        .loader {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          display: block;
          margin: 15px auto;
          position: relative;
          background: ${backgroundColor};
          box-shadow:
            -24px 0 ${backgroundColor},
            24px 0 ${backgroundColor};
          box-sizing: border-box;
          animation: shadowPulse 2s linear infinite;
        }

        @keyframes shadowPulse {
          33% {
            background: ${backgroundColor};
            box-shadow:
              -24px 0 ${primaryColor},
              24px 0 ${backgroundColor};
          }
          66% {
            background: ${primaryColor};
            box-shadow:
              -24px 0 ${backgroundColor},
              24px 0 ${backgroundColor};
          }
          100% {
            background: ${backgroundColor};
            box-shadow:
              -24px 0 ${backgroundColor},
              24px 0 ${primaryColor};
          }
        }
      `}</style>
    </div>
  );
};

export default PageLoader;
