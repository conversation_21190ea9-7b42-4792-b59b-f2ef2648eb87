import { useEffect, useState } from 'react';
import { RouterProvider } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import AppRouter from '@/router/AppRouter';
import AppLoader from '@/components/shared/AppLoader';

const AppWrapper = () => {
  const { loading, isInitialized, clerkLoaded } = useAuth();
  const [appReady, setAppReady] = useState(false);

  useEffect(() => {
    // Show loading for at least 1 second for better UX
    const timer = setTimeout(() => {
      if (clerkLoaded && isInitialized && !loading) {
        setAppReady(true);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [clerkLoaded, isInitialized, loading]);

  if (!appReady) {
    return <AppLoader message="Loading Wurk..." />;
  }

  return <RouterProvider router={AppRouter} />;
};

export default AppWrapper;
