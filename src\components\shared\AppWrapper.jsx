import { RouterProvider } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import AppRouter from '@/router/AppRouter';
import AppLoader from '@/components/shared/AppLoader';

const AppWrapper = () => {
  const { isLoaded } = useUser();

  // Show loading while Clerk is initializing
  if (!isLoaded) {
    return <AppLoader message="Loading Flyt..." />;
  }

  return <RouterProvider router={AppRouter} />;
};

export default AppWrapper;
