/**
 * Interviewer Feature Constants
 * 
 * Note: Interview scheduling and video call functionality has been removed.
 * This file contains constants for general interviewer functionality only.
 */

// Interviewer specialization areas (for profile and expertise)
export const INTERVIEWER_SPECIALIZATIONS = [
  'Technical Assessment',
  'Behavioral Assessment', 
  'Cultural Fit Assessment',
  'Leadership Assessment',
  'Communication Skills',
  'Problem Solving',
  'Domain Expertise',
  'Soft Skills Evaluation'
];

// Experience levels for interviewers
export const INTERVIEWER_EXPERIENCE_LEVELS = [
  { value: 'junior', label: 'Junior (1-3 years)', min: 1, max: 3 },
  { value: 'mid', label: 'Mid-level (3-7 years)', min: 3, max: 7 },
  { value: 'senior', label: 'Senior (7-12 years)', min: 7, max: 12 },
  { value: 'lead', label: 'Lead (12+ years)', min: 12, max: null }
];

// Industries that interviewers can specialize in
export const INTERVIEWER_INDUSTRIES = [
  'Real Estate',
  'Technology',
  'Finance',
  'Healthcare',
  'Education',
  'Manufacturing',
  'Retail',
  'Consulting',
  'Media & Entertainment',
  'Non-profit',
  'Government',
  'Startups'
];

// Assessment criteria for evaluations
export const ASSESSMENT_CRITERIA = [
  {
    id: 'technical_skills',
    name: 'Technical Skills',
    description: 'Evaluation of technical competencies and knowledge',
    weight: 0.3
  },
  {
    id: 'communication',
    name: 'Communication',
    description: 'Verbal and written communication abilities',
    weight: 0.2
  },
  {
    id: 'problem_solving',
    name: 'Problem Solving',
    description: 'Analytical thinking and solution development',
    weight: 0.2
  },
  {
    id: 'cultural_fit',
    name: 'Cultural Fit',
    description: 'Alignment with company values and culture',
    weight: 0.15
  },
  {
    id: 'experience',
    name: 'Relevant Experience',
    description: 'Past experience and achievements',
    weight: 0.15
  }
];

// Rating scales for assessments
export const RATING_SCALES = {
  FIVE_POINT: {
    min: 1,
    max: 5,
    labels: {
      1: 'Poor',
      2: 'Below Average', 
      3: 'Average',
      4: 'Good',
      5: 'Excellent'
    }
  },
  TEN_POINT: {
    min: 1,
    max: 10,
    labels: {
      1: 'Very Poor',
      3: 'Poor',
      5: 'Average',
      7: 'Good',
      10: 'Excellent'
    }
  }
};

// Interviewer availability status
export const AVAILABILITY_STATUS = {
  AVAILABLE: 'available',
  BUSY: 'busy',
  UNAVAILABLE: 'unavailable',
  BREAK: 'on_break'
};

// Calendar event types specific to interviewers
export const INTERVIEWER_EVENT_TYPES = [
  { id: 'assessment', name: 'Assessment Review', color: 'blue' },
  { id: 'preparation', name: 'Preparation Time', color: 'orange' },
  { id: 'feedback', name: 'Feedback Session', color: 'green' },
  { id: 'training', name: 'Training', color: 'purple' },
  { id: 'meeting', name: 'Team Meeting', color: 'cyan' },
  { id: 'break', name: 'Break', color: 'gray' }
];

// Performance metrics for interviewers
export const PERFORMANCE_METRICS = [
  {
    id: 'assessments_completed',
    name: 'Assessments Completed',
    description: 'Total number of candidate assessments completed',
    unit: 'count'
  },
  {
    id: 'average_rating',
    name: 'Average Rating Given',
    description: 'Average rating given to candidates',
    unit: 'rating'
  },
  {
    id: 'feedback_quality',
    name: 'Feedback Quality Score',
    description: 'Quality of feedback provided (rated by companies)',
    unit: 'score'
  },
  {
    id: 'response_time',
    name: 'Average Response Time',
    description: 'Average time to complete assessments',
    unit: 'hours'
  }
];

// Notification types for interviewers
export const INTERVIEWER_NOTIFICATIONS = {
  NEW_ASSESSMENT_REQUEST: 'new_assessment_request',
  ASSESSMENT_REMINDER: 'assessment_reminder',
  FEEDBACK_REQUEST: 'feedback_request',
  PROFILE_UPDATE: 'profile_update',
  PAYMENT_RECEIVED: 'payment_received',
  TRAINING_AVAILABLE: 'training_available'
};

// Default interviewer dashboard widgets
export const INTERVIEWER_DASHBOARD_WIDGETS = [
  {
    id: 'pending_assessments',
    title: 'Pending Assessments',
    type: 'counter',
    priority: 1
  },
  {
    id: 'completed_this_month',
    title: 'Completed This Month',
    type: 'counter',
    priority: 2
  },
  {
    id: 'earnings_overview',
    title: 'Earnings Overview',
    type: 'chart',
    priority: 3
  },
  {
    id: 'recent_feedback',
    title: 'Recent Feedback',
    type: 'list',
    priority: 4
  },
  {
    id: 'upcoming_deadlines',
    title: 'Upcoming Deadlines',
    type: 'timeline',
    priority: 5
  }
];

export default {
  INTERVIEWER_SPECIALIZATIONS,
  INTERVIEWER_EXPERIENCE_LEVELS,
  INTERVIEWER_INDUSTRIES,
  ASSESSMENT_CRITERIA,
  RATING_SCALES,
  AVAILABILITY_STATUS,
  INTERVIEWER_EVENT_TYPES,
  PERFORMANCE_METRICS,
  INTERVIEWER_NOTIFICATIONS,
  INTERVIEWER_DASHBOARD_WIDGETS
};
