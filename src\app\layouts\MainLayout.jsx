import React from 'react';
import { Layout, Button } from 'antd';
import { Outlet, Link } from 'react-router-dom';
import useDeviceDetect from '@/hooks/useDeviceDetect';
import Header from '../../components/layouts/Header';
import Footer from '../../components/layouts/Footer';

const { Content } = Layout;

const MainLayout = () => {
  const { isMobile, isTablet } = useDeviceDetect();

  return (
    <Layout
      style={{
        minHeight: '100vh',
        backgroundColor: 'var(--background)',
        color: 'var(--foreground)',
      }}
    >
      <Header />
      <Content style={{ backgroundColor: 'var(--background)' }}>
        <div
          className="mx-auto px-4 sm:px-6 lg:px-10 py-4 sm:py-6 lg:py-8 main-content-wrapper"
          style={{
            maxWidth: '1400px',
            paddingLeft: isTablet ? '24px' : undefined,
            paddingRight: isTablet ? '24px' : undefined,
            backgroundColor: 'var(--background)',
            color: 'var(--foreground)',
          }}
        >
          <Outlet />
        </div>
      </Content>

      <Footer />
    </Layout>
  );
};

export default MainLayout;
