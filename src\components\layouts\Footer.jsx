import React, { useEffect } from 'react';
import {
  FacebookOutlined,
  InstagramOutlined,
  LinkedinOutlined,
  TwitterOutlined,
} from '@ant-design/icons';
import { Col, Divider, Layout, Row, Space, Typography } from 'antd';
import { Link } from 'react-router-dom';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useColorModeStore } from '@/store/colorMode.store';
import { logo_lite, logo_dark } from '../../assets';

const { Footer: AntFooter } = Layout;
const { Title, Text } = Typography;

const Footer = () => {
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const footerLinks = [
    {
      title: 'Company',
      items: [
        { label: 'About Us', path: '/about' },
        { label: 'Careers', path: '/careers' },
        { label: 'Blog', path: '/blog' },
        { label: 'Press', path: '/press' },
      ],
    },
    {
      title: 'Resources',
      items: [
        { label: 'Help Center', path: '/help' },
        { label: 'Guides', path: '/guides' },
        { label: 'Partners', path: '/partners' },
        { label: 'Webinars', path: '/webinars' },
      ],
    },
    {
      title: 'Legal',
      items: [
        { label: 'Terms of Service', path: '/terms' },
        { label: 'Privacy Policy', path: '/privacy' },
        { label: 'Cookie Policy', path: '/cookies' },
        { label: 'Security', path: '/security' },
      ],
    },
  ];

  return (
    <AntFooter
      className="bg-card shadow-lg"
      style={{
        boxShadow: '0 -4px 10px rgba(0, 0, 0, 0.05)',
        paddingTop: isMobile ? '32px' : isTablet ? '40px' : '48px',
        paddingBottom: isMobile ? '16px' : isTablet ? '20px' : '24px',
        paddingLeft: isMobile ? '16px' : '24px',
        paddingRight: isMobile ? '16px' : '24px',
      }}
    >
      <div className="max-w-7xl mx-auto">
        <Row gutter={[16, 32]}>
          <Col
            xs={24}
            sm={24}
            md={8}
            lg={8}
          >
            <div className="mb-4">
              <img
                src={isDark ? logo_dark : logo_lite}
                alt="logo"
                className="w-28 sm:w-36"
              />
            </div>
            <Text style={{ display: 'block', maxWidth: '300px', color: 'var(--text-secondary)' }}>
              The ultimate platform for connecting job seekers with interviewers. Find your perfect
              match and ace your next interview.
            </Text>

            <Space
              className="mt-6"
              size="large"
            >
              <a
                href="https://facebook.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted hover:text-primary transition-colors"
              >
                <FacebookOutlined
                  style={{ fontSize: '24px', color: 'var(--text-muted)' }}
                  className="hover:text-primary"
                />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted hover:text-primary transition-colors"
              >
                <TwitterOutlined
                  style={{ fontSize: '24px', color: 'var(--text-muted)' }}
                  className="hover:text-primary"
                />
              </a>
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted hover:text-primary transition-colors"
              >
                <InstagramOutlined
                  style={{ fontSize: '24px', color: 'var(--text-muted)' }}
                  className="hover:text-primary"
                />
              </a>
              <a
                href="https://linkedin.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted hover:text-primary transition-colors"
              >
                <LinkedinOutlined
                  style={{ fontSize: '24px', color: 'var(--text-muted)' }}
                  className="hover:text-primary"
                />
              </a>
            </Space>
          </Col>

          {/* Footer Links */}
          {footerLinks.map((section, index) => (
            <Col
              xs={24}
              sm={8}
              md={5}
              lg={5}
              key={index}
            >
              <Title
                level={5}
                style={{ color: 'var(--text-primary)' }}
              >
                {section.title}
              </Title>
              <ul className="space-y-2">
                {section.items.map((item, idx) => (
                  <li key={idx}>
                    <Link
                      to={item.path}
                      className="text-secondary hover:text-primary transition-colors"
                      style={{ color: 'var(--text-secondary)' }}
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </Col>
          ))}
        </Row>

        <Divider style={{ margin: isMobile ? '24px 0' : '32px 0', borderColor: 'var(--border)' }} />

        <Row
          justify="space-between"
          align={isMobile ? 'top' : 'middle'}
          gutter={[0, 16]}
        >
          <Col
            xs={24}
            md={12}
          >
            <Text style={{ color: 'var(--text-muted)' }}>
              © {new Date().getFullYear()} Inter View. All rights reserved.
            </Text>
          </Col>
          <Col
            xs={24}
            md={12}
            style={{ textAlign: isMobile ? 'left' : 'right' }}
          >
            <Space
              split={
                <Divider
                  type="vertical"
                  style={{ borderColor: 'var(--border)' }}
                />
              }
            >
              <Link
                to="/terms"
                className="text-muted hover:text-primary"
                style={{ color: 'var(--text-muted)' }}
              >
                Terms
              </Link>
              <Link
                to="/privacy"
                className="text-muted hover:text-primary"
                style={{ color: 'var(--text-muted)' }}
              >
                Privacy
              </Link>
              <Link
                to="/cookies"
                className="text-muted hover:text-primary"
                style={{ color: 'var(--text-muted)' }}
              >
                Cookies
              </Link>
            </Space>
          </Col>
        </Row>
      </div>
    </AntFooter>
  );
};

export default Footer;
