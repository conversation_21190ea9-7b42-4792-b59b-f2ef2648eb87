/**
 * Courses Section Component for Candidate Dashboard
 */
import React from 'react';
import { Card, Row, Col, Button, Typography, Space, Rate, Tag, Skeleton } from 'antd';
import { BookOpen, Clock, Users, Star, IndianRupee } from 'lucide-react';

const { Title, Text } = Typography;

/**
 * CoursesSection component for displaying recommended courses
 * @param {Object} props
 * @param {Array} props.courses - Array of course objects
 * @param {boolean} props.loading - Loading state
 * @param {Function} props.onViewCourse - Handler for viewing course details
 * @param {Function} props.onViewAllCourses - Handler for viewing all courses
 * @returns {JSX.Element}
 */
const CoursesSection = ({ courses = [], loading = false, onViewCourse, onViewAllCourses }) => {
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getLevelColor = (level) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'green';
      case 'intermediate':
        return 'orange';
      case 'advanced':
        return 'red';
      default:
        return 'blue';
    }
  };

  if (loading) {
    return (
      <Card
        title="Recommended Courses"
        extra={<Button type="link">View All Courses</Button>}
        style={{ marginBottom: '1.5rem' }}
      >
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4].map((i) => (
            <Col
              xs={24}
              sm={12}
              lg={6}
              key={i}
            >
              <Card>
                <Skeleton
                  active
                  paragraph={{ rows: 4 }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Title
          level={4}
          className="mb-0"
        >
          Recommended Courses
        </Title>
      }
      extra={
        <Button
          type="link"
          onClick={onViewAllCourses}
        >
          View All Courses
        </Button>
      }
      className="mb-6"
    >
      <Row gutter={[16, 16]}>
        {courses.slice(0, 4).map((course) => (
          <Col
            xs={24}
            sm={12}
            lg={6}
            key={course.id}
          >
            <Card
              hoverable
              className="h-full transition-all duration-200 hover:shadow-lg"
              cover={
                <div className="relative">
                  <img
                    alt={course.title}
                    src={course.image}
                    className="h-40 w-full object-cover"
                  />
                  <div className="absolute top-2 right-2">
                    <Tag color={getLevelColor(course.level)}>{course.level}</Tag>
                  </div>
                </div>
              }
              onClick={() => onViewCourse?.(course)}
            >
              <div className="flex flex-col h-full">
                {/* Course Title */}
                <Title
                  level={5}
                  className="mb-2 line-clamp-2"
                >
                  {course.title}
                </Title>

                {/* Provider */}
                <Text
                  type="secondary"
                  className="text-sm mb-3"
                >
                  by {course.provider}
                </Text>

                {/* Course Info */}
                <div className="flex-1 space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1 text-gray-600">
                      <Clock size={14} />
                      <span>{course.duration}</span>
                    </div>
                    <div className="flex items-center gap-1 text-gray-600">
                      <Users size={14} />
                      <span>{course.students.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <Rate
                        disabled
                        allowHalf
                        value={course.rating}
                        className="text-xs"
                      />
                      <Text className="text-xs">({course.rating})</Text>
                    </div>
                  </div>

                  <Text
                    type="secondary"
                    className="text-xs line-clamp-2"
                  >
                    {course.description}
                  </Text>
                </div>

                {/* Skills Tags */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {course.skills.slice(0, 2).map((skill) => (
                      <Tag
                        key={skill}
                        size="small"
                        className="text-xs"
                      >
                        {skill}
                      </Tag>
                    ))}
                    {course.skills.length > 2 && (
                      <Tag
                        size="small"
                        className="text-xs"
                      >
                        +{course.skills.length - 2} more
                      </Tag>
                    )}
                  </div>
                </div>

                {/* Price and Action */}
                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                  <div className="flex items-center gap-1">
                    <IndianRupee
                      size={16}
                      className="text-green-600"
                    />
                    <Text className="font-semibold text-green-600">
                      {formatPrice(course.price)}
                    </Text>
                  </div>
                  <Button
                    type="primary"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onViewCourse?.(course);
                    }}
                  >
                    Enroll
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );
};

export default CoursesSection;
