import React from 'react';
import { Row, Col } from 'antd';
import { FileText, Clock, Calendar, Heart } from 'lucide-react';
import StatCard from '../StatCard';

/**
 * StatsSection component for displaying candidate statistics
 */
const StatsSection = ({ stats, loading, onStatClick }) => {
  const statCards = [
    {
      title: 'Total Applications',
      value: stats.totalApplications,
      icon: <FileText />,
      key: 'applications',
      valueStyle: { color: '#1890ff' },
    },
    {
      title: 'Pending Reviews',
      value: stats.pendingApplications,
      icon: <Clock />,
      key: 'pending',
      valueStyle: { color: '#faad14' },
    },
    {
      title: 'Upcoming Interviews',
      value: stats.interviewsScheduled,
      icon: <Calendar />,
      key: 'interviews',
      valueStyle: { color: '#52c41a' },
    },
    {
      title: 'Saved Jobs',
      value: stats.savedJobsCount,
      icon: <Heart />,
      key: 'saved',
      valueStyle: { color: '#eb2f96' },
    },
  ];

  return (
    <Row gutter={[16, 16]}>
      {statCards.map((stat) => (
        <Col
          xs={12}
          sm={6}
          key={stat.key}
        >
          <StatCard
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            loading={loading}
            valueStyle={stat.valueStyle}
            onClick={() => onStatClick?.(stat.key)}
          />
        </Col>
      ))}
    </Row>
  );
};

export default StatsSection;
