import { useEffect } from 'react';
import { ConfigProvider } from 'antd';
import { RouterProvider } from 'react-router-dom';
import { ToastContainer, Zoom } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Clerk<PERSON>rovider } from '@clerk/clerk-react';
import { useColorModeStore } from '@/store/colorMode.store';
import AppRouter from '@/router/AppRouter';
import { theme as appTheme, getThemeColors } from '@/styles/theme';
import NetworkStatusIndicator from '@/components/shared/NetworkStatusIndicator';

// Import your publishable key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error('Missing Publishable Key');
}

const App = () => {
  const { colorMode } = useColorModeStore();
  const themeColors = getThemeColors(colorMode);
  const isDark = colorMode === 'dark';

  // Apply theme class immediately when colorMode changes
  useEffect(() => {
    const html = document.documentElement;
    const body = document.body;

    if (isDark) {
      html.classList.add('dark');
      body.classList.add('dark');
    } else {
      html.classList.remove('dark');
      body.classList.remove('dark');
    }
  }, [isDark]);

  return (
    <ClerkProvider
      publishableKey={PUBLISHABLE_KEY}
      afterSignOutUrl="/"
    >
      <ConfigProvider
        theme={{
          // algorithm: isDark ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
          token: {
            colorPrimary: appTheme.colors.primary,
            colorBgBase: themeColors.background,
            colorTextBase: themeColors.foreground,
            borderRadius: parseInt(appTheme.radii.button),
            fontFamily: appTheme.typography.fontFamily.join(', '),
            colorBgContainer: themeColors.card,
            colorBorderSecondary: themeColors.border,
            colorTextSecondary: themeColors.muted,
          },
          components: {
            Button: {
              colorPrimary: appTheme.colors.primary,
              algorithm: true,
            },
            Menu: {
              // Updated tokens based on warnings
              itemBg: 'transparent',
              itemColor: themeColors.foreground,
              itemHoverColor: appTheme.colors.primary,
              itemSelectedColor: appTheme.colors.primary,
              activeBarBorderWidth: 0,
              horizontalItemSelectedColor: appTheme.colors.primary,
              horizontalItemHoverColor: appTheme.colors.primaryHover,
            },
            Card: {
              colorBgContainer: themeColors.card,
              colorBorderSecondary: themeColors.border,
            },
            Drawer: {
              colorBgElevated: themeColors.card,
              colorBorderSecondary: themeColors.border,
            },
            Modal: {
              colorBgElevated: themeColors.card,
              colorBorderSecondary: themeColors.border,
            },
            Layout: {
              bodyBg: themeColors.background,
              headerBg: themeColors.card,
              footerBg: themeColors.card,
              siderBg: themeColors.card,
            },
            Typography: {
              colorTextHeading: themeColors.foreground,
              colorText: themeColors.foreground,
              colorTextSecondary: themeColors.muted,
            },
            Tooltip: {
              colorBgSpotlight: isDark ? '#2c2c2c' : '#ffffff',
              colorTextLightSolid: isDark ? '#ffffff' : '#1e1e1e',
              colorBgElevated: isDark ? '#2c2c2c' : '#ffffff',
            },
            Badge: {
              colorBgContainer: isDark ? '#3a3a3a' : '#f0f4ff',
              colorText: isDark ? '#ffffff' : '#1e1e1e',
            },
          },
        }}
      >
        <RouterProvider router={AppRouter} />
        <ToastContainer
          position="top-center"
          autoClose={2500}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick={false}
          rtl={false}
          pauseOnFocusLoss={false}
          draggable={false}
          pauseOnHover={false}
          transition={Zoom}
          theme={isDark ? 'dark' : 'light'}
        />

        {/* Network Status Indicator */}
        <NetworkStatusIndicator />
      </ConfigProvider>
    </ClerkProvider>
  );
};

export default App;
