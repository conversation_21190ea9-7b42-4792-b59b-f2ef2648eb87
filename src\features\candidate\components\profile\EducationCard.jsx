/**
 * Education Card Component
 * Shows educational background in timeline format
 */
import React from 'react';
import { Card, Timeline, Typography, Tag, Al<PERSON>, Button } from 'antd';
import { GraduationCap } from 'lucide-react';

const { Text } = Typography;

const EducationCard = ({ profile, onEditProfile }) => {
  return (
    <Card
      title={
        <span className="flex items-center">
          <GraduationCap size={18} className="mr-2 text-purple-500" />
          Education
        </span>
      }
      className="shadow-sm rounded-lg"
    >
      {profile?.education && Array.isArray(profile.education) && profile.education.length > 0 ? (
        <Timeline className="mt-4">
          {profile.education.map((edu, index) => (
            <Timeline.Item key={index} color="blue">
              <div className="space-y-1">
                <Text strong className="text-base">{edu.degree}</Text>
                <div className="text-gray-600">{edu.institution}</div>
                <div className="text-gray-500 text-sm">
                  {edu.start_year} - {edu.end_year || 'Present'}
                </div>
                {edu.gpa && (
                  <div className="text-sm">
                    <Tag color="blue">GPA: {edu.gpa}</Tag>
                  </div>
                )}
                {edu.field_of_study && (
                  <div className="text-sm text-gray-600">
                    Field: {edu.field_of_study}
                  </div>
                )}
                {edu.achievements && Array.isArray(edu.achievements) && edu.achievements.length > 0 && (
                  <div className="text-sm">
                    <div className="font-medium text-gray-700 mb-1">Achievements:</div>
                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                      {edu.achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      ) : (
        <Alert
          message="Add education"
          description="Include your educational background to strengthen your profile."
          type="info"
          showIcon
          action={
            <Button size="small" type="primary" onClick={onEditProfile}>
              Add Education
            </Button>
          }
        />
      )}
    </Card>
  );
};

export default EducationCard;
