# Logs
logs
*.log
pnpm-debug.log*
npm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.*.local

# Directory for instrumented libs
lib-cov

# Coverage
coverage
*.lcov
.nyc_output

# Build artifacts
dist/
build/

# Dependency directories
node_modules/

# Caches
.cache/
.parcel-cache/
.vite/
.pnpm-debug.log*

.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_*/

# IDE files
.DS_Store
.vscode/*
!.vscode/settings.json

# REPL history
.node_repl_history

# Output from `npm pack`
*.tgz

# Binary addons
build/Release

# Testing tools
.vscode-test/

# Others
*.sublime-workspace
*.sublime-project

.pnp.*


