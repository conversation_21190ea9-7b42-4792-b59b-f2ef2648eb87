/**
 * Companies Section Component for Candidate Dashboard
 */
import React from 'react';
import { Card, Row, Col, Button, Typography, Space, Rate, Tag, Avatar, Skeleton } from 'antd';
import { Building, MapPin, Users, Briefcase } from 'lucide-react';

const { Title, Text } = Typography;

/**
 * CompaniesSection component for displaying top companies
 * @param {Object} props
 * @param {Array} props.companies - Array of company objects
 * @param {boolean} props.loading - Loading state
 * @param {Function} props.onViewCompany - Handler for viewing company details
 * @param {Function} props.onViewAllCompanies - Handler for viewing all companies
 * @returns {JSX.Element}
 */
const CompaniesSection = ({
  companies = [],
  loading = false,
  onViewCompany,
  onViewAllCompanies,
}) => {
  if (loading) {
    return (
      <Card
        title="Top Companies"
        extra={<Button type="link">View All Companies</Button>}
        style={{ marginBottom: '1.5rem' }}
      >
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Col
              xs={24}
              sm={12}
              lg={8}
              key={i}
            >
              <Card>
                <Skeleton
                  active
                  avatar
                  paragraph={{ rows: 3 }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Title
          level={4}
          className="mb-0"
        >
          Top Companies
        </Title>
      }
      extra={
        <Button
          type="link"
          onClick={onViewAllCompanies}
        >
          View All Companies
        </Button>
      }
      style={{ marginBottom: '1.5rem' }}
    >
      <Row gutter={[16, 16]}>
        {companies.slice(0, 6).map((company) => (
          <Col
            xs={24}
            sm={12}
            lg={8}
            key={company.id}
          >
            <Card
              hoverable
              className="h-full transition-all duration-200 hover:shadow-lg"
              onClick={() => onViewCompany?.(company)}
            >
              <div className="flex flex-col h-full">
                {/* Company Header */}
                <div className="flex items-start gap-3 mb-4">
                  <Avatar
                    size={48}
                    src={company.logo_url}
                    icon={<Building size={24} />}
                    className="flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <Title
                      level={5}
                      className="mb-1 truncate"
                    >
                      {company.company_name}
                    </Title>
                    <Text
                      type="secondary"
                      className="text-sm"
                    >
                      {company.industry}
                    </Text>
                  </div>
                </div>

                {/* Company Info */}
                <div className="flex-1 space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin size={14} />
                    <span className="truncate">{company.location}</span>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Users size={14} />
                    <span>{company.company_size} employees</span>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Briefcase size={14} />
                    <span>{company.open_positions} open positions</span>
                  </div>
                </div>

                {/* Rating and Description */}
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Rate
                      disabled
                      allowHalf
                      value={company.rating}
                      className="text-sm"
                    />
                    <Text className="text-sm font-medium">({company.rating})</Text>
                  </div>

                  <Text
                    type="secondary"
                    className="text-xs line-clamp-2"
                  >
                    {company.description}
                  </Text>
                </div>
                {/* Action Button */}
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <Button
                    type="primary"
                    size="middle"
                    block
                    onClick={(e) => {
                      e.stopPropagation();
                      onViewCompany?.(company);
                    }}
                    className="items-end"
                  >
                    View Jobs
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );
};

export default CompaniesSection;
