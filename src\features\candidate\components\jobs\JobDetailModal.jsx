/**
 * Job Detail Modal Component
 * Displays detailed information about a job in a modal
 */

import React from 'react';
import {
  Modal,
  Typography,
  Tag,
  Button,
  Space,
  Divider,
  Row,
  Col,
  Avatar,
  Progress,
  Tooltip,
} from 'antd';
import {
  EnvironmentOutlined,
  ClockCircleOutlined,
  BookOutlined,
  HeartOutlined,
  HeartFilled,
  CalendarOutlined,
  TeamOutlined,
  BankOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

import {
  formatSalaryRange,
  formatJobDate,
  getMatchColor,
} from '@/features/candidate/utils/jobUtils';

// Rupee Icon Component
const RupeeIcon = () => <span style={{ fontWeight: 'bold', fontSize: '14px' }}>₹</span>;

const { Title, Text, Paragraph } = Typography;

const JobDetailModal = ({
  job,
  open,
  onClose,
  onApply,
  onSave,
  isApplied = false,
  isSaved = false,
  loading = false,
}) => {
  if (!job) return null;

  const company = job.companies || job.company_profiles;
  const matchPercentage = job.matchPercentage || 0;

  return (
    <Modal
      title={null}
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
      className="job-detail-modal"
      styles={{
        body: { padding: 0 },
      }}
    >
      <div className="job-detail-content">
        {/* Header */}
        <div className="job-detail-header p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
          <Row
            gutter={[16, 16]}
            align="middle"
          >
            <Col flex="auto">
              <div className="flex items-start gap-4">
                {company?.company_logo_url && (
                  <Avatar
                    src={company.company_logo_url}
                    size={64}
                    shape="square"
                    className="border"
                  />
                )}
                <div className="flex-1">
                  <Title
                    level={3}
                    className="mb-2"
                  >
                    {job.title}
                  </Title>
                  <Text className="text-lg font-medium">{company?.company_name}</Text>
                  {matchPercentage > 0 && (
                    <div className="mt-2">
                      <Text
                        type="secondary"
                        className="text-sm"
                      >
                        Match Score:
                      </Text>
                      <Progress
                        percent={matchPercentage}
                        size="small"
                        strokeColor={getMatchColor(matchPercentage)}
                        className="ml-2"
                        style={{ width: 120 }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </Col>
            <Col>
              <Space
                direction="vertical"
                size="small"
              >
                <Button
                  type="primary"
                  size="large"
                  onClick={onApply}
                  disabled={isApplied || loading}
                  icon={isApplied ? <CheckCircleOutlined /> : null}
                  className="w-full"
                >
                  {isApplied ? 'Applied' : 'Apply Now'}
                </Button>
                <Button
                  icon={isSaved ? <HeartFilled /> : <HeartOutlined />}
                  onClick={onSave}
                  disabled={loading}
                  className={`w-full ${isSaved ? 'text-red-500 border-red-500' : ''}`}
                >
                  {isSaved ? 'Saved' : 'Save Job'}
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Job Info Tags */}
        <div className="p-6 border-b">
          <Space
            wrap
            size="middle"
          >
            <Tag
              icon={<EnvironmentOutlined />}
              color="blue"
              className="px-3 py-1"
            >
              {job.location}
            </Tag>
            {job.salary_range && (
              <Tag
                icon={<RupeeIcon />}
                color="green"
                className="px-3 py-1"
              >
                {formatSalaryRange(job.salary_range)}
              </Tag>
            )}
            <Tag
              icon={<ClockCircleOutlined />}
              color="orange"
              className="px-3 py-1"
            >
              {job.experience_level}
            </Tag>
            {job.employment_type && (
              <Tag
                icon={<BookOutlined />}
                color="purple"
                className="px-3 py-1"
              >
                {job.employment_type}
              </Tag>
            )}
            <Tag
              icon={<CalendarOutlined />}
              color="default"
              className="px-3 py-1"
            >
              Posted {formatJobDate(job.created_at)}
            </Tag>
          </Space>
        </div>

        {/* Job Description */}
        <div className="p-6">
          <Title
            level={4}
            className="mb-3"
          >
            Job Description
          </Title>
          <Paragraph className="text-base leading-relaxed whitespace-pre-line">
            {job.description}
          </Paragraph>

          {/* Required Skills */}
          {job.required_skills && job.required_skills.length > 0 && (
            <>
              <Divider />
              <Title
                level={4}
                className="mb-3"
              >
                Required Skills
              </Title>
              <Space wrap>
                {job.required_skills.map((skill, index) => (
                  <Tag
                    key={index}
                    className="mb-2 px-3 py-1"
                  >
                    {skill}
                  </Tag>
                ))}
              </Space>
            </>
          )}

          {/* Company Information */}
          {company && (
            <>
              <Divider />
              <Title
                level={4}
                className="mb-3"
              >
                About the Company
              </Title>
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Space
                    direction="vertical"
                    size="small"
                    className="w-full"
                  >
                    <div className="flex items-center gap-2">
                      <BankOutlined className="text-gray-500" />
                      <Text strong>{company.company_name}</Text>
                    </div>
                    {company.company_type && (
                      <div className="flex items-center gap-2">
                        <Text type="secondary">Industry:</Text>
                        <Text>{company.company_type}</Text>
                      </div>
                    )}
                    {company.company_size && (
                      <div className="flex items-center gap-2">
                        <TeamOutlined className="text-gray-500" />
                        <Text type="secondary">Company Size:</Text>
                        <Text>{company.company_size}</Text>
                      </div>
                    )}
                  </Space>
                </Col>
              </Row>
            </>
          )}

          {/* Additional Job Details */}
          <Divider />
          <Row gutter={[24, 16]}>
            <Col
              xs={24}
              sm={12}
            >
              <div className="space-y-2">
                <Text
                  type="secondary"
                  className="block"
                >
                  Job Status
                </Text>
                <Tag color={job.status === 'active' ? 'green' : 'orange'}>
                  {job.status?.toUpperCase()}
                </Tag>
              </div>
            </Col>
            <Col
              xs={24}
              sm={12}
            >
              <div className="space-y-2">
                <Text
                  type="secondary"
                  className="block"
                >
                  Posted Date
                </Text>
                <Text>{formatJobDate(job.created_at)}</Text>
              </div>
            </Col>
          </Row>
        </div>

        {/* Footer Actions */}
        <div className="p-6 border-t bg-gray-50 dark:bg-gray-800">
          <Row
            gutter={[16, 16]}
            justify="space-between"
            align="middle"
          >
            <Col>
              <Text type="secondary">Interested in this position?</Text>
            </Col>
            <Col>
              <Space>
                <Button onClick={onClose}>Close</Button>
                <Button
                  type="primary"
                  onClick={onApply}
                  disabled={isApplied || loading}
                  loading={loading}
                  icon={isApplied ? <CheckCircleOutlined /> : null}
                >
                  {isApplied ? 'Already Applied' : 'Apply Now'}
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </div>
    </Modal>
  );
};

export default JobDetailModal;
