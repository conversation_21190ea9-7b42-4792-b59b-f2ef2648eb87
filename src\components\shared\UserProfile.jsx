import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Row,
  Col,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  Select,
  Divider,
  Tabs,
  Tag,
  Space,
  message,
} from 'antd';
import {
  FaUser,
  FaUpload,
  FaEdit,
  FaLinkedin,
  FaGithub,
  FaGlobe,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaDollarSign,
} from 'react-icons/fa';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * Reusable Profile component that can be used for different user types
 * @param {Object} props
 * @param {Object} props.profile - User profile data
 * @param {Function} props.updateProfile - Function to update profile
 * @param {boolean} props.loading - Loading state
 * @param {string} props.userType - Type of user (recruiter, candidate, company)
 * @param {Array} props.specializationOptions - Options for specialization field
 * @param {Array} props.languageOptions - Options for language field
 * @param {Array} props.additionalTabs - Additional tabs to render
 */
const UserProfile = ({
  profile,
  updateProfile,
  loading = false,
  userType = 'user',
  specializationOptions = [],
  languageOptions = [],
  additionalTabs = [],
}) => {
  const { isMobile } = useDeviceDetect();
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(false);

  console.log(profile);

  useEffect(() => {
    if (profile) {
      form.setFieldsValue({
        fullName: profile.fullName || '',
        email: profile.email || '',
        phone: profile.phone || '',
        location: profile.location || '',
        bio: profile.bio || '',
        specialization: profile.specialization || [],
        yearsOfExperience: profile.yearsOfExperience || '',
        linkedinUrl: profile.linkedinUrl || '',
        githubUrl: profile.githubUrl || '',
        personalWebsite: profile.personalWebsite || '',
        languages: profile.languages || [],
        hourlyRate: profile.hourlyRate || '',
        availability: profile.availability || '',
      });
    }
  }, [profile, form]);

  const handleSubmit = async (values) => {
    try {
      console.log(values);
      await updateProfile(values);
      message.success('Profile updated successfully');
      setEditMode(false);
    } catch (error) {
      message.error('Failed to update profile');
      console.error(error);
    }
  };

  // Default options if none provided
  const defaultSpecializationOptions = [
    'Frontend Development',
    'Backend Development',
    'Full Stack',
    'DevOps',
    'Mobile Development',
    'UI/UX Design',
    'Product Management',
    'Data Science',
    'Machine Learning',
    'Artificial Intelligence',
    'Cybersecurity',
    'Cloud Computing',
  ];

  const defaultLanguageOptions = [
    'English',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
    'Russian',
    'Portuguese',
    'Arabic',
    'Hindi',
    'Korean',
    'Italian',
  ];

  const specOptions =
    specializationOptions.length > 0 ? specializationOptions : defaultSpecializationOptions;
  const langOptions = languageOptions.length > 0 ? languageOptions : defaultLanguageOptions;

  return (
    <div className="profile-page">
      <Card
        bordered={false}
        className="mb-6"
      >
        <Row
          gutter={[24, 24]}
          align="middle"
        >
          <Col
            xs={24}
            md={8}
            className="text-center md:text-left"
          >
            <div className="flex flex-col md:flex-row items-center">
              <Avatar
                size={100}
                src={profile?.avatarUrl}
                icon={!profile?.avatarUrl && <FaUser />}
                className="mb-4 md:mb-0 md:mr-6"
              />
              <div>
                <Title
                  level={3}
                  className="mb-1"
                >
                  {profile?.full_name || profile?.company_name || 'Your Name'}
                </Title>
                <Text className="text-primary block mb-2">
                  {userType.charAt(0).toUpperCase() + userType.slice(1)}
                </Text>
                {!editMode && (
                  <Button
                    type="primary"
                    icon={<FaEdit style={{ marginRight: 8 }} />}
                    onClick={() => setEditMode(true)}
                  >
                    Edit Profile
                  </Button>
                )}
              </div>
            </div>
          </Col>
          <Col
            xs={24}
            md={16}
          >
            {!editMode ? (
              <div className="profile-info">
                <Row gutter={[16, 16]}>
                  <Col
                    xs={24}
                    md={12}
                  >
                    <div className="flex items-center mb-3">
                      <FaEnvelope className="mr-2 text-muted" />
                      <Text>{profile?.email || '<EMAIL>'}</Text>
                    </div>
                    <div className="flex items-center mb-3">
                      <FaPhone className="mr-2 text-muted" />
                      <Text>{profile?.phone || 'Not specified'}</Text>
                    </div>
                    <div className="flex items-center">
                      <FaMapMarkerAlt className="mr-2 text-muted" />
                      <Text>{profile?.location || 'Not specified'}</Text>
                    </div>
                  </Col>
                  <Col
                    xs={24}
                    md={12}
                  >
                    <div className="flex items-center mb-3">
                      <FaCalendarAlt className="mr-2 text-muted" />
                      <Text>Experience: {profile?.yearsOfExperience || '0'} years</Text>
                    </div>
                    <div className="flex items-center mb-3">
                      <FaDollarSign className="mr-2 text-muted" />
                      <Text>Rate: ${profile?.hourlyRate || '0'}/hour</Text>
                    </div>
                    <div className="social-links">
                      {profile?.linkedinUrl && (
                        <a
                          href={profile.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="mr-3"
                        >
                          <FaLinkedin style={{ fontSize: '18px' }} />
                        </a>
                      )}
                      {profile?.githubUrl && (
                        <a
                          href={profile.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="mr-3"
                        >
                          <FaGithub style={{ fontSize: '18px' }} />
                        </a>
                      )}
                      {profile?.personalWebsite && (
                        <a
                          href={profile.personalWebsite}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <FaGlobe style={{ fontSize: '18px' }} />
                        </a>
                      )}
                    </div>
                  </Col>
                </Row>
              </div>
            ) : (
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  fullName: profile?.fullName || '',
                  email: profile?.email || '',
                  // Other fields initialized in useEffect
                }}
              >
                <Row gutter={[16, 16]}>
                  <Col
                    xs={24}
                    md={12}
                  >
                    <Form.Item
                      name="fullName"
                      label="Full Name"
                      rules={[{ required: true, message: 'Please enter your name' }]}
                    >
                      <Input placeholder="Your full name" />
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={12}
                  >
                    <Form.Item
                      name="email"
                      label="Email"
                      rules={[
                        { required: true, message: 'Please enter your email' },
                        { type: 'email', message: 'Please enter a valid email' },
                      ]}
                    >
                      <Input
                        placeholder="Your email"
                        disabled
                      />
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={12}
                  >
                    <Form.Item
                      name="phone"
                      label="Phone"
                    >
                      <Input placeholder="Your phone number" />
                    </Form.Item>
                  </Col>
                  <Col
                    xs={24}
                    md={12}
                  >
                    <Form.Item
                      name="location"
                      label="Location"
                    >
                      <Input placeholder="City, Country" />
                    </Form.Item>
                  </Col>
                </Row>
                <div className="form-actions mt-4">
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                    >
                      Save Changes
                    </Button>
                    <Button onClick={() => setEditMode(false)}>Cancel</Button>
                  </Space>
                </div>
              </Form>
            )}
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="about">
        <TabPane
          tab="About"
          key="about"
        >
          <Card bordered={false}>
            {!editMode ? (
              <>
                <Title level={4}>Bio</Title>
                <Paragraph className="mb-6">
                  {profile?.bio || 'No bio information provided yet.'}
                </Paragraph>

                <Title level={4}>Specialization</Title>
                <div className="mb-6">
                  {profile?.specialization?.length > 0 ? (
                    <Space
                      size={[0, 8]}
                      wrap
                    >
                      {profile.specialization.map((skill) => (
                        <Tag
                          key={skill}
                          color="blue"
                        >
                          {skill}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary">No specialization specified</Text>
                  )}
                </div>

                <Title level={4}>Languages</Title>
                <div className="mb-6">
                  {profile?.languages?.length > 0 ? (
                    <Space
                      size={[0, 8]}
                      wrap
                    >
                      {profile.languages.map((language) => (
                        <Tag key={language}>{language}</Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary">No languages specified</Text>
                  )}
                </div>
              </>
            ) : (
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
              >
                <Form.Item
                  name="bio"
                  label="Bio"
                >
                  <Input.TextArea
                    rows={4}
                    placeholder="Tell us about yourself"
                  />
                </Form.Item>

                <Form.Item
                  name="specialization"
                  label="Specialization"
                >
                  <Select
                    mode="multiple"
                    placeholder="Select your specializations"
                    style={{ width: '100%' }}
                  >
                    {specOptions.map((option) => (
                      <Option
                        key={option}
                        value={option}
                      >
                        {option}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="languages"
                  label="Languages"
                >
                  <Select
                    mode="multiple"
                    placeholder="Select languages you speak"
                    style={{ width: '100%' }}
                  >
                    {langOptions.map((option) => (
                      <Option
                        key={option}
                        value={option}
                      >
                        {option}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="yearsOfExperience"
                  label="Years of Experience"
                >
                  <Input
                    type="number"
                    min={0}
                    placeholder="Years of experience"
                  />
                </Form.Item>

                <Form.Item
                  name="hourlyRate"
                  label="Hourly Rate ($)"
                >
                  <Input
                    type="number"
                    min={0}
                    placeholder="Your hourly rate"
                  />
                </Form.Item>

                <div className="form-actions mt-4">
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                    >
                      Save Changes
                    </Button>
                    <Button onClick={() => setEditMode(false)}>Cancel</Button>
                  </Space>
                </div>
              </Form>
            )}
          </Card>
        </TabPane>
        <TabPane
          tab="Social Profiles"
          key="social"
        >
          <Card bordered={false}>
            {!editMode ? (
              <div className="social-profiles">
                <div className="mb-4">
                  <Title level={5}>LinkedIn</Title>
                  <a
                    href={profile?.linkedinUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {profile?.linkedinUrl || 'Not specified'}
                  </a>
                </div>
                <Divider />
                <div className="mb-4">
                  <Title level={5}>GitHub</Title>
                  <a
                    href={profile?.githubUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {profile?.githubUrl || 'Not specified'}
                  </a>
                </div>
                <Divider />
                <div>
                  <Title level={5}>Personal Website</Title>
                  <a
                    href={profile?.personalWebsite}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {profile?.personalWebsite || 'Not specified'}
                  </a>
                </div>
              </div>
            ) : (
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
              >
                <Form.Item
                  name="linkedinUrl"
                  label="LinkedIn URL"
                >
                  <Input
                    prefix={<FaLinkedin />}
                    placeholder="https://linkedin.com/in/yourprofile"
                  />
                </Form.Item>

                <Form.Item
                  name="githubUrl"
                  label="GitHub URL"
                >
                  <Input
                    prefix={<FaGithub />}
                    placeholder="https://github.com/yourusername"
                  />
                </Form.Item>

                <Form.Item
                  name="personalWebsite"
                  label="Personal Website"
                >
                  <Input
                    prefix={<FaGlobe />}
                    placeholder="https://yourwebsite.com"
                  />
                </Form.Item>

                <div className="form-actions mt-4">
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                    >
                      Save Changes
                    </Button>
                    <Button onClick={() => setEditMode(false)}>Cancel</Button>
                  </Space>
                </div>
              </Form>
            )}
          </Card>
        </TabPane>

        {/* Render additional tabs if provided */}
        {additionalTabs.map((tab) => (
          <TabPane
            tab={tab.title}
            key={tab.key}
          >
            {tab.content}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default UserProfile;
