/**
 * Interviewer Profile Component
 * 
 * Profile management for interviewers including:
 * - Personal information
 * - Specializations
 * - Experience details
 * - Availability settings
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  Typography,
  Avatar,
  Upload,
  Tag,
  Divider,
  Space,
  Alert
} from 'antd';
import {
  UserOutlined,
  UploadOutlined,
  SaveOutlined,
  EditOutlined
} from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';
import { 
  INTERVIEWER_SPECIALIZATIONS, 
  INTERVIEWER_EXPERIENCE_LEVELS,
  INTERVIEWER_INDUSTRIES 
} from '../constants';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const InterviewerProfile = () => {
  const { user, profile, updateProfile } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);

  useEffect(() => {
    if (profile) {
      form.setFieldsValue({
        full_name: profile.full_name,
        email: profile.email,
        mobile_number: profile.mobile_number,
        current_designation: profile.current_designation,
        current_company: profile.current_company,
        years_experience: profile.years_experience,
        specializations: profile.specializations || [],
        industries: profile.industries || [],
        bio: profile.bio,
        linkedin_url: profile.linkedin_url,
        hourly_rate: profile.hourly_rate
      });
    }
  }, [profile, form]);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const result = await updateProfile(values);
      if (result.success) {
        setEditing(false);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = (info) => {
    // Handle profile photo upload
    console.log('Upload info:', info);
  };

  if (!user || profile?.role !== 'interviewer') {
    return (
      <Alert
        message="Access Denied"
        description="This page is only accessible to interviewers."
        type="error"
        showIcon
      />
    );
  }

  return (
    <div className="interviewer-profile p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <Title level={2}>Interviewer Profile</Title>
          <Button
            type={editing ? 'default' : 'primary'}
            icon={editing ? <SaveOutlined /> : <EditOutlined />}
            onClick={() => {
              if (editing) {
                form.submit();
              } else {
                setEditing(true);
              }
            }}
            loading={loading}
          >
            {editing ? 'Save Changes' : 'Edit Profile'}
          </Button>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={!editing}
      >
        <Row gutter={24}>
          {/* Profile Photo and Basic Info */}
          <Col xs={24} lg={8}>
            <Card title="Profile Photo" className="mb-6">
              <div className="text-center">
                <Avatar
                  size={120}
                  src={profile?.profile_photo_url}
                  icon={<UserOutlined />}
                  className="mb-4"
                />
                {editing && (
                  <Upload
                    name="avatar"
                    listType="picture"
                    showUploadList={false}
                    onChange={handleUpload}
                  >
                    <Button icon={<UploadOutlined />}>
                      Upload Photo
                    </Button>
                  </Upload>
                )}
              </div>
            </Card>

            <Card title="Quick Stats" className="mb-6">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <Text>Experience:</Text>
                  <Text strong>{profile?.years_experience || 0} years</Text>
                </div>
                <div className="flex justify-between">
                  <Text>Specializations:</Text>
                  <Text strong>{profile?.specializations?.length || 0}</Text>
                </div>
                <div className="flex justify-between">
                  <Text>Industries:</Text>
                  <Text strong>{profile?.industries?.length || 0}</Text>
                </div>
                <div className="flex justify-between">
                  <Text>Hourly Rate:</Text>
                  <Text strong>₹{profile?.hourly_rate || 0}</Text>
                </div>
              </div>
            </Card>
          </Col>

          {/* Main Profile Form */}
          <Col xs={24} lg={16}>
            <Card title="Personal Information" className="mb-6">
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="full_name"
                    label="Full Name"
                    rules={[{ required: true, message: 'Please enter your full name' }]}
                  >
                    <Input placeholder="Enter your full name" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="email"
                    label="Email"
                    rules={[
                      { required: true, message: 'Please enter your email' },
                      { type: 'email', message: 'Please enter a valid email' }
                    ]}
                  >
                    <Input placeholder="Enter your email" disabled />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="mobile_number"
                    label="Mobile Number"
                    rules={[{ required: true, message: 'Please enter your mobile number' }]}
                  >
                    <Input placeholder="Enter your mobile number" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="linkedin_url"
                    label="LinkedIn Profile"
                  >
                    <Input placeholder="https://linkedin.com/in/yourprofile" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            <Card title="Professional Information" className="mb-6">
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="current_designation"
                    label="Current Designation"
                    rules={[{ required: true, message: 'Please enter your designation' }]}
                  >
                    <Input placeholder="e.g., Senior Software Engineer" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="current_company"
                    label="Current Company"
                    rules={[{ required: true, message: 'Please enter your company' }]}
                  >
                    <Input placeholder="e.g., Tech Corp" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="years_experience"
                    label="Years of Experience"
                    rules={[{ required: true, message: 'Please select your experience level' }]}
                  >
                    <Select placeholder="Select experience level">
                      {INTERVIEWER_EXPERIENCE_LEVELS.map(level => (
                        <Option key={level.value} value={level.value}>
                          {level.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="hourly_rate"
                    label="Hourly Rate (₹)"
                  >
                    <Input type="number" placeholder="e.g., 2000" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="bio"
                label="Professional Bio"
              >
                <TextArea
                  rows={4}
                  placeholder="Brief description of your professional background and expertise..."
                />
              </Form.Item>
            </Card>

            <Card title="Expertise & Specializations" className="mb-6">
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="specializations"
                    label="Assessment Specializations"
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select your specializations"
                      options={INTERVIEWER_SPECIALIZATIONS.map(spec => ({
                        label: spec,
                        value: spec
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="industries"
                    label="Industry Experience"
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select industries you have experience in"
                      options={INTERVIEWER_INDUSTRIES.map(industry => ({
                        label: industry,
                        value: industry
                      }))}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Display current specializations */}
              {profile?.specializations && profile.specializations.length > 0 && (
                <div className="mt-4">
                  <Text strong>Current Specializations:</Text>
                  <div className="mt-2">
                    {profile.specializations.map(spec => (
                      <Tag key={spec} color="blue" className="mb-1">
                        {spec}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

              {/* Display current industries */}
              {profile?.industries && profile.industries.length > 0 && (
                <div className="mt-4">
                  <Text strong>Industry Experience:</Text>
                  <div className="mt-2">
                    {profile.industries.map(industry => (
                      <Tag key={industry} color="green" className="mb-1">
                        {industry}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {editing && (
              <Card>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    Save Changes
                  </Button>
                  <Button
                    onClick={() => {
                      setEditing(false);
                      form.resetFields();
                    }}
                  >
                    Cancel
                  </Button>
                </Space>
              </Card>
            )}
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default InterviewerProfile;
