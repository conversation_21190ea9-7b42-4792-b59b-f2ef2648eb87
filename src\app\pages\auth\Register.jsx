import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SignUp } from '@clerk/clerk-react';
import { motion } from 'framer-motion';
import { Typography } from 'antd';
import useAuth from '@/hooks/useAuth';

const { Title, Paragraph } = Typography;

const Register = () => {
  const navigate = useNavigate();
  const { user, role, rolePath, isAuthenticated } = useAuth();

  // Redirect if already logged in
  useEffect(() => {
    if (isAuthenticated && user && (role || rolePath)) {
      const path =
        rolePath || (role === 'interviewer' ? 'sourcer' : role === 'company' ? 'org' : role);
      if (path) {
        navigate(`/${path}/dashboard`, { replace: true });
      }
    }
  }, [isAuthenticated, user, role, rolePath, navigate]);

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeIn}
        className="w-full max-w-md"
      >
        <div className="text-center mb-8">
          <Title
            level={2}
            className="mb-2"
          >
            Create Your Account
          </Title>
          <Paragraph className="text-text-secondary">
            Join our platform and start your journey
          </Paragraph>
        </div>

        <div className="flex justify-center">
          <SignUp
            appearance={{
              elements: {
                rootBox: 'w-full',
                card: 'shadow-lg rounded-xl border',
                headerTitle: 'hidden',
                headerSubtitle: 'hidden',
                socialButtonsBlockButton: 'rounded-lg h-12',
                formButtonPrimary: 'rounded-lg h-12 bg-primary hover:bg-primary-hover',
                formFieldInput: 'rounded-lg h-12',
                footerActionLink: 'text-primary hover:text-primary-hover',
              },
            }}
            forceRedirectUrl={window.location.origin + '/dashboard'}
            signInUrl="/login"
          />
        </div>
      </motion.div>
    </div>
  );
};

export default Register;
