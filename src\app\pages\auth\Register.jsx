import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card, Col, Row, Steps, Typography, Result } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import showToast from '@/utils/toast';
import UnifiedRegistrationForm from '@/components/auth/UnifiedRegistrationForm';
import OTPVerification from '@/components/auth/OTPVerification';
import RegistrationService from '@/services/registrationService';

const { Title, Text, Paragraph } = Typography;

const Register = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [role, setRole] = useState('');
  const [registrationData, setRegistrationData] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleRoleSelect = (selectedRole) => {
    setRole(selectedRole);
    setCurrentStep(1);
  };

  const handleRegistrationFormSubmit = async (userData) => {
    setLoading(true);
    try {
      const result = await RegistrationService.signUpWithOTP(userData);

      if (result.success) {
        setRegistrationData(userData);
        setCurrentStep(2);
        showToast.success('Registration initiated! Please check your email for verification code.');
      } else {
        throw new Error('Registration failed - please try again');
      }
    } catch (error) {
      showToast.error(error.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const handleOTPVerificationSuccess = async (verificationData) => {
    try {
      setCurrentStep(3);
      showToast.success('Email verified successfully!');
    } catch (error) {
      showToast.error('Verification completed but there was an issue. Please try logging in.');
    }
  };

  const handleGoToDashboard = () => {
    // Map role to URL path
    const rolePath = role === 'interviewer' ? 'sourcer' : role === 'company' ? 'org' : role;
    navigate(`/${rolePath}/dashboard`);
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.75 } },
  };

  const getRoleDisplayName = (role) => {
    switch (role) {
      case 'candidate':
        return 'Candidate';
      case 'interviewer':
        return 'Recruiter';
      case 'company':
        return 'Company';
      default:
        return 'User';
    }
  };

  const steps = [
    {
      title: 'Select Role',
      content: (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="min-h-[400px]"
        >
          <Row
            gutter={[26, 26]}
            className="min-h-[400px] text-center"
          >
            <Col
              xs={24}
              md={12}
              className=""
            >
              <div className="p-6">
                <Title
                  level={3}
                  className="text-2xl md:text-3xl font-bold mb-2"
                >
                  Select your role to get started!
                </Title>
                <Paragraph className="text-text-secondary mb-8">
                  Choose to join as a candidate, recruiter or company.
                </Paragraph>

                <div className="flex flex-col gap-4 mx-auto">
                  <Button
                    size="large"
                    type={role === 'candidate' ? 'primary' : 'default'}
                    className="h-14 flex items-center justify-center px-6 transition-all hover:shadow-md"
                    onClick={() => handleRoleSelect('candidate')}
                  >
                    <span className="font-medium">Candidate</span>
                  </Button>

                  <Button
                    size="large"
                    type={role === 'interviewer' ? 'primary' : 'default'}
                    className="h-14 flex items-center justify-center px-6 transition-all hover:shadow-md"
                    onClick={() => handleRoleSelect('interviewer')}
                  >
                    <span className="font-medium">Recruiter</span>
                  </Button>

                  <Button
                    size="large"
                    type={role === 'company' ? 'primary' : 'default'}
                    className="h-14 flex items-center justify-center px-6 transition-all hover:shadow-md"
                    onClick={() => handleRoleSelect('company')}
                  >
                    <span className="font-medium">Company</span>
                  </Button>
                </div>
              </div>
            </Col>

            <Col
              xs={24}
              md={12}
              className="bg-card rounded-lg shadow-sm overflow-hidden"
            >
              <div className="p-6 h-full flex flex-col justify-between">
                <div>
                  <Title
                    level={4}
                    className="mb-4 text-center"
                  >
                    Smart Hiring, Seamless Interviews.
                  </Title>
                  <Paragraph className="text-text-secondary">
                    Sign up in minutes and start scheduling interviews today!
                  </Paragraph>
                </div>

                <div className="mt-1 flex justify-center">
                  <img
                    src="https://img.freepik.com/free-vector/interview-concept-illustration_114360-1678.jpg?semt=ais_hybrid&w=740"
                    alt="Interview illustration"
                    className="w-lg max-w-xs mx-auto"
                  />
                </div>
              </div>
            </Col>
          </Row>
        </motion.div>
      ),
    },
    {
      title: 'Create Account',
      content: (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="min-h-[400px]"
        >
          <Row
            gutter={[24, 24]}
            className="min-h-[400px]"
          >
            <Col
              xs={24}
              md={12}
              className="hidden md:block"
            >
              <div className="p-6 h-full flex flex-col justify-between">
                <div>
                  <Title
                    level={3}
                    className="text-2xl md:text-3xl font-bold mb-2"
                  >
                    Join Our Community
                  </Title>
                  <Paragraph className="text-text-secondary mb-6">
                    Create your {getRoleDisplayName(role).toLowerCase()} account to access all
                    features.
                  </Paragraph>

                  <div className="space-y-4 mt-8">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
                        1
                      </div>
                      <div>
                        <Text strong>Enter your details</Text>
                        <Text className="block text-text-secondary">
                          Provide your basic information
                        </Text>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
                        2
                      </div>
                      <div>
                        <Text strong>Verify your email</Text>
                        <Text className="block text-text-secondary">
                          Enter the 6-digit code sent to your email
                        </Text>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
                        3
                      </div>
                      <div>
                        <Text strong>Start using the platform</Text>
                        <Text className="block text-text-secondary">
                          Access your dashboard immediately
                        </Text>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Col>

            <Col
              xs={24}
              md={12}
              className="bg-card rounded-lg shadow-md"
            >
              <div className="p-6">
                <Title
                  level={4}
                  className="mb-4"
                >
                  Create {getRoleDisplayName(role)} Account
                </Title>
                <Text
                  type="secondary"
                  className="mb-6 block"
                >
                  Already have an account?{' '}
                  <Link
                    to="/login"
                    className="text-primary hover:text-primary-hover"
                  >
                    Log in
                  </Link>
                </Text>

                <UnifiedRegistrationForm
                  role={role}
                  onSuccess={handleRegistrationFormSubmit}
                  loading={loading}
                />
              </div>
            </Col>
          </Row>
        </motion.div>
      ),
    },
    {
      title: 'Verify Email',
      content: (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="min-h-[400px] flex items-center justify-center"
        >
          <OTPVerification
            email={registrationData?.email}
            onSuccess={handleOTPVerificationSuccess}
          />
        </motion.div>
      ),
    },
    {
      title: 'Success',
      content: (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="min-h-[400px] flex items-center justify-center"
        >
          <Result
            icon={<CheckCircleOutlined style={{ color: 'var(--primary)', fontSize: 64 }} />}
            title="Registration Successful!"
            subTitle={`Welcome to Flyt! Your ${getRoleDisplayName(role).toLowerCase()} account has been created and verified successfully.`}
            extra={[
              <Button
                type="primary"
                key="dashboard"
                size="large"
                onClick={handleGoToDashboard}
                className="h-12 rounded-lg font-medium"
              >
                Go to Dashboard
              </Button>,
              <Button
                key="login"
                size="large"
                onClick={() => navigate('/login')}
                className="h-12 rounded-lg font-medium"
              >
                Go to Login
              </Button>,
            ]}
          />
        </motion.div>
      ),
    },
  ];

  return (
    <div className="flex justify-center">
      <div className="w-full flex justify-center max-w-6xl">
        <Card
          variant="bordered"
          className="shadow-lg rounded-xl overflow-hidden"
        >
          <Steps
            current={currentStep}
            className="px-8 pb-4"
          >
            <Steps.Step title="Select Role" />
            <Steps.Step title="Enter Details" />
            <Steps.Step title="Verify OTP" />
            <Steps.Step title="Complete" />
          </Steps>

          <div className="steps-content pt-8">{steps[currentStep].content}</div>

          {currentStep > 0 && currentStep < 3 && (
            <div className="steps-action px-8 py-4 border-t">
              <Button
                onClick={() => setCurrentStep(currentStep - 1)}
                className="hover:bg-secondary transition-colors"
              >
                Back
              </Button>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Register;
