import { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { SignUp } from '@clerk/clerk-react';
import useAuth from '@/hooks/useAuth';
import { useColorModeStore } from '@/store/colorMode.store';
import { getClerkTheme } from '@/styles/clerkTheme';

const Register = () => {
  const navigate = useNavigate();
  const { user, role, rolePath, isAuthenticated } = useAuth();
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Redirect if already logged in
  useEffect(() => {
    if (isAuthenticated && user && (role || rolePath)) {
      const path =
        rolePath || (role === 'interviewer' ? 'sourcer' : role === 'company' ? 'org' : role);
      if (path) {
        navigate(`/${path}/dashboard`, { replace: true });
      }
    }
  }, [isAuthenticated, user, role, rolePath, navigate]);

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <main className="flex justify-center items-center min-h-[60vh]">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeIn}
        className="w-full max-w-md"
      >
        <div className="flex justify-center">
          <SignUp
            appearance={getClerkTheme(isDark)}
            forceRedirectUrl={window.location.origin + '/dashboard'}
            signInUrl="/login"
          />
        </div>
      </motion.div>
    </main>
  );
};

export default Register;
