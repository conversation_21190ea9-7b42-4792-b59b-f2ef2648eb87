import React from 'react';
import { Layout } from 'antd';
import { <PERSON> } from 'react-router-dom';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Footer } = Layout;

/**
 * Footer component for the Candidate Layout
 */
const CandidateFooter = () => {
  const { isMobile, isTablet } = useDeviceDetect();

  return (
    <Footer className="text-center py-4 sm:py-6 bg-white dark:bg-gray-800 shadow-2xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-6 text-left">
          <div>
            <h4 className="font-medium mb-3">Information</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="#">About Us</Link>
              </li>
              <li>
                <Link to="#">Terms & Conditions</Link>
              </li>
              <li>
                <Link to="#">Privacy Policy</Link>
              </li>
              <li>
                <Link to="#">Careers</Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-3">Jobseekers</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="#">Job Search</Link>
              </li>
              <li>
                <Link to="#">Resume Services</Link>
              </li>
              <li>
                <Link to="#">Career Resources</Link>
              </li>
              <li>
                <Link to="#">Help Center</Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-3">Employers</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="#">Post a Job</Link>
              </li>
              <li>
                <Link to="#">Recruitment Solutions</Link>
              </li>
              <li>
                <Link to="#">Pricing</Link>
              </li>
              <li>
                <Link to="#">Contact Sales</Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-3">Connect with us</h4>
            <div className="flex space-x-3 mb-3 justify-center sm:justify-start">
              <Link
                to="#"
                className="text-blue-500 hover:text-blue-600 transition-colors"
              >
                <i className="fab fa-facebook text-lg"></i>
              </Link>
              <Link
                to="#"
                className="text-blue-400 hover:text-blue-500 transition-colors"
              >
                <i className="fab fa-twitter text-lg"></i>
              </Link>
              <Link
                to="#"
                className="text-pink-500 hover:text-pink-600 transition-colors"
              >
                <i className="fab fa-instagram text-lg"></i>
              </Link>
              <Link
                to="#"
                className="text-blue-700 hover:text-blue-800 transition-colors"
              >
                <i className="fab fa-linkedin text-lg"></i>
              </Link>
            </div>
            <div className="flex space-x-2 mt-3 justify-center sm:justify-start">
              <Link
                to="#"
                className="hover:opacity-80 transition-opacity"
              >
                <img
                  src="/app-store.png"
                  alt="App Store"
                  className="h-8 sm:h-10"
                />
              </Link>
              <Link
                to="#"
                className="hover:opacity-80 transition-opacity"
              >
                <img
                  src="/play-store.png"
                  alt="Play Store"
                  className="h-8 sm:h-10"
                />
              </Link>
            </div>
          </div>
        </div>
        <div className="border-t pt-4 text-xs sm:text-sm text-center">
          Flyt ©{new Date().getFullYear()} - All rights reserved
        </div>
      </div>
    </Footer>
  );
};

export default CandidateFooter;
