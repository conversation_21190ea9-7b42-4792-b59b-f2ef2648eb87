/**
 * Profile utility functions for candidate features
 */

/**
 * Calculate profile completion percentage
 * @param {Object} profile - Candidate profile object
 * @returns {number} Completion percentage (0-100)
 */
export const calculateProfileCompletion = (profile) => {
  if (!profile || typeof profile !== 'object') return 0;

  const requiredFields = [
    'full_name',
    'city',
    'role_applied_for',
    'years_experience',
    'current_job_title',
    'skills',
    'expected_ctc',
    'notice_period',
  ];

  const optionalFields = [
    'current_company',
    'linkedin_url',
    'profile_photo_url',
    'resume_url',
    'languages',
  ];

  let completedRequired = 0;
  let completedOptional = 0;

  // Check required fields (70% weight)
  requiredFields.forEach(field => {
    if (profile[field] && profile[field] !== '' && 
        (Array.isArray(profile[field]) ? profile[field].length > 0 : true)) {
      completedRequired++;
    }
  });

  // Check optional fields (30% weight)
  optionalFields.forEach(field => {
    if (profile[field] && profile[field] !== '' && 
        (Array.isArray(profile[field]) ? profile[field].length > 0 : true)) {
      completedOptional++;
    }
  });

  const requiredScore = (completedRequired / requiredFields.length) * 70;
  const optionalScore = (completedOptional / optionalFields.length) * 30;

  return Math.round(requiredScore + optionalScore);
};

/**
 * Get missing profile fields
 * @param {Object} profile - Candidate profile object
 * @returns {Array} Array of missing field objects
 */
export const getMissingProfileFields = (profile) => {
  if (!profile || typeof profile !== 'object') return [];

  const fieldDefinitions = {
    full_name: { label: 'Full Name', required: true },
    city: { label: 'City', required: true },
    role_applied_for: { label: 'Role Applied For', required: true },
    years_experience: { label: 'Years of Experience', required: true },
    current_job_title: { label: 'Current Job Title', required: true },
    skills: { label: 'Skills', required: true },
    expected_ctc: { label: 'Expected CTC', required: true },
    notice_period: { label: 'Notice Period', required: true },
    current_company: { label: 'Current Company', required: false },
    linkedin_url: { label: 'LinkedIn URL', required: false },
    profile_photo_url: { label: 'Profile Photo', required: false },
    resume_url: { label: 'Resume', required: false },
    languages: { label: 'Languages', required: false },
  };

  const missingFields = [];

  Object.entries(fieldDefinitions).forEach(([field, definition]) => {
    const value = profile[field];
    const isEmpty = !value || value === '' || 
                   (Array.isArray(value) && value.length === 0);
    
    if (isEmpty) {
      missingFields.push({
        field,
        label: definition.label,
        required: definition.required,
      });
    }
  });

  return missingFields;
};

/**
 * Check if profile is complete enough for job applications
 * @param {Object} profile - Candidate profile object
 * @returns {Object} Completion status and missing fields
 */
export const checkApplicationReadiness = (profile) => {
  const completion = calculateProfileCompletion(profile);
  const missingFields = getMissingProfileFields(profile);
  const missingRequired = missingFields.filter(field => field.required);

  return {
    isReady: completion >= 70 && missingRequired.length === 0,
    completion,
    missingFields: missingRequired,
    suggestions: missingFields.filter(field => !field.required),
  };
};

/**
 * Get profile completion status with recommendations
 * @param {Object} profile - Candidate profile object
 * @returns {Object} Detailed completion status
 */
export const getProfileCompletionStatus = (profile) => {
  const completion = calculateProfileCompletion(profile);
  const missingFields = getMissingProfileFields(profile);
  
  let status = 'incomplete';
  let message = 'Complete your profile to get better job matches';
  let color = 'orange';

  if (completion >= 90) {
    status = 'excellent';
    message = 'Your profile is excellent!';
    color = 'green';
  } else if (completion >= 70) {
    status = 'good';
    message = 'Your profile looks good';
    color = 'blue';
  } else if (completion >= 50) {
    status = 'fair';
    message = 'Add more details to improve your profile';
    color = 'orange';
  } else {
    status = 'poor';
    message = 'Your profile needs more information';
    color = 'red';
  }

  return {
    completion,
    status,
    message,
    color,
    missingFields,
    nextSteps: missingFields.slice(0, 3), // Top 3 recommendations
  };
};

/**
 * Format profile data for job application
 * @param {Object} profile - Candidate profile object
 * @returns {Object} Formatted application data
 */
export const formatProfileForApplication = (profile) => {
  if (!profile || typeof profile !== 'object') return {};

  return {
    candidate_name: profile.full_name || '',
    candidate_email: profile.email || '',
    candidate_phone: profile.phone_number || '',
    current_position: profile.current_job_title || '',
    current_company: profile.current_company || '',
    experience_years: profile.years_experience || 0,
    expected_salary: profile.expected_ctc || '',
    notice_period: profile.notice_period || '',
    location: profile.city || '',
    skills: Array.isArray(profile.skills) 
      ? profile.skills.map(skill => typeof skill === 'string' ? skill : skill.name).join(', ')
      : profile.skills || '',
    linkedin_url: profile.linkedin_url || '',
    resume_url: profile.resume_url || '',
  };
};
