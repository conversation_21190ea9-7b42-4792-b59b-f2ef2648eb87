import { useState, useCallback } from 'react';
import { Button, Form, Input, Typography, message } from 'antd';
import { LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { useColorModeStore } from '@/store/colorMode.store';
import PasswordResetService from '@/services/passwordResetService';

const { Title, Text } = Typography;

const NewPasswordForm = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const handleResetPassword = useCallback(
    async (values) => {
      setLoading(true);
      try {
        const result = await PasswordResetService.resetPassword(values.password);
        message.success('Password reset successfully!');
        onSuccess?.(result);
      } catch (error) {
        message.error(error.message || 'Failed to reset password. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    [onSuccess]
  );

  return (
    <div className="w-full max-w-lg mx-auto px-4 sm:px-6">
      {/* Header */}
      <div className="text-center mb-6 sm:mb-8">
        <div className="mb-4">
          <LockOutlined
            style={{
              color: 'var(--primary)',
              fontSize: window.innerWidth < 640 ? 40 : 48,
            }}
          />
        </div>
        <Title
          level={2}
          className={`mb-3 text-lg sm:text-xl md:text-2xl ${isDark ? 'text-white' : 'text-gray-900'}`}
        >
          Create New Password
        </Title>
        <Text className={`text-sm sm:text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
          Enter your new password below.
          <br />
          Make sure it's <strong>at least 8 characters</strong> long.
        </Text>
      </div>

      {/* Password Form */}
      <Form
        form={form}
        layout="vertical"
        onFinish={handleResetPassword}
        requiredMark={false}
        className="space-y-4"
      >
        <Form.Item
          name="password"
          rules={[
            { required: true, message: 'Please enter a new password' },
            { min: 8, message: 'Password must be at least 8 characters long' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />}
            placeholder="New password"
            size="large"
            className={`
              h-12 sm:h-14 rounded-xl
              ${isDark ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300'}
            `}
            iconRender={(visible) =>
              visible ? (
                <EyeTwoTone className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
              ) : (
                <EyeInvisibleOutlined className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
              )
            }
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          dependencies={['password']}
          rules={[
            { required: true, message: 'Please confirm your password' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('The two passwords do not match'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />}
            placeholder="Confirm new password"
            size="large"
            className={`
              h-12 sm:h-14 rounded-xl
              ${isDark ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300'}
            `}
            iconRender={(visible) =>
              visible ? (
                <EyeTwoTone className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
              ) : (
                <EyeInvisibleOutlined className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
              )
            }
          />
        </Form.Item>

        <Form.Item className="mb-0 pt-4">
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            block
            loading={loading}
            className="h-12 sm:h-14 rounded-xl font-semibold text-sm sm:text-base"
            icon={<LockOutlined />}
          >
            Reset Password
          </Button>
        </Form.Item>
      </Form>

      {/* Password Requirements */}
      <div className={`mt-6 p-4 rounded-lg ${isDark ? 'bg-gray-800' : 'bg-gray-50'}`}>
        <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
          <strong>Password Requirements:</strong>
        </Text>
        <ul className={`mt-2 text-xs ${isDark ? 'text-gray-400' : 'text-gray-600'} space-y-1`}>
          <li>• At least 8 characters long</li>
          <li>
            • Use a combination of one capital and small letter, numbers and special characters
          </li>
          <li>• Avoid using personal information</li>
        </ul>
      </div>
    </div>
  );
};

export default NewPasswordForm;
