import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Switch,
  Select,
  Divider,
  Space,
  Row,
  Col,
  Alert,
  message,
  Modal,
} from 'antd';
import {
  <PERSON>a<PERSON>ock,
  FaBell,
  FaGlobe,
  Fa<PERSON>ser,
  FaS<PERSON>eldAlt,
  FaCreditCard,
  FaTrash,
  FaExclamationTriangle,
  FaKey,
  FaEye,
  FaEyeSlash,
  FaMoon,
  FaSun,
} from 'react-icons/fa';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { confirm } = Modal;

/**
 * Reusable Settings component that can be used for different user types
 * @param {Object} props
 * @param {Function} props.updatePassword - Function to update password
 * @param {Function} props.updateNotifications - Function to update notification settings
 * @param {Function} props.updatePreferences - Function to update user preferences
 * @param {Function} props.deactivateAccount - Function to deactivate account
 * @param {Object} props.preferences - User preferences
 * @param {Object} props.notifications - User notification settings
 * @param {boolean} props.loading - Loading state
 * @param {Array} props.additionalTabs - Additional tabs to render
 * @param {boolean} props.isDark - Dark mode state
 * @param {Function} props.toggleTheme - Function to toggle theme
 */
const SettingsPage = ({
  updatePassword,
  updateNotifications,
  updatePreferences,
  deactivateAccount,
  preferences = {},
  notifications = {},
  loading = false,
  additionalTabs = [],
  isDark = false,
  toggleTheme,
}) => {
  const { isMobile } = useDeviceDetect();
  const [passwordForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [preferencesForm] = Form.useForm();
  const [showPassword, setShowPassword] = useState(false);

  const handlePasswordChange = async (values) => {
    try {
      if (values.newPassword !== values.confirmPassword) {
        message.error('Passwords do not match');
        return;
      }

      if (updatePassword) {
        await updatePassword(values);
        message.success('Password updated successfully');
        passwordForm.resetFields();
      }
    } catch (error) {
      message.error('Failed to update password');
    }
  };

  const handleNotificationSave = async (values) => {
    try {
      if (updateNotifications) {
        await updateNotifications(values);
        message.success('Notification preferences updated');
      }
    } catch (error) {
      message.error('Failed to update notification preferences');
    }
  };

  const handlePreferencesSave = async (values) => {
    try {
      if (updatePreferences) {
        await updatePreferences(values);
        message.success('Preferences updated successfully');
      }
    } catch (error) {
      message.error('Failed to update preferences');
    }
  };

  const showDeactivationConfirm = () => {
    confirm({
      title: 'Are you sure you want to deactivate your account?',
      icon: <FaExclamationTriangle style={{ color: '#ff4d4f' }} />,
      content:
        'This action will deactivate your account. You can reactivate it by logging in within 30 days, after which it will be permanently deleted.',
      okText: 'Yes, deactivate',
      okType: 'danger',
      cancelText: 'No, cancel',
      onOk() {
        if (deactivateAccount) {
          deactivateAccount()
            .then(() => message.warning('Account deactivation request submitted'))
            .catch(() => message.error('Failed to process deactivation request'));
        }
      },
    });
  };

  return (
    <div className="settings-page">
      <Title
        level={2}
        className="mb-6"
      >
        Settings
      </Title>

      <Tabs defaultActiveKey="account">
        <TabPane
          tab={
            <span className="flex items-center">
              <FaUser className="mr-2" />
              Account
            </span>
          }
          key="account"
        >
          <Card
            bordered={false}
            className="mb-6"
          >
            <Title level={4}>Account Information</Title>
            <Paragraph className="text-muted mb-4">
              Manage your account details and preferences
            </Paragraph>

            <Form
              layout="vertical"
              form={preferencesForm}
              initialValues={preferences}
              onFinish={handlePreferencesSave}
            >
              <Row gutter={[16, 16]}>
                <Col
                  xs={24}
                  md={12}
                >
                  <Form.Item
                    label="Email Address"
                    name="email"
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col
                  xs={24}
                  md={12}
                >
                  <Form.Item
                    label="Username"
                    name="username"
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  Save Changes
                </Button>
              </Form.Item>
            </Form>
          </Card>

          <Card bordered={false}>
            <Title level={4}>Timezone & Language</Title>
            <Paragraph className="text-muted mb-4">
              Set your preferred timezone and language
            </Paragraph>

            <Form
              layout="vertical"
              form={preferencesForm}
              initialValues={preferences}
              onFinish={handlePreferencesSave}
            >
              <Row gutter={[16, 16]}>
                <Col
                  xs={24}
                  md={12}
                >
                  <Form.Item
                    label="Timezone"
                    name="timezone"
                  >
                    <Select>
                      <Option value="UTC-8">Pacific Time (UTC-8)</Option>
                      <Option value="UTC-7">Mountain Time (UTC-7)</Option>
                      <Option value="UTC-6">Central Time (UTC-6)</Option>
                      <Option value="UTC-5">Eastern Time (UTC-5)</Option>
                      <Option value="UTC+0">UTC</Option>
                      <Option value="UTC+1">Central European Time (UTC+1)</Option>
                      <Option value="UTC+8">China Standard Time (UTC+8)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col
                  xs={24}
                  md={12}
                >
                  <Form.Item
                    label="Language"
                    name="language"
                  >
                    <Select>
                      <Option value="en">English</Option>
                      <Option value="es">Spanish</Option>
                      <Option value="fr">French</Option>
                      <Option value="de">German</Option>
                      <Option value="zh">Chinese</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  Save Preferences
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span className="flex items-center">
              <FaLock className="mr-2" />
              Security
            </span>
          }
          key="security"
        >
          <Card
            bordered={false}
            className="mb-6"
          >
            <Title level={4}>Change Password</Title>
            <Paragraph className="text-muted mb-4">
              Update your password regularly to keep your account secure
            </Paragraph>

            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordChange}
            >
              <Form.Item
                name="currentPassword"
                label="Current Password"
                rules={[{ required: true, message: 'Please enter your current password' }]}
              >
                <Input.Password
                  placeholder="Enter your current password"
                  iconRender={(visible) => (visible ? <FaEye /> : <FaEyeSlash />)}
                />
              </Form.Item>

              <Form.Item
                name="newPassword"
                label="New Password"
                rules={[
                  { required: true, message: 'Please enter a new password' },
                  { min: 8, message: 'Password must be at least 8 characters' },
                ]}
              >
                <Input.Password
                  placeholder="Enter a new password"
                  iconRender={(visible) => (visible ? <FaEye /> : <FaEyeSlash />)}
                />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label="Confirm New Password"
                rules={[
                  { required: true, message: 'Please confirm your new password' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('The two passwords do not match'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  placeholder="Confirm your new password"
                  iconRender={(visible) => (visible ? <FaEye /> : <FaEyeSlash />)}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  Update Password
                </Button>
              </Form.Item>
            </Form>
          </Card>

          <Card bordered={false}>
            <Title level={4}>Two-Factor Authentication</Title>
            <Paragraph className="text-muted mb-4">
              Add an extra layer of security to your account
            </Paragraph>

            <div className="flex items-center justify-between mb-4">
              <div>
                <Text strong>Enable Two-Factor Authentication</Text>
                <Paragraph className="text-muted mb-0">
                  Require a verification code when logging in
                </Paragraph>
              </div>
              <Switch
                checked={preferences.twoFactorEnabled}
                onChange={(checked) => {
                  if (updatePreferences) {
                    updatePreferences({ ...preferences, twoFactorEnabled: checked })
                      .then(() =>
                        message.success(
                          `Two-factor authentication ${checked ? 'enabled' : 'disabled'}`
                        )
                      )
                      .catch(() => message.error('Failed to update two-factor authentication'));
                  }
                }}
              />
            </div>

            {preferences.twoFactorEnabled && (
              <Button icon={<FaKey className="mr-2" />}>Configure Two-Factor Authentication</Button>
            )}
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span className="flex items-center">
              <FaBell className="mr-2" />
              Notifications
            </span>
          }
          key="notifications"
        >
          <Card bordered={false}>
            <Title level={4}>Notification Preferences</Title>
            <Paragraph className="text-muted mb-4">
              Manage how and when you receive notifications
            </Paragraph>

            <Form
              form={notificationForm}
              layout="vertical"
              initialValues={notifications}
              onFinish={handleNotificationSave}
            >
              <div className="mb-4">
                <Title level={5}>Email Notifications</Title>
                <Divider className="mt-2 mb-4" />

                <Form.Item
                  name="emailInterviews"
                  valuePropName="checked"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <Text strong>Interview Reminders</Text>
                      <Paragraph className="text-muted mb-0">
                        Receive email reminders about upcoming interviews
                      </Paragraph>
                    </div>
                    <Switch />
                  </div>
                </Form.Item>

                <Form.Item
                  name="emailMessages"
                  valuePropName="checked"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <Text strong>New Messages</Text>
                      <Paragraph className="text-muted mb-0">
                        Receive emails when you get new messages
                      </Paragraph>
                    </div>
                    <Switch />
                  </div>
                </Form.Item>

                <Form.Item
                  name="emailUpdates"
                  valuePropName="checked"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <Text strong>Platform Updates</Text>
                      <Paragraph className="text-muted mb-0">
                        Receive emails about new features and updates
                      </Paragraph>
                    </div>
                    <Switch />
                  </div>
                </Form.Item>
              </div>

              <div className="mb-4">
                <Title level={5}>In-App Notifications</Title>
                <Divider className="mt-2 mb-4" />

                <Form.Item
                  name="appInterviews"
                  valuePropName="checked"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <Text strong>Interview Notifications</Text>
                      <Paragraph className="text-muted mb-0">
                        Receive notifications about interview status changes
                      </Paragraph>
                    </div>
                    <Switch />
                  </div>
                </Form.Item>

                <Form.Item
                  name="appMessages"
                  valuePropName="checked"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <Text strong>Message Notifications</Text>
                      <Paragraph className="text-muted mb-0">
                        Receive notifications for new messages
                      </Paragraph>
                    </div>
                    <Switch />
                  </div>
                </Form.Item>
              </div>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  Save Notification Preferences
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* Render additional tabs if provided */}
        {additionalTabs.map((tab) => (
          <TabPane
            tab={
              <span className="flex items-center">
                {tab.icon && <span className="mr-2">{tab.icon}</span>}
                {tab.title}
              </span>
            }
            key={tab.key}
          >
            {tab.content}
          </TabPane>
        ))}

        <TabPane
          tab={
            <span className="flex items-center">
              <FaTrash className="mr-2" />
              Deactivation
            </span>
          }
          key="deactivation"
        >
          <Card bordered={false}>
            <Title level={4}>Account Deactivation</Title>
            <Paragraph className="text-muted mb-4">
              Temporarily deactivate or permanently delete your account
            </Paragraph>

            <Alert
              message="Warning: Account Deactivation"
              description="Deactivating your account will remove your profile from the platform. You can reactivate your account by logging in within 30 days. After 30 days, your account may be permanently deleted."
              type="warning"
              showIcon
              icon={<FaExclamationTriangle />}
              className="mb-6"
            />

            <Button
              danger
              icon={<FaTrash className="mr-2" />}
              onClick={showDeactivationConfirm}
            >
              Deactivate Account
            </Button>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
