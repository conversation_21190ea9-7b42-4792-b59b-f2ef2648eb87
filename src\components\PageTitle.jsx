import { Helmet } from 'react-helmet';

const PageTitle = ({ title, description = '', icon = '', keywords = '' }) => (
  <Helmet>
    <title>{title} | InterviewPro</title>
    {description && (
      <meta
        name="description"
        content={description}
      />
    )}
    {icon && (
      <meta
        name="icon"
        content={icon}
      />
    )}
    {keywords && (
      <meta
        name="keywords"
        content={keywords}
      />
    )}
  </Helmet>
);

export default PageTitle;
