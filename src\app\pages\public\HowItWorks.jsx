import React from 'react';
import { Typography, Steps, Card, Row, Col, But<PERSON>, Divider } from 'antd';
import {
  UserOutlined,
  FormOutlined,
  CalendarOutlined,
  VideoCameraOutlined,
  CheckCircleOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph } = Typography;

const HowItWorks = () => {
  const { isMobile, isTablet } = useDeviceDetect();

  return (
    <div className="how-it-works-page max-w-5xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-12 mb-8">
        <div className="inline-block p-3 bg-secondary rounded-full mb-4">
          <RocketOutlined className="text-primary text-2xl" />
        </div>
        <Title
          level={1}
          className="mb-4"
        >
          How It Works
        </Title>
        <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
          Our platform simplifies the interview process for both candidates and companies. Here's
          how you can get started in just a few simple steps.
        </Paragraph>
      </div>

      {/* Process Flow Cards */}
      <Row
        gutter={[24, 24]}
        className="mb-16"
      >
        <Col xs={24}>
          <Card
            className="process-card shadow-md hover:shadow-lg transition-shadow"
            bordered={false}
          >
            <div className="text-center mb-8">
              <Title
                level={2}
                className="relative inline-block"
              >
                For Candidates
                <div className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-70"></div>
              </Title>
            </div>

            <Steps
              direction={isMobile ? 'vertical' : 'horizontal'}
              size={isMobile || isTablet ? 'small' : 'default'}
              progressDot
              current={-1}
              className="candidate-steps"
              items={[
                {
                  title: 'Create Profile',
                  description: 'Sign up and build your professional profile',
                  icon: <UserOutlined className="step-icon" />,
                },
                {
                  title: 'Browse Jobs',
                  description: 'Explore opportunities that match your skills',
                  icon: <FormOutlined className="step-icon" />,
                },
                {
                  title: 'Schedule',
                  description: 'Book interviews at convenient times',
                  icon: <CalendarOutlined className="step-icon" />,
                },
                {
                  title: 'Interview',
                  description: 'Join video interviews through our platform',
                  icon: <VideoCameraOutlined className="step-icon" />,
                },
                {
                  title: 'Get Hired',
                  description: 'Receive feedback and job offers',
                  icon: <CheckCircleOutlined className="step-icon" />,
                },
              ]}
            />

            <div className="text-center mt-8">
              <Button
                type="primary"
                size="large"
                icon={<UserOutlined />}
              >
                Sign Up as Candidate
              </Button>
            </div>
          </Card>
        </Col>

        <Col xs={24}>
          <Card
            className="process-card shadow-md hover:shadow-lg transition-shadow"
            bordered={false}
          >
            <div className="text-center mb-8">
              <Title
                level={2}
                className="relative inline-block"
              >
                For Companies
                <div className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-70"></div>
              </Title>
            </div>

            <Steps
              direction={isMobile ? 'vertical' : 'horizontal'}
              size={isMobile || isTablet ? 'small' : 'default'}
              progressDot
              current={-1}
              className="company-steps"
              items={[
                {
                  title: 'Create Profile',
                  description: 'Set up your company profile and culture',
                  icon: <UserOutlined className="step-icon" />,
                },
                {
                  title: 'Post Jobs',
                  description: 'Create detailed job listings',
                  icon: <FormOutlined className="step-icon" />,
                },
                {
                  title: 'Review',
                  description: 'Browse candidate applications',
                  icon: <CheckCircleOutlined className="step-icon" />,
                },
                {
                  title: 'Interview',
                  description: 'Conduct interviews through our platform',
                  icon: <VideoCameraOutlined className="step-icon" />,
                },
                {
                  title: 'Hire',
                  description: 'Collaborate with team and extend offers',
                  icon: <CheckCircleOutlined className="step-icon" />,
                },
              ]}
            />

            <div className="text-center mt-8">
              <Button
                type="primary"
                size="large"
                icon={<UserOutlined />}
              >
                Sign Up as Company
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Benefits Section */}
      <div className="text-center mb-8">
        <Title
          level={2}
          className="relative inline-block"
        >
          Why Choose Our Platform
          <div className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-70"></div>
        </Title>
      </div>

      <Row
        gutter={[24, 24]}
        className="mb-16"
      >
        <Col
          xs={24}
          md={8}
        >
          <Card
            className="benefit-card text-center h-full"
            bordered={false}
          >
            <div className="inline-block p-3 bg-secondary rounded-full mb-4">
              <RocketOutlined className="text-primary text-2xl" />
            </div>
            <Title level={4}>Streamlined Process</Title>
            <Paragraph className="text-muted">
              Our all-in-one platform eliminates the need for multiple tools and simplifies the
              entire interview process.
            </Paragraph>
          </Card>
        </Col>

        <Col
          xs={24}
          md={8}
        >
          <Card
            className="benefit-card text-center h-full"
            bordered={false}
          >
            <div className="inline-block p-3 bg-secondary rounded-full mb-4">
              <VideoCameraOutlined className="text-primary text-2xl" />
            </div>
            <Title level={4}>Integrated Video</Title>
            <Paragraph className="text-muted">
              Conduct interviews directly through our platform with no additional software or
              downloads required.
            </Paragraph>
          </Card>
        </Col>

        <Col
          xs={24}
          md={8}
        >
          <Card
            className="benefit-card text-center h-full"
            bordered={false}
          >
            <div className="inline-block p-3 bg-secondary rounded-full mb-4">
              <CheckCircleOutlined className="text-primary text-2xl" />
            </div>
            <Title level={4}>Better Matches</Title>
            <Paragraph className="text-muted">
              Our matching algorithm helps connect the right candidates with the right opportunities
              for better outcomes.
            </Paragraph>
          </Card>
        </Col>
      </Row>

      {/* CTA Section */}
      <Card
        className="cta-card text-center mb-8"
        bordered={false}
        style={{ background: 'var(--primary)', color: 'white' }}
      >
        <Title
          level={3}
          style={{ color: 'white', marginBottom: '16px' }}
        >
          Ready to transform your interview experience?
        </Title>
        <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)', marginBottom: '24px' }}>
          Join thousands of candidates and companies already using our platform.
        </Paragraph>
        <Button
          size="large"
          style={{
            background: 'white',
            color: 'var(--primary)',
            borderColor: 'white',
            fontWeight: 500,
          }}
        >
          Get Started Now
        </Button>
      </Card>
    </div>
  );
};

export default HowItWorks;
