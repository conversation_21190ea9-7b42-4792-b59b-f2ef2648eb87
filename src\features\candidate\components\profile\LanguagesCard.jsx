/**
 * Languages Card Component
 * Shows language skills with proficiency levels
 */
import React from 'react';
import { Card, Tag, Alert, Button } from 'antd';
import { Globe } from 'lucide-react';

const LanguagesCard = ({ profile, onEditProfile }) => {
  return (
    <Card
      title={
        <span className="flex items-center">
          <Globe size={18} className="mr-2 text-green-500" />
          Languages
        </span>
      }
      className="shadow-sm rounded-lg"
    >
      {profile?.languages && Array.isArray(profile.languages) && profile.languages.length > 0 ? (
        <div className="space-y-3">
          {profile.languages.map((lang) => (
            <div key={lang.name} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium text-gray-700">{lang.name}</span>
              <Tag 
                color={
                  lang.proficiency === 'Native' ? 'green' :
                  lang.proficiency === 'Fluent' ? 'blue' :
                  lang.proficiency === 'Intermediate' ? 'orange' : 'default'
                }
                className="px-2 py-1 rounded-md"
              >
                {lang.proficiency}
              </Tag>
            </div>
          ))}
        </div>
      ) : (
        <Alert
          message="Add languages"
          description="Showcase your language skills to global employers."
          type="info"
          showIcon
          action={
            <Button size="small" type="primary" onClick={onEditProfile}>
              Add Languages
            </Button>
          }
        />
      )}
    </Card>
  );
};

export default LanguagesCard;
