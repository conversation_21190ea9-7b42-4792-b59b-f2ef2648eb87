import dayjs from 'dayjs';

/**
 * String utilities
 */
export const capitalize = (str) => (str ? str.charAt(0).toUpperCase() + str.slice(1) : '');

export const getInitials = (name) => {
  if (!name) return '';
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();
};

export const truncateText = (text, maxLength = 100) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Date and time utilities
 */
export const formatDate = (date) => (date ? dayjs(date).format('DD MMM YYYY') : '');

export const formatTime = (time) => (time ? dayjs(time).format('hh:mm A') : '');

export const formatDateTime = (dateTime) =>
  dateTime ? dayjs(dateTime).format('DD MMM YYYY, hh:mm A') : '';

export const getRelativeTime = (date) => {
  if (!date) return '';
  const now = dayjs();
  const target = dayjs(date);
  const diffInDays = now.diff(target, 'day');

  if (diffInDays === 0) return 'Today';
  if (diffInDays === 1) return 'Yesterday';
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return `${Math.floor(diffInDays / 365)} years ago`;
};

/**
 * Number and currency utilities
 */
export const formatCurrency = (amount, currency = 'INR') => {
  if (!amount) return '';
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatNumber = (num) => {
  if (!num) return '0';
  return new Intl.NumberFormat('en-IN').format(num);
};

/**
 * Array utilities
 */
export const removeDuplicates = (array, key) => {
  if (!key) return [...new Set(array)];
  return array.filter((item, index, self) => index === self.findIndex((t) => t[key] === item[key]));
};

export const sortByKey = (array, key, order = 'asc') => {
  return [...array].sort((a, b) => {
    if (order === 'desc') return b[key] - a[key];
    return a[key] - b[key];
  });
};

/**
 * Validation utilities
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone) => {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
};

/**
 * URL and file utilities
 */
export const generateSlug = (text) => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .trim();
};

export const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase();
};

export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
