import { Spin, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

const ClerkLoader = ({ message = 'Initializing authentication...' }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <Spin
          indicator={
            <LoadingOutlined
              style={{ 
                fontSize: 48,
                color: 'var(--primary-color, #1890ff)'
              }}
              spin
            />
          }
          size="large"
        />
        <div className="mt-6">
          <Text className="text-lg text-text-secondary">
            {message}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default ClerkLoader;
