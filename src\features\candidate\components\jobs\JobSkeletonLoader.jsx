/**
 * Job Skeleton Loader Component
 * Displays skeleton loading animation while jobs are being fetched
 */

import React from 'react';
import { Card, Skeleton, Space } from 'antd';

const JobSkeletonLoader = ({ count = 6, viewMode = 'grid' }) => {
  const skeletons = Array.from({ length: count }, (_, index) => (
    <JobCardSkeleton key={index} viewMode={viewMode} />
  ));

  if (viewMode === 'list') {
    return <div className="flex flex-col gap-4">{skeletons}</div>;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {skeletons}
    </div>
  );
};

const JobCardSkeleton = ({ viewMode }) => {
  return (
    <Card className="mb-4 shadow-sm">
      <div className="flex flex-col">
        {/* Header with title and company logo */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <Skeleton.Input
              style={{ width: '70%', height: 24 }}
              active
              className="mb-2"
            />
            <Skeleton.Input
              style={{ width: '50%', height: 20 }}
              active
            />
          </div>
          <Skeleton.Avatar
            size={48}
            shape="square"
            active
          />
        </div>

        {/* Tags */}
        <Space className="mb-4" wrap>
          <Skeleton.Button
            style={{ width: 80, height: 24 }}
            active
            size="small"
          />
          <Skeleton.Button
            style={{ width: 100, height: 24 }}
            active
            size="small"
          />
          <Skeleton.Button
            style={{ width: 90, height: 24 }}
            active
            size="small"
          />
          <Skeleton.Button
            style={{ width: 70, height: 24 }}
            active
            size="small"
          />
        </Space>

        {/* Description */}
        <div className="mb-4">
          <Skeleton
            paragraph={{
              rows: viewMode === 'list' ? 3 : 2,
              width: ['100%', '100%', '80%'],
            }}
            title={false}
            active
          />
        </div>

        {/* Skills */}
        <Space className="mb-4" wrap>
          <Skeleton.Button
            style={{ width: 60, height: 20 }}
            active
            size="small"
          />
          <Skeleton.Button
            style={{ width: 80, height: 20 }}
            active
            size="small"
          />
          <Skeleton.Button
            style={{ width: 70, height: 20 }}
            active
            size="small"
          />
        </Space>

        {/* Footer with date and apply button */}
        <div className="flex justify-between items-center">
          <Skeleton.Input
            style={{ width: 100, height: 16 }}
            active
            size="small"
          />
          <Skeleton.Button
            style={{ width: 100, height: 32 }}
            active
          />
        </div>
      </div>
    </Card>
  );
};

export default JobSkeletonLoader;
