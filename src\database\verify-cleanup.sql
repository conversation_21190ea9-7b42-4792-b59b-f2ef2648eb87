-- Verification script to check Supabase cleanup
-- Run this script after the cleanup to verify everything was removed properly

-- 1. Check for any remaining candidate-related tables
SELECT 
  schemaname,
  tablename,
  'Table still exists' as status
FROM pg_tables 
WHERE tablename LIKE '%candidate%' 
   OR tablename LIKE '%application%'
   OR tablename LIKE '%saved_job%'
ORDER BY tablename;

-- 2. Check for candidate-related views
SELECT 
  schemaname,
  viewname,
  'View still exists' as status
FROM pg_views 
WHERE viewname LIKE '%candidate%'
ORDER BY viewname;

-- 3. Check for candidate-related functions
SELECT 
  n.nspname as schema_name,
  p.proname as function_name,
  'Function still exists' as status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname LIKE '%candidate%'
   OR p.proname LIKE '%application%'
ORDER BY function_name;

-- 4. Check for candidate role in profiles
SELECT 
  'Profiles with candidate role' as check_type,
  COUNT(*) as count
FROM profiles 
WHERE role = 'candidate';

-- 5. Check remaining user roles
SELECT 
  role,
  COUNT(*) as user_count
FROM profiles 
GROUP BY role
ORDER BY role;

-- 6. Check for candidate references in other tables
-- Check interviews table
SELECT 
  'Interviews with candidate_id' as check_type,
  COUNT(*) as count
FROM interviews 
WHERE candidate_id IS NOT NULL;

-- Check applications table (if it still exists)
SELECT 
  'Applications with candidate_id' as check_type,
  COUNT(*) as count
FROM applications 
WHERE candidate_id IS NOT NULL;

-- 7. Check for orphaned records
-- Profiles without corresponding role-specific profiles
SELECT 
  'Company profiles without company_profiles record' as check_type,
  COUNT(*) as count
FROM profiles p
WHERE p.role = 'company' 
  AND NOT EXISTS (
    SELECT 1 FROM company_profiles cp WHERE cp.id = p.id
  );

SELECT 
  'Interviewer profiles without interviewer_profiles record' as check_type,
  COUNT(*) as count
FROM profiles p
WHERE p.role = 'interviewer' 
  AND NOT EXISTS (
    SELECT 1 FROM interviewer_profiles ip WHERE ip.id = p.id
  );

-- 8. Check table sizes and record counts
SELECT 
  'profiles' as table_name,
  COUNT(*) as record_count
FROM profiles
UNION ALL
SELECT 
  'company_profiles',
  COUNT(*)
FROM company_profiles
UNION ALL
SELECT 
  'interviewer_profiles',
  COUNT(*)
FROM interviewer_profiles
UNION ALL
SELECT 
  'jobs',
  COUNT(*)
FROM jobs
UNION ALL
SELECT 
  'interviews',
  COUNT(*)
FROM interviews
ORDER BY table_name;

-- 9. Check for any remaining candidate-related indexes
SELECT 
  schemaname,
  tablename,
  indexname,
  'Index still exists' as status
FROM pg_indexes 
WHERE indexname LIKE '%candidate%'
   OR indexname LIKE '%application%'
ORDER BY indexname;

-- 10. Check for any remaining candidate-related policies
SELECT 
  schemaname,
  tablename,
  policyname,
  'Policy still exists' as status
FROM pg_policies 
WHERE policyname LIKE '%candidate%'
   OR policyname LIKE '%application%'
ORDER BY policyname;

-- 11. Check storage buckets (if using Supabase Storage)
-- This would need to be checked through the Supabase dashboard or storage API
-- SELECT name, id FROM storage.buckets WHERE name LIKE '%candidate%';

-- 12. Summary report
SELECT 
  'CLEANUP VERIFICATION SUMMARY' as report_section,
  '' as details
UNION ALL
SELECT 
  'Total remaining profiles:',
  COUNT(*)::text
FROM profiles
UNION ALL
SELECT 
  'Company profiles:',
  COUNT(*)::text
FROM company_profiles
UNION ALL
SELECT 
  'Interviewer profiles:',
  COUNT(*)::text
FROM interviewer_profiles
UNION ALL
SELECT 
  'Active jobs:',
  COUNT(*)::text
FROM jobs 
WHERE status = 'active'
UNION ALL
SELECT 
  'Total interviews:',
  COUNT(*)::text
FROM interviews;

-- 13. Check for any remaining enum values
SELECT 
  t.typname as enum_name,
  e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE e.enumlabel LIKE '%candidate%'
ORDER BY t.typname, e.enumlabel;

-- 14. Final recommendations query
SELECT 
  'NEXT STEPS' as section,
  'If any candidate-related objects still exist, review and remove them manually' as recommendation
UNION ALL
SELECT 
  'PERFORMANCE',
  'Run VACUUM ANALYZE on all tables to reclaim space and update statistics'
UNION ALL
SELECT 
  'MONITORING',
  'Set up monitoring for remaining tables and functions'
UNION ALL
SELECT 
  'TESTING',
  'Test all application functionality to ensure nothing is broken';
