import React, { useState, useEffect } from 'react';
import { Modal, Input, List, Avatar, Empty } from 'antd';
import {
  SearchOutlined,
  UserOutlined,
  FileTextOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useColorModeStore } from '@/store/colorMode.store';

const { Search } = Input;

const SearchModal = ({ visible, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Mock search results
  const mockResults = [
    {
      id: 1,
      type: 'candidate',
      title: '<PERSON>',
      description: 'Frontend Developer',
      path: '/admin/candidates/1',
      icon: <UserOutlined style={{ fontSize: '18px' }} />,
    },
    {
      id: 2,
      type: 'candidate',
      title: '<PERSON>',
      description: 'UX Designer',
      path: '/admin/candidates/2',
      icon: <UserOutlined style={{ fontSize: '18px' }} />,
    },
    {
      id: 3,
      type: 'report',
      title: 'Q2 Interview Report',
      description: 'Quarterly interview statistics',
      path: '/admin/reports/3',
      icon: <FileTextOutlined style={{ fontSize: '18px' }} />,
    },
    {
      id: 4,
      type: 'interview',
      title: 'Technical Interview',
      description: 'Scheduled for tomorrow at 10:00 AM',
      path: '/admin/interviews/4',
      icon: <CalendarOutlined style={{ fontSize: '18px' }} />,
    },
  ];

  useEffect(() => {
    // Reset search when modal opens
    if (visible) {
      setSearchTerm('');
      setSearchResults([]);
    }
  }, [visible]);

  const handleSearch = (value) => {
    setSearchTerm(value);
    setLoading(true);

    // Simulate API search
    setTimeout(() => {
      if (!value.trim()) {
        setSearchResults([]);
      } else {
        // Filter mock results based on search term
        const filtered = mockResults.filter(
          (item) =>
            item.title.toLowerCase().includes(value.toLowerCase()) ||
            item.description.toLowerCase().includes(value.toLowerCase())
        );
        setSearchResults(filtered);
      }
      setLoading(false);
    }, 300);
  };

  const handleResultClick = (path) => {
    navigate(path);
    onClose();
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+K or Cmd+K to open search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        onClose(true); // Pass true to indicate it should open
      }

      // Escape to close
      if (e.key === 'Escape' && visible) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [visible, onClose]);

  return (
    <Modal
      title={
        <div className="flex items-center">
          <SearchOutlined
            style={{ fontSize: '18px' }}
            className="mr-2"
          />
          <span style={{ fontSize: '18px' }}>Search</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
    >
      <Search
        placeholder="Search for candidates, reports, interviews..."
        value={searchTerm}
        onChange={(e) => handleSearch(e.target.value)}
        loading={loading}
        autoFocus
        className="mb-4"
        size="large"
        style={{ fontSize: '16px' }}
      />

      {searchResults.length > 0 ? (
        <List
          itemLayout="horizontal"
          dataSource={searchResults}
          renderItem={(item) => (
            <List.Item
              className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md p-3 transition-colors"
              onClick={() => handleResultClick(item.path)}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    size={40}
                    icon={item.icon}
                  />
                }
                title={<span style={{ fontSize: '16px' }}>{item.title}</span>}
                description={<span style={{ fontSize: '14px' }}>{item.description}</span>}
              />
            </List.Item>
          )}
        />
      ) : searchTerm ? (
        <Empty description={<span style={{ fontSize: '16px' }}>No results found</span>} />
      ) : null}

      <div
        className="text-sm text-gray-500 mt-4 text-center"
        style={{ fontSize: '14px' }}
      >
        Press <kbd className="px-2 py-1 bg-secondary rounded">ESC</kbd> to close or{' '}
        <kbd className="px-2 py-1 bg-secondary rounded">Ctrl+K</kbd> to open search
      </div>
    </Modal>
  );
};

export default SearchModal;
