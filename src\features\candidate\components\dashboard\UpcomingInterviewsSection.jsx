import React from 'react';
import { Card, List, Tag, Typography, Button, Empty, Skeleton } from 'antd';
import { Calendar, Clock, MapPin, Video } from 'lucide-react';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

/**
 * UpcomingInterviewsSection component
 */
const UpcomingInterviewsSection = ({
  interviews,
  loading,
  onViewInterview,
  onViewAllInterviews,
}) => {
  const getInterviewTypeIcon = (type) => {
    const icons = {
      video: (
        <Video
          size={16}
          className="text-blue-500"
        />
      ),
      phone: (
        <Clock
          size={16}
          className="text-green-500"
        />
      ),
      in_person: (
        <MapPin
          size={16}
          className="text-orange-500"
        />
      ),
    };
    return (
      icons[type] || (
        <Calendar
          size={16}
          className="text-gray-500"
        />
      )
    );
  };

  const getInterviewTypeColor = (type) => {
    const colors = {
      video: 'blue',
      phone: 'green',
      in_person: 'orange',
    };
    return colors[type] || 'default';
  };

  const formatInterviewDate = (dateString) => {
    const date = dayjs(dateString);
    const now = dayjs();
    const tomorrow = now.add(1, 'day');

    // Check if same day
    if (date.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) {
      return `Today at ${date.format('h:mm A')}`;
    } else if (date.format('YYYY-MM-DD') === tomorrow.format('YYYY-MM-DD')) {
      return `Tomorrow at ${date.format('h:mm A')}`;
    } else {
      return date.format('MMM D, YYYY [at] h:mm A');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      scheduled: 'blue',
      confirmed: 'green',
      completed: 'default',
      cancelled: 'red',
    };
    return colors[status] || 'default';
  };

  if (loading) {
    return (
      <Card
        title="Upcoming Interviews"
        extra={<Button type="link">View All</Button>}
      >
        <Skeleton
          active
          paragraph={{ rows: 3 }}
        />
      </Card>
    );
  }

  // Filter for upcoming interviews only
  const upcomingInterviews = interviews.filter(
    (interview) => new Date(interview.scheduled_at) > new Date()
  );

  return (
    <Card
      title="Upcoming Interviews"
      extra={
        <Button
          type="link"
          onClick={onViewAllInterviews}
        >
          View All
        </Button>
      }
    >
      {upcomingInterviews.length === 0 ? (
        <Empty
          description="No upcoming interviews"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <List
          dataSource={upcomingInterviews.slice(0, 3)}
          renderItem={(interview) => (
            <List.Item
              key={interview.id}
              actions={[
                <Button
                  type="text"
                  onClick={() => onViewInterview?.(interview)}
                >
                  View Details
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={getInterviewTypeIcon(interview.type)}
                title={
                  <div className="flex items-center justify-between">
                    <Text
                      strong
                      className="line-clamp-1"
                    >
                      {interview.applications?.jobs?.title || 'Interview'}
                    </Text>
                    <div className="flex gap-1">
                      <Tag
                        color={getInterviewTypeColor(interview.type)}
                        size="small"
                      >
                        {interview.type?.replace('_', ' ')}
                      </Tag>
                      <Tag
                        color={getStatusColor(interview.status)}
                        size="small"
                      >
                        {interview.status}
                      </Tag>
                    </div>
                  </div>
                }
                description={
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-blue-600">
                      {formatInterviewDate(interview.scheduled_at)}
                    </div>
                    {interview.location && (
                      <div className="text-sm text-gray-600">📍 {interview.location}</div>
                    )}
                    {interview.applications?.jobs?.location && (
                      <div className="text-xs text-gray-500">
                        Company: {interview.applications.jobs.location}
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default UpcomingInterviewsSection;
