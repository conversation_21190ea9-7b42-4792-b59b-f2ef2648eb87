# Clerk-Supabase Integration Setup Guide

This guide will help you complete the setup of Clerk authentication with Supa<PERSON> database integration for your B2B SaaS interview platform.

## 🔧 Prerequisites

- Clerk account and application
- Supabase project (already configured)
- Environment variables set up

## 📋 Setup Steps

### 1. Clerk Dashboard Configuration

1. **Go to your Clerk Dashboard**
   - Navigate to https://dashboard.clerk.com
   - Select your application

2. **Configure Authentication Settings**
   - Go to "User & Authentication" → "Email, Phone, Username"
   - Enable email authentication
   - Configure social providers (Google, LinkedIn) if needed

3. **Set up Webhooks**
   - Go to "Webhooks" in the sidebar
   - Click "Add Endpoint"
   - URL: `https://your-supabase-project.supabase.co/functions/v1/clerk-webhook`
   - Events to subscribe to:
     - `user.created`
     - `user.updated` 
     - `user.deleted`
   - Copy the webhook secret for later use

4. **Configure User Metadata**
   - Go to "User & Authentication" → "Metadata"
   - Add custom fields for role management

### 2. Environment Variables

Update your `.env` file:

```env
# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_actual_key_here

# Supabase Configuration (already set)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# For webhook function
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### 3. Deploy Supabase Edge Function

1. **Install Supabase CLI** (if not already installed):
   ```bash
   npm install -g supabase
   ```

2. **Login to Supabase**:
   ```bash
   supabase login
   ```

3. **Deploy the webhook function**:
   ```bash
   supabase functions deploy clerk-webhook --project-ref your-project-ref
   ```

4. **Set environment variables for the function**:
   ```bash
   supabase secrets set CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret --project-ref your-project-ref
   ```

### 4. Database Setup Verification

The following has already been completed:
- ✅ Candidate data removed from database
- ✅ RLS policies updated
- ✅ Webhook handler function created
- ✅ User sync functions implemented

### 5. Test the Integration

1. **Test User Registration**:
   - Go to `/register`
   - Create a new account
   - Verify user appears in Supabase `profiles` table
   - Check role-specific profile creation

2. **Test User Login**:
   - Go to `/login`
   - Login with existing account
   - Verify role-based routing works
   - Check profile data loading

3. **Test Role-Based Access**:
   - Company users should access `/org/dashboard`
   - Interviewer users should access `/sourcer/dashboard`

## 🎨 Features Implemented

### ✅ Authentication
- Clerk-based login/registration
- Custom themed auth components
- Automatic user sync with Supabase
- Role-based routing

### ✅ Loading Experience
- Unified app loading animation
- Smooth transitions
- Professional loading screens

### ✅ Database Integration
- Automatic user profile creation
- Role-specific profile tables
- Webhook-based synchronization
- RLS policies for security

### ✅ Code Cleanup
- Removed candidate-related code
- Cleaned up unused utilities
- Simplified loading implementations
- Optimized imports and dependencies

## 🔍 Troubleshooting

### Common Issues

1. **Webhook not working**:
   - Check webhook URL is correct
   - Verify webhook secret is set
   - Check Supabase function logs

2. **User not syncing**:
   - Check Supabase function deployment
   - Verify database permissions
   - Check webhook events are configured

3. **Role-based routing issues**:
   - Verify user role is set correctly
   - Check profile creation in database
   - Ensure RLS policies allow access

### Debug Commands

```bash
# Check Supabase function logs
supabase functions logs clerk-webhook --project-ref your-project-ref

# Test webhook locally
supabase functions serve clerk-webhook --env-file .env.local
```

## 🚀 Next Steps

1. **Configure Social Providers** in Clerk dashboard
2. **Set up email templates** for better user experience
3. **Add user onboarding flow** for role selection
4. **Implement user profile completion** tracking
5. **Add user management features** for admins

## 📞 Support

If you encounter any issues:
1. Check the browser console for errors
2. Review Supabase function logs
3. Verify webhook configuration in Clerk
4. Test database connectivity

The integration is now complete and ready for production use!
