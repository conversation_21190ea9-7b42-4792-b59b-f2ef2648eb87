import { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Typography, message } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useColorModeStore } from '@/store/colorMode.store';
import PasswordResetService from '@/services/passwordResetService';

const { Title, Text } = Typography;

const PasswordResetOTP = ({ email, onSuccess }) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef([]);
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const startCountdown = useCallback(() => {
    setCountdown(60);
    setCanResend(false);

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return timer;
  }, []);

  useEffect(() => {
    const timer = startCountdown();
    inputRefs.current[0]?.focus();
    return () => clearInterval(timer);
  }, [startCountdown]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (index, value) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all 6 digits are entered
    if (newOtp.every((digit) => digit !== '') && newOtp.join('').length === 6) {
      handleVerifyOTP(newOtp.join(''));
    }
  };

  const handleKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    // Handle paste
    if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      navigator.clipboard.readText().then((text) => {
        const digits = text.replace(/\D/g, '').slice(0, 6);
        if (digits.length === 6) {
          const newOtp = digits.split('');
          setOtp(newOtp);
          handleVerifyOTP(digits);
        }
      });
    }
  };

  const handleVerifyOTP = useCallback(
    async (otpCode) => {
      setLoading(true);
      try {
        const result = await PasswordResetService.verifyPasswordResetOTP(email, otpCode);
        message.success('Verification successful!');
        onSuccess?.(result);
      } catch (error) {
        const errorMessage = error.message.includes('expired')
          ? 'Verification code has expired. Please request a new one.'
          : error.message.includes('invalid')
            ? 'Invalid verification code. Please check and try again.'
            : 'Invalid verification code';

        message.error(errorMessage);
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      } finally {
        setLoading(false);
      }
    },
    [email, onSuccess]
  );

  const handleResendOTP = useCallback(async () => {
    setResendLoading(true);
    try {
      await PasswordResetService.resendPasswordResetOTP(email);
      message.success('Verification code sent successfully!');
      startCountdown();
    } catch {
      message.error('Failed to resend verification code. Please try again.');
    } finally {
      setResendLoading(false);
    }
  }, [email, startCountdown]);

  return (
    <div className="w-full max-w-lg mx-auto px-4 sm:px-6">
      {/* Header */}
      <div className="text-center mb-6 sm:mb-8">
        <div className="mb-4">
          <LockOutlined
            style={{
              color: 'var(--primary)',
              fontSize: window.innerWidth < 640 ? 40 : 48,
            }}
          />
        </div>
        <Title
          level={2}
          className={`mb-3 text-lg sm:text-xl md:text-2xl ${isDark ? 'text-white' : 'text-gray-900'}`}
        >
          Reset Your Password
        </Title>
        <Text className={`text-sm sm:text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
          Enter the 6-digit code sent to your email.
          <br /> This code is valid for <strong>10 min.</strong>
        </Text>
      </div>

      {/* OTP Input Boxes */}
      <div className="flex justify-center gap-2 sm:gap-3 mb-6 sm:mb-8">
        {otp.map((digit, index) => (
          <input
            key={index}
            ref={(el) => (inputRefs.current[index] = el)}
            type="text"
            value={digit}
            onChange={(e) => handleInputChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            className={`
              w-12 h-12 sm:w-14 sm:h-14 text-center text-lg sm:text-xl font-mono font-semibold
              ${
                isDark
                  ? 'bg-gray-800 text-white placeholder-gray-500'
                  : 'bg-white text-gray-900 placeholder-gray-400'
              }
              border-2 rounded-xl
              transition-all duration-200
              focus:outline-none focus:ring-0
              ${
                digit
                  ? `border-primary ${isDark ? 'bg-gray-700' : 'bg-blue-50'}`
                  : `${isDark ? 'border-gray-600' : 'border-gray-300'}`
              }
              ${
                loading
                  ? 'opacity-50 cursor-not-allowed'
                  : `${isDark ? 'hover:border-gray-500' : 'hover:border-gray-400'}`
              }
              ${
                index === otp.findIndex((d) => d === '') && !loading
                  ? 'border-primary shadow-lg shadow-primary/20'
                  : ''
              }
            `}
            maxLength={1}
            disabled={loading}
            autoComplete="one-time-code"
          />
        ))}
      </div>

      {/* Verify Button */}
      <Button
        type="primary"
        size="large"
        block
        loading={loading}
        onClick={() => handleVerifyOTP(otp.join(''))}
        disabled={otp.some((digit) => digit === '') || loading}
        className="h-12 sm:h-14 rounded-xl font-semibold text-sm sm:text-base mb-6"
        icon={<LockOutlined />}
      >
        Verify Code
      </Button>

      {/* Resend Section */}
      <div className="text-center">
        <Text className={`text-xs sm:text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
          Didn't get the code?{' '}
          <button
            onClick={handleResendOTP}
            disabled={!canResend || resendLoading}
            className={`
              font-semibold underline transition-colors text-xs sm:text-sm
              ${
                canResend && !resendLoading
                  ? 'text-primary hover:text-primary-hover cursor-pointer'
                  : `${isDark ? 'text-gray-500' : 'text-gray-400'} cursor-not-allowed`
              }
            `}
          >
            {resendLoading
              ? 'Sending...'
              : canResend
                ? 'Resend code'
                : `Resend in ${formatTime(countdown)}`}
          </button>
        </Text>
      </div>
    </div>
  );
};

export default PasswordResetOTP;
