/**
 * Google Meet Service
 * 
 * This service handles Google Meet integration for creating
 * and managing video meetings for interviews and events.
 */

import { getGoogleAPIClient, isSignedInToGoogle } from '@/utils/googleApi';
import showToast from '@/utils/toast';

/**
 * Create a Google Meet meeting
 * @param {Object} meetingData - Meeting configuration
 * @returns {Promise<Object>} - Meeting creation result
 */
export const createGoogleMeet = async (meetingData) => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    const {
      title,
      description,
      startTime,
      endTime,
      attendees = [],
      timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    } = meetingData;

    // Create calendar event with Google Meet
    const event = {
      summary: title,
      description: description || '',
      start: {
        dateTime: startTime,
        timeZone: timezone
      },
      end: {
        dateTime: endTime,
        timeZone: timezone
      },
      attendees: attendees.map(email => ({ email })),
      conferenceData: {
        createRequest: {
          requestId: generateRequestId(),
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          }
        }
      },
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'email', minutes: 24 * 60 }, // 1 day before
          { method: 'popup', minutes: 15 } // 15 minutes before
        ]
      }
    };

    const response = await client.calendar.events.insert({
      calendarId: 'primary',
      resource: event,
      conferenceDataVersion: 1,
      sendUpdates: 'all' // Send invitations to attendees
    });

    const createdEvent = response.result;
    const meetingInfo = extractMeetingInfo(createdEvent);

    return {
      success: true,
      data: {
        eventId: createdEvent.id,
        meetingLink: meetingInfo.meetingLink,
        meetingId: meetingInfo.meetingId,
        dialInInfo: meetingInfo.dialInInfo,
        event: createdEvent
      }
    };
  } catch (error) {
    console.error('Error creating Google Meet:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update Google Meet meeting
 * @param {string} eventId - Calendar event ID
 * @param {Object} updateData - Updated meeting data
 * @returns {Promise<Object>} - Update result
 */
export const updateGoogleMeet = async (eventId, updateData) => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    // First, get the existing event
    const existingEvent = await client.calendar.events.get({
      calendarId: 'primary',
      eventId
    });

    const {
      title,
      description,
      startTime,
      endTime,
      attendees,
      timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    } = updateData;

    // Update event data
    const updatedEvent = {
      ...existingEvent.result,
      summary: title || existingEvent.result.summary,
      description: description || existingEvent.result.description,
      start: startTime ? {
        dateTime: startTime,
        timeZone: timezone
      } : existingEvent.result.start,
      end: endTime ? {
        dateTime: endTime,
        timeZone: timezone
      } : existingEvent.result.end,
      attendees: attendees ? attendees.map(email => ({ email })) : existingEvent.result.attendees
    };

    const response = await client.calendar.events.update({
      calendarId: 'primary',
      eventId,
      resource: updatedEvent,
      conferenceDataVersion: 1,
      sendUpdates: 'all'
    });

    const meetingInfo = extractMeetingInfo(response.result);

    return {
      success: true,
      data: {
        eventId: response.result.id,
        meetingLink: meetingInfo.meetingLink,
        meetingId: meetingInfo.meetingId,
        dialInInfo: meetingInfo.dialInInfo,
        event: response.result
      }
    };
  } catch (error) {
    console.error('Error updating Google Meet:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete Google Meet meeting
 * @param {string} eventId - Calendar event ID
 * @returns {Promise<Object>} - Deletion result
 */
export const deleteGoogleMeet = async (eventId) => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    await client.calendar.events.delete({
      calendarId: 'primary',
      eventId,
      sendUpdates: 'all' // Notify attendees
    });

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting Google Meet:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get Google Meet details from event
 * @param {string} eventId - Calendar event ID
 * @returns {Promise<Object>} - Meeting details
 */
export const getGoogleMeetDetails = async (eventId) => {
  try {
    if (!isSignedInToGoogle()) {
      throw new Error('Not signed in to Google');
    }

    const client = getGoogleAPIClient();
    if (!client) {
      throw new Error('Google API client not available');
    }

    const response = await client.calendar.events.get({
      calendarId: 'primary',
      eventId
    });

    const event = response.result;
    const meetingInfo = extractMeetingInfo(event);

    return {
      success: true,
      data: {
        eventId: event.id,
        title: event.summary,
        description: event.description,
        startTime: event.start.dateTime,
        endTime: event.end.dateTime,
        attendees: event.attendees || [],
        meetingLink: meetingInfo.meetingLink,
        meetingId: meetingInfo.meetingId,
        dialInInfo: meetingInfo.dialInInfo,
        status: event.status
      }
    };
  } catch (error) {
    console.error('Error getting Google Meet details:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Extract meeting information from Google Calendar event
 * @param {Object} event - Google Calendar event
 * @returns {Object} - Meeting information
 */
const extractMeetingInfo = (event) => {
  const meetingInfo = {
    meetingLink: null,
    meetingId: null,
    dialInInfo: null
  };

  // Check for hangout link (Google Meet)
  if (event.hangoutLink) {
    meetingInfo.meetingLink = event.hangoutLink;
    
    // Extract meeting ID from the link
    const meetingIdMatch = event.hangoutLink.match(/\/([a-z-]+)$/);
    if (meetingIdMatch) {
      meetingInfo.meetingId = meetingIdMatch[1];
    }
  }

  // Check for conference data
  if (event.conferenceData && event.conferenceData.entryPoints) {
    const videoEntry = event.conferenceData.entryPoints.find(
      entry => entry.entryPointType === 'video'
    );
    
    if (videoEntry) {
      meetingInfo.meetingLink = videoEntry.uri;
    }

    const phoneEntry = event.conferenceData.entryPoints.find(
      entry => entry.entryPointType === 'phone'
    );
    
    if (phoneEntry) {
      meetingInfo.dialInInfo = {
        phoneNumber: phoneEntry.uri.replace('tel:', ''),
        pin: phoneEntry.pin
      };
    }
  }

  return meetingInfo;
};

/**
 * Generate unique request ID for conference creation
 * @returns {string} - Unique request ID
 */
const generateRequestId = () => {
  return `flyt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Create instant Google Meet (for immediate use)
 * @param {Object} meetingData - Basic meeting data
 * @returns {Promise<Object>} - Instant meeting result
 */
export const createInstantGoogleMeet = async (meetingData) => {
  const now = new Date();
  const endTime = new Date(now.getTime() + (60 * 60 * 1000)); // 1 hour from now

  return createGoogleMeet({
    ...meetingData,
    startTime: now.toISOString(),
    endTime: endTime.toISOString()
  });
};

export default {
  createGoogleMeet,
  updateGoogleMeet,
  deleteGoogleMeet,
  getGoogleMeetDetails,
  createInstantGoogleMeet
};
