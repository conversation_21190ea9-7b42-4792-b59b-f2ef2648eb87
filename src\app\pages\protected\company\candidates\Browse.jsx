import React from 'react';
import { Card, Typography, Empty } from 'antd';

const { Title, Text } = Typography;

const Browse = () => {
  return (
    <div className="p-6">
      <Title level={2}>Browse Candidates</Title>
      <Card>
        <Empty
          description={
            <div>
              <Text type="secondary">Candidate browsing functionality is not available.</Text>
              <br />
              <Text type="secondary">This feature has been removed from the platform.</Text>
            </div>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    </div>
  );
};

export default Browse;
