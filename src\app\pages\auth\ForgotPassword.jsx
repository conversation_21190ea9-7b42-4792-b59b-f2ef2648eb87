import { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Card, Form, Input, Typography, Alert, Result, Steps, Space } from 'antd';
import {
  MailOutlined,
  ArrowLeftOutlined,
  SendOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useColorModeStore } from '@/store/colorMode.store';
import PasswordResetService from '@/services/passwordResetService';
import PasswordResetOTP from '@/components/auth/PasswordResetOTP';
import NewPasswordForm from '@/components/auth/NewPasswordForm';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const ForgotPassword = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [email, setEmail] = useState('');
  const [currentStep, setCurrentStep] = useState(0);
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const handleSendOTP = useCallback(async (values) => {
    setLoading(true);
    setError(null);
    setEmail(values.email);

    try {
      await PasswordResetService.sendPasswordResetOTP(values.email);
      setCurrentStep(1);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleOTPSuccess = useCallback(() => setCurrentStep(2), []);
  const handlePasswordResetSuccess = useCallback(() => setCurrentStep(3), []);

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  const stepTitles = ['Email', 'Verify', 'Reset', 'Complete'];

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <div className="text-center mb-6">
              <Title
                level={3}
                className={`mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}
              >
                Forgot Your Password?
              </Title>
              <Paragraph className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                Enter the email address associated with your account and we'll send you a
                verification code to reset your password.
              </Paragraph>
            </div>

            {error && (
              <Alert
                message={
                  error.includes('No account found')
                    ? 'Account Not Found'
                    : error.includes('verify your email')
                      ? 'Email Not Verified'
                      : 'Error'
                }
                description={
                  <div>
                    <p>{error}</p>
                    {error.includes('No account found') && (
                      <div className="mt-3">
                        <Link
                          to="/register"
                          className="text-primary hover:text-primary-hover font-medium"
                        >
                          Create a new account →
                        </Link>
                      </div>
                    )}
                    {error.includes('verify your email') && (
                      <div className="mt-3">
                        <Link
                          to="/login"
                          className="text-primary hover:text-primary-hover font-medium"
                        >
                          Go to login to resend verification →
                        </Link>
                      </div>
                    )}
                  </div>
                }
                type={
                  error.includes('No account found') || error.includes('verify your email')
                    ? 'warning'
                    : 'error'
                }
                showIcon
                className="mb-6"
                closable
                onClose={() => setError(null)}
              />
            )}

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSendOTP}
              requiredMark={false}
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: 'Please enter your email' },
                  { type: 'email', message: 'Please enter a valid email' },
                ]}
              >
                <Input
                  prefix={
                    <MailOutlined className={`${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
                  }
                  placeholder="Enter your registered email address"
                  size="large"
                  className={`h-12 rounded-xl ${isDark ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300'}`}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={loading}
                  icon={<SendOutlined />}
                  className="h-12 rounded-xl font-medium mt-4"
                >
                  Send Verification Code
                </Button>
              </Form.Item>
            </Form>

            <div className="text-center mt-6">
              <Space>
                <ArrowLeftOutlined />
                <Link
                  to="/login"
                  className="text-primary hover:text-primary-hover"
                >
                  Back to Login
                </Link>
              </Space>
            </div>
          </motion.div>
        );

      case 1:
        return (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <PasswordResetOTP
              email={email}
              onSuccess={handleOTPSuccess}
            />
            <div className="text-center mt-6">
              <Space>
                <ArrowLeftOutlined />
                <Link
                  to="/login"
                  className="text-primary hover:text-primary-hover"
                >
                  Back to Login
                </Link>
              </Space>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <NewPasswordForm onSuccess={handlePasswordResetSuccess} />
            <div className="text-center mt-6">
              <Space>
                <ArrowLeftOutlined />
                <Link
                  to="/login"
                  className="text-primary hover:text-primary-hover"
                >
                  Back to Login
                </Link>
              </Space>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <Result
              status="success"
              title={
                <span className={`${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Password Reset Successful!
                </span>
              }
              subTitle={
                <span className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  Your password has been reset successfully. You can now log in with your new
                  password.
                </span>
              }
              extra={[
                <Button
                  type="primary"
                  key="login"
                  size="large"
                  onClick={() => navigate('/login')}
                  className="h-12 rounded-xl font-medium"
                  icon={<CheckCircleOutlined />}
                >
                  Go to Login
                </Button>,
              ]}
            />
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-lg">
        <Card
          variant="bordered"
          className={`shadow-lg rounded-xl overflow-hidden ${isDark ? 'bg-gray-900 border-gray-700' : 'bg-white border-gray-200'}`}
        >
          <Steps
            current={currentStep}
            className="px-4 pb-6"
          >
            {stepTitles.map((title) => (
              <Step
                key={title}
                title={title}
              />
            ))}
          </Steps>

          <div className="p-1 sm:p-2 md:p-6">{renderStep()}</div>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPassword;
