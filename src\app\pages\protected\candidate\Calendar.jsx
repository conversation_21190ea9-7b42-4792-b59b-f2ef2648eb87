/**
 * Candidate Calendar Page
 *
 * Calendar page specifically designed for candidates with:
 * - Interview scheduling and requests
 * - Application deadlines
 * - Assessment schedules
 * - Interview preparation time
 * - Google Calendar integration
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Space,
  Button,
  Row,
  Col,
  Statistic,
  Tag,
  List,
  Avatar,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  TimePicker,
  message,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  UserOutlined,
  GoogleOutlined,
  PlusOutlined,
  BlockOutlined,
  SendOutlined,
  BookOutlined,
} from '@ant-design/icons';
import Calendar from '@/components/shared/Calendar';
import useAuth from '@/hooks/useAuth';
import {
  getCandidateRequests,
  getCalendarEvents,
  createInterviewRequest,
} from '@/services/interviewRequest.service';
import { useJobs } from '@/features/candidate/hooks';
import { getRelativeTime } from '@/utils/helpers';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CandidateCalendar = () => {
  const { user, profile } = useAuth();
  const { jobs } = useJobs();

  const [calendarStats, setCalendarStats] = useState({
    pendingRequests: 0,
    scheduledInterviews: 0,
    completedInterviews: 0,
    totalEvents: 0,
  });

  const [interviewRequests, setInterviewRequests] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [requestModalVisible, setRequestModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  // Load candidate data on component mount
  useEffect(() => {
    if (user?.id) {
      loadInterviewRequests();
      loadCalendarEvents();
    }
  }, [user?.id]);

  // Load interview requests
  const loadInterviewRequests = async () => {
    if (!user?.id) return;

    try {
      const { success, data } = await getCandidateRequests(user.id);
      if (success) {
        setInterviewRequests(data);
      }
    } catch (error) {
      console.error('Error loading interview requests:', error);
    }
  };

  // Load calendar events
  const loadCalendarEvents = async () => {
    if (!user?.id) return;

    try {
      const { success, data } = await getCalendarEvents(user.id, 'candidate');
      if (success) {
        setCalendarEvents(data);
      }
    } catch (error) {
      console.error('Error loading calendar events:', error);
    }
  };

  // Calculate calendar statistics
  useEffect(() => {
    const pendingRequests =
      interviewRequests?.filter((req) => req.status === 'pending').length || 0;

    const scheduledInterviews =
      interviewRequests?.filter((req) => req.status === 'accepted').length || 0;

    const completedInterviews =
      interviewRequests?.filter((req) => req.status === 'completed').length || 0;

    setCalendarStats({
      pendingRequests,
      scheduledInterviews,
      completedInterviews,
      totalEvents: calendarEvents.length,
    });
  }, [interviewRequests, calendarEvents]);

  // Handle interview request submission
  const handleRequestSubmit = async (values) => {
    try {
      setLoading(true);

      // Ensure we have valid dayjs objects
      const preferredDate = dayjs(values.preferredDate);
      const preferredTime = dayjs(values.preferredTime);

      if (!preferredDate.isValid() || !preferredTime.isValid()) {
        message.error('Please select valid date and time');
        setLoading(false);
        return;
      }

      // Combine date and time using dayjs
      const interviewDateTime = preferredDate
        .hour(preferredTime.hour())
        .minute(preferredTime.minute())
        .second(0)
        .millisecond(0);

      const requestData = {
        candidate_id: user.id,
        job_id: values.jobId,
        preferred_date: interviewDateTime.toISOString(),
        duration_minutes: values.duration || 60,
        interview_type: values.interviewType || 'video',
        message: values.message,
      };

      const { success, error } = await createInterviewRequest(requestData);

      if (success) {
        message.success('Interview request submitted successfully!');
        setRequestModalVisible(false);
        form.resetFields();
        loadInterviewRequests(); // Refresh the list
      } else {
        message.error(error || 'Failed to submit request');
      }
    } catch (error) {
      console.error('Error submitting request:', error);
      message.error('Failed to submit request');
    } finally {
      setLoading(false);
    }
  };

  // Candidate-specific event types
  const candidateEventTypes = [
    { id: 'interview', name: 'Interview', color: 'blue' },
    { id: 'assessment', name: 'Assessment', color: 'green' },
    { id: 'preparation', name: 'Preparation', color: 'orange' },
    { id: 'application_deadline', name: 'Application Deadline', color: 'red' },
    { id: 'follow_up', name: 'Follow Up', color: 'purple' },
  ];

  return (
    <div className="candidate-calendar-page p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div>
              <CalendarOutlined className="text-2xl text-primary mr-3" />
              <Title
                level={2}
                className="m-0"
              >
                Calendar
              </Title>
              <Text type="secondary">
                Manage your interview schedule and sync with Google Calendar
              </Text>
            </div>
          </div>
          <Space>
            <Button
              type="primary"
              icon={<SendOutlined />}
              size="large"
              onClick={() => setRequestModalVisible(true)}
            >
              Request Interview
            </Button>
            <Button
              icon={<GoogleOutlined />}
              size="large"
            >
              Connect Google
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row
        gutter={16}
        className="mb-6"
      >
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Pending Requests"
              value={calendarStats.pendingRequests}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Scheduled Interviews"
              value={calendarStats.scheduledInterviews}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Completed Interviews"
              value={calendarStats.completedInterviews}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Total Events"
              value={calendarStats.totalEvents}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Calendar Layout */}
      <Row gutter={16}>
        {/* Sidebar */}
        <Col
          xs={24}
          lg={6}
        >
          {/* My Schedule */}
          <Card
            title="My Schedule"
            className="mb-4"
            size="small"
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              <div className="flex items-center justify-between">
                <Text>Interview Requests</Text>
                <Tag color="blue">{calendarStats.pendingRequests}</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Scheduled Interviews</Text>
                <Tag color="green">{calendarStats.scheduledInterviews}</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Preparation Time</Text>
                <Tag color="orange">3</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Application Deadlines</Text>
                <Tag color="red">2</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Follow Ups</Text>
                <Tag color="purple">1</Tag>
              </div>
            </Space>
          </Card>

          {/* Categories */}
          <Card
            title="Categories"
            className="mb-4"
            size="small"
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              {candidateEventTypes.map((type) => (
                <div
                  key={type.id}
                  className="flex items-center"
                >
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{
                      backgroundColor:
                        type.color === 'blue'
                          ? '#1890ff'
                          : type.color === 'green'
                            ? '#52c41a'
                            : type.color === 'orange'
                              ? '#faad14'
                              : type.color === 'red'
                                ? '#f5222d'
                                : type.color === 'purple'
                                  ? '#722ed1'
                                  : '#8c8c8c',
                    }}
                  />
                  <Text>{type.name}</Text>
                </div>
              ))}
            </Space>
          </Card>

          {/* Recent Activity */}
          <Card
            title="Recent Activity"
            size="small"
          >
            <List
              size="small"
              dataSource={interviewRequests.slice(0, 3)}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<BlockOutlined />}
                        size="small"
                      />
                    }
                    title={
                      <Text
                        strong
                        style={{ fontSize: '12px' }}
                      >
                        {item.status === 'pending'
                          ? 'Request Sent'
                          : item.status === 'accepted'
                            ? 'Interview Scheduled'
                            : 'Interview Completed'}
                      </Text>
                    }
                    description={
                      <div>
                        <Text style={{ fontSize: '11px' }}>{item.job_title}</Text>
                        <br />
                        <Text
                          type="secondary"
                          style={{ fontSize: '10px' }}
                        >
                          {getRelativeTime(item.created_at)}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Main Calendar */}
        <Col
          xs={24}
          lg={18}
        >
          <Calendar
            userType="candidate"
            eventTypes={candidateEventTypes}
            events={calendarEvents}
            viewOptions={{
              month: true,
              day: true,
              agenda: true,
            }}
          />
        </Col>
      </Row>

      {/* Interview Request Modal */}
      <Modal
        title="Request Interview"
        open={requestModalVisible}
        onCancel={() => {
          setRequestModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleRequestSubmit}
          initialValues={{
            duration: 60,
            interviewType: 'video',
            preferredDate: dayjs().add(1, 'day'),
            preferredTime: dayjs().hour(10).minute(0),
          }}
        >
          <Form.Item
            name="jobId"
            label="Select Job"
            rules={[{ required: true, message: 'Please select a job' }]}
          >
            <Select placeholder="Choose a job position">
              {jobs?.map((job) => (
                <Option
                  key={job.id}
                  value={job.id}
                >
                  {job.title} - {job.company_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="preferredDate"
                label="Preferred Date"
                rules={[{ required: true, message: 'Please select a date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="preferredTime"
                label="Preferred Time"
                rules={[{ required: true, message: 'Please select a time' }]}
              >
                <TimePicker
                  style={{ width: '100%' }}
                  format="HH:mm"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="duration"
                label="Duration (minutes)"
              >
                <Select>
                  <Option value={30}>30 minutes</Option>
                  <Option value={45}>45 minutes</Option>
                  <Option value={60}>60 minutes</Option>
                  <Option value={90}>90 minutes</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="interviewType"
                label="Interview Type"
              >
                <Select>
                  <Option value="video">Video Call</Option>
                  <Option value="phone">Phone Call</Option>
                  <Option value="in-person">In Person</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="message"
            label="Message (Optional)"
          >
            <TextArea
              rows={3}
              placeholder="Add any specific requirements or preferences..."
            />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={() => setRequestModalVisible(false)}>Cancel</Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                Submit Request
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CandidateCalendar;
