import React from 'react';
import { <PERSON>po<PERSON>, <PERSON>, <PERSON>, Card, Button, Space, Divider } from 'antd';
import { RocketOutlined, TeamOutlined, UserOutlined, CheckOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const Products = () => {
  return (
    <div className="products-page">
      <div className="hero-section text-center py-12">
        <Title level={1}>Our Products</Title>
        <Paragraph className="text-lg max-w-3xl mx-auto">
          Discover the perfect interview solution for your needs. Whether you're a business looking
          to streamline your hiring process or an individual preparing for interviews, we have you
          covered.
        </Paragraph>
      </div>

      <Row
        gutter={[32, 32]}
        className="py-8"
      >
        <Col
          xs={24}
          md={12}
        >
          <Card className="h-full product-card shadow-lg hover:shadow-xl transition-shadow">
            <Title
              level={2}
              className="flex items-center"
            >
              <TeamOutlined className="mr-3 text-primary" /> For Business
            </Title>
            <Paragraph className="text-lg mb-6">
              Streamline your hiring process with our comprehensive interview platform designed for
              companies of all sizes.
            </Paragraph>
            <ul className="space-y-3 mb-8">
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Automated candidate screening and assessment</span>
              </li>
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Collaborative interview feedback and scoring</span>
              </li>
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Custom interview templates and question banks</span>
              </li>
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Advanced analytics and reporting</span>
              </li>
            </ul>
            <Button
              type="primary"
              size="large"
            >
              Learn More
            </Button>
          </Card>
        </Col>

        <Col
          xs={24}
          md={12}
        >
          <Card className="h-full product-card shadow-lg hover:shadow-xl transition-shadow">
            <Title
              level={2}
              className="flex items-center"
            >
              <UserOutlined className="mr-3 text-primary" /> For Individuals
            </Title>
            <Paragraph className="text-lg mb-6">
              Prepare for your next interview with our personalized practice platform designed for
              job seekers.
            </Paragraph>
            <ul className="space-y-3 mb-8">
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Practice with industry-specific interview questions</span>
              </li>
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Get feedback on your interview responses</span>
              </li>
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Connect with professional interviewers for coaching</span>
              </li>
              <li className="flex items-start">
                <CheckOutlined className="mt-1 mr-3 text-primary" />
                <span>Track your progress and improvement areas</span>
              </li>
            </ul>
            <Button
              type="primary"
              size="large"
            >
              Learn More
            </Button>
          </Card>
        </Col>
      </Row>

      <Divider />

      <div className="cta-section text-center py-12">
        <Title level={2}>Ready to transform your interview experience?</Title>
        <Paragraph className="text-lg max-w-3xl mx-auto mb-8">
          Join thousands of businesses and individuals who have improved their interview process
          with our platform.
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            icon={<RocketOutlined />}
          >
            Get Started
          </Button>
          <Button size="large">Contact Sales</Button>
        </Space>
      </div>
    </div>
  );
};

export default Products;
