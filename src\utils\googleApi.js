/**
 * Google API Utilities for Calendar and Meet Integration
 *
 * This module handles Google Calendar API and Google Meet integration
 * for the Flyt platform's calendar scheduling system.
 */

// Google API Configuration
const GOOGLE_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID,
  API_KEY: import.meta.env.VITE_GOOGLE_API_KEY,
  DISCOVERY_DOCS: [
    'https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest',
    'https://www.googleapis.com/discovery/v1/apis/people/v1/rest',
  ],
  SCOPES: [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email',
  ],
};

let gapi = null;
let isInitialized = false;

/**
 * Initialize Google API
 * @returns {Promise<boolean>} - Success status
 */
export const initializeGoogleAPI = async () => {
  try {
    if (isInitialized) return true;

    // Load Google API script if not already loaded
    if (!window.gapi) {
      await loadGoogleAPIScript();
    }

    gapi = window.gapi;

    // Initialize the API
    await new Promise((resolve, reject) => {
      gapi.load('auth2:client', {
        callback: resolve,
        onerror: reject,
      });
    });

    await gapi.client.init({
      apiKey: GOOGLE_CONFIG.API_KEY,
      clientId: GOOGLE_CONFIG.CLIENT_ID,
      discoveryDocs: GOOGLE_CONFIG.DISCOVERY_DOCS,
      scope: GOOGLE_CONFIG.SCOPES.join(' '),
    });

    isInitialized = true;
    return true;
  } catch (error) {
    console.error('Failed to initialize Google API:', error);
    return false;
  }
};

/**
 * Load Google API script dynamically
 * @returns {Promise<void>}
 */
const loadGoogleAPIScript = () => {
  return new Promise((resolve, reject) => {
    if (window.gapi) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://apis.google.com/js/api.js';
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

// Note: Google authentication is now handled by Clerk
// These functions are kept for Google API integration (Calendar, etc.)

/**
 * Check if Google API is available and initialized
 * @returns {boolean} - Availability status
 */
export const isGoogleAPIAvailable = () => {
  return isInitialized && !!gapi;
};

/**
 * Get Google API client instance
 * @returns {Object|null} - Google API client or null
 */
export const getGoogleAPIClient = () => {
  return isGoogleAPIAvailable() ? gapi.client : null;
};

export default {
  initializeGoogleAPI,
  isGoogleAPIAvailable,
  getGoogleAPIClient,
};
