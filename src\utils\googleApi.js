/**
 * Google API Utilities for Calendar and Meet Integration
 * 
 * This module handles Google Calendar API and Google Meet integration
 * for the Flyt platform's calendar scheduling system.
 */

// Google API Configuration
const GOOGLE_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_GOOGLE_CLIENT_ID,
  API_KEY: import.meta.env.VITE_GOOGLE_API_KEY,
  DISCOVERY_DOCS: [
    'https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest',
    'https://www.googleapis.com/discovery/v1/apis/people/v1/rest'
  ],
  SCOPES: [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email'
  ]
};

let gapi = null;
let isInitialized = false;

/**
 * Initialize Google API
 * @returns {Promise<boolean>} - Success status
 */
export const initializeGoogleAPI = async () => {
  try {
    if (isInitialized) return true;

    // Load Google API script if not already loaded
    if (!window.gapi) {
      await loadGoogleAPIScript();
    }

    gapi = window.gapi;

    // Initialize the API
    await new Promise((resolve, reject) => {
      gapi.load('auth2:client', {
        callback: resolve,
        onerror: reject
      });
    });

    await gapi.client.init({
      apiKey: GOOGLE_CONFIG.API_KEY,
      clientId: GOOGLE_CONFIG.CLIENT_ID,
      discoveryDocs: GOOGLE_CONFIG.DISCOVERY_DOCS,
      scope: GOOGLE_CONFIG.SCOPES.join(' ')
    });

    isInitialized = true;
    return true;
  } catch (error) {
    console.error('Failed to initialize Google API:', error);
    return false;
  }
};

/**
 * Load Google API script dynamically
 * @returns {Promise<void>}
 */
const loadGoogleAPIScript = () => {
  return new Promise((resolve, reject) => {
    if (window.gapi) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://apis.google.com/js/api.js';
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

/**
 * Sign in to Google
 * @returns {Promise<Object>} - User info and auth status
 */
export const signInToGoogle = async () => {
  try {
    if (!isInitialized) {
      const initialized = await initializeGoogleAPI();
      if (!initialized) throw new Error('Failed to initialize Google API');
    }

    const authInstance = gapi.auth2.getAuthInstance();
    const user = await authInstance.signIn();
    
    const profile = user.getBasicProfile();
    const authResponse = user.getAuthResponse();

    return {
      success: true,
      user: {
        id: profile.getId(),
        name: profile.getName(),
        email: profile.getEmail(),
        picture: profile.getImageUrl(),
        accessToken: authResponse.access_token
      }
    };
  } catch (error) {
    console.error('Google sign-in failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Sign out from Google
 * @returns {Promise<boolean>} - Success status
 */
export const signOutFromGoogle = async () => {
  try {
    if (!isInitialized) return true;

    const authInstance = gapi.auth2.getAuthInstance();
    await authInstance.signOut();
    return true;
  } catch (error) {
    console.error('Google sign-out failed:', error);
    return false;
  }
};

/**
 * Check if user is signed in to Google
 * @returns {boolean} - Sign-in status
 */
export const isSignedInToGoogle = () => {
  if (!isInitialized || !gapi) return false;
  
  const authInstance = gapi.auth2.getAuthInstance();
  return authInstance.isSignedIn.get();
};

/**
 * Get current Google user info
 * @returns {Object|null} - User info or null
 */
export const getCurrentGoogleUser = () => {
  if (!isSignedInToGoogle()) return null;

  const authInstance = gapi.auth2.getAuthInstance();
  const user = authInstance.currentUser.get();
  const profile = user.getBasicProfile();
  const authResponse = user.getAuthResponse();

  return {
    id: profile.getId(),
    name: profile.getName(),
    email: profile.getEmail(),
    picture: profile.getImageUrl(),
    accessToken: authResponse.access_token
  };
};

/**
 * Refresh Google access token
 * @returns {Promise<string|null>} - New access token or null
 */
export const refreshGoogleToken = async () => {
  try {
    if (!isSignedInToGoogle()) return null;

    const authInstance = gapi.auth2.getAuthInstance();
    const user = authInstance.currentUser.get();
    const authResponse = await user.reloadAuthResponse();
    
    return authResponse.access_token;
  } catch (error) {
    console.error('Failed to refresh Google token:', error);
    return null;
  }
};

/**
 * Check if Google API is available and initialized
 * @returns {boolean} - Availability status
 */
export const isGoogleAPIAvailable = () => {
  return isInitialized && !!gapi;
};

/**
 * Get Google API client instance
 * @returns {Object|null} - Google API client or null
 */
export const getGoogleAPIClient = () => {
  return isGoogleAPIAvailable() ? gapi.client : null;
};

export default {
  initializeGoogleAPI,
  signInToGoogle,
  signOutFromGoogle,
  isSignedInToGoogle,
  getCurrentGoogleUser,
  refreshGoogleToken,
  isGoogleAPIAvailable,
  getGoogleAPIClient
};
