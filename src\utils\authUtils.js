/**
 * Utility functions for authentication
 */

/**
 * Clears all authentication-related data from browser storage
 * This includes localStorage, sessionStorage, and cookies
 */
export const clearAuthStorage = () => {
  if (typeof window !== 'undefined' && window.localStorage) {
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith('supabase.auth.token') ||
          key.startsWith('sb-') || // Handles sb-{project-id}-auth-token format
          key.includes('auth-storage') ||
          key.includes('supabase-auth') ||
          key.includes('supabase'))
      ) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => {
      localStorage.removeItem(key);
    });
  }

  if (typeof window !== 'undefined' && window.sessionStorage) {
    const keysToRemove = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (key.includes('supabase') || key.includes('auth') || key.startsWith('sb-'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach((key) => {
      sessionStorage.removeItem(key);
    });
  }

  if (typeof document !== 'undefined') {
    document.cookie.split(';').forEach((cookie) => {
      const [name] = cookie.trim().split('=');
      if (name && (name.includes('supabase') || name.includes('auth') || name.startsWith('sb-'))) {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      }
    });
  }
};

/**
 * Checks if the user is authenticated by verifying localStorage and sessionStorage
 * @returns {boolean} True if user appears to be authenticated
 */
export const isUserAuthenticated = () => {
  if (typeof window === 'undefined') return false;

  // Check localStorage for auth tokens
  let hasAuthToken = false;
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('supabase.auth.token')) {
      hasAuthToken = true;
      break;
    }
  }

  // Check for auth-storage with isAuthenticated flag
  const authStorage = localStorage.getItem('auth-storage');
  if (authStorage) {
    try {
      const parsed = JSON.parse(authStorage);
      if (parsed.state && parsed.state.isAuthenticated === true) {
        return true;
      }
    } catch (e) {
      console.error('Error parsing auth-storage:', e);
    }
  }

  return hasAuthToken;
};
