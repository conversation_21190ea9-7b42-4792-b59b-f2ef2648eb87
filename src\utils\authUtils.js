/**
 * Utility functions for authentication with Clerk
 */

/**
 * Clears app-specific storage data
 * Note: Clerk handles its own auth storage cleanup automatically
 */
export const clearAppStorage = () => {
  if (typeof window !== 'undefined' && window.localStorage) {
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('auth-storage') || key.includes('user-storage'))) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => {
      localStorage.removeItem(key);
    });
  }
};

/**
 * Get role-based dashboard path
 */
export const getRoleDashboardPath = (role) => {
  const rolePathMap = {
    company: '/org/dashboard',
    interviewer: '/sourcer/dashboard',
  };
  return rolePathMap[role] || '/';
};
