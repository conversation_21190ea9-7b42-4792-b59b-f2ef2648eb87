/**
 * Utility functions for authentication with <PERSON>
 */

/**
 * Clears all authentication-related data from browser storage
 * This includes localStorage, sessionStorage, and cookies
 * Note: Clerk handles its own auth storage cleanup
 */
export const clearAuthStorage = () => {
  if (typeof window !== 'undefined' && window.localStorage) {
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('auth-storage') || key.includes('user-storage'))) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => {
      localStorage.removeItem(key);
    });
  }
};

/**
 * Checks if the user is authenticated
 * Note: With Clerk, authentication state should be checked via useAuth hook
 * @returns {boolean} True if user appears to be authenticated
 */
export const isUserAuthenticated = () => {
  if (typeof window === 'undefined') return false;

  // With Clerk, authentication state is managed by Clerk
  // This function is kept for compatibility but should use useAuth hook instead
  console.warn('Use useAuth hook to check authentication state with <PERSON>');
  return false;
};
