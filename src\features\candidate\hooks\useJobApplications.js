/**
 * useJobApplications Hook for Candidates
 *
 * Handles job application operations and integrates with candidate store.
 * Uses optimized caching for better performance.
 */

import { useState, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import showToast from '@/utils/toast';

const useJobApplications = () => {
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get store methods
  const { appliedJobs, fetchAppliedJobs } = useCandidateStore();

  /**
   * Apply to a job
   * Uses optimized caching for better performance
   */
  const applyToJob = useCallback(
    async (jobId, applicationData = {}) => {
      if (!user || !profile) {
        showToast.error('Please login to apply for jobs');
        return { success: false, error: 'User not authenticated' };
      }

      setLoading(true);
      setError(null);

      try {
        // Check if already applied
        const existingApplication = appliedJobs.find((app) => app.job_id === jobId);
        if (existingApplication) {
          showToast.warning('You have already applied to this job');
          return { success: false, error: 'Already applied' };
        }

        // Create application directly
        const { data, error } = await supabase
          .from('applications')
          .insert({
            candidate_id: profile.id,
            job_id: jobId,
            status: 'applied',
            application_date: new Date().toISOString(),
            ...applicationData,
          })
          .select()
          .single();

        if (error) throw error;

        // Update store with new application
        await fetchAppliedJobs(profile.id, true);

        showToast.success('Application submitted successfully!');
        return { success: true, data };
      } catch (error) {
        console.error('Error applying to job:', error);
        const errorMessage = error.message || 'Failed to submit application';
        setError(errorMessage);
        showToast.error(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [user, profile, appliedJobs, fetchAppliedJobs]
  );

  /**
   * Withdraw job application
   * Invalidates cache after update
   */
  const withdrawApplication = useCallback(
    async (applicationId) => {
      if (!user || !profile) {
        showToast.error('Please login to withdraw application');
        return { success: false, error: 'User not authenticated' };
      }

      setLoading(true);
      setError(null);

      try {
        const { error } = await supabase
          .from('applications')
          .update({
            status: 'withdrawn',
            updated_at: new Date().toISOString(),
          })
          .eq('id', applicationId)
          .eq('candidate_id', profile.id);

        if (error) throw error;

        // Refresh applied jobs
        await fetchAppliedJobs(profile.id, true);

        showToast.success('Application withdrawn successfully');
        return { success: true };
      } catch (error) {
        console.error('Error withdrawing application:', error);
        const errorMessage = error.message || 'Failed to withdraw application';
        setError(errorMessage);
        showToast.error(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [user, profile, fetchAppliedJobs]
  );

  /**
   * Get application status for a job
   * Uses cached data for better performance
   */
  const getApplicationStatus = useCallback(
    async (jobId, forceRefresh = false) => {
      if (!user?.id) return null;

      // Check local state first
      const localApplication = appliedJobs.find((app) => app.job_id === jobId);
      if (localApplication && !forceRefresh) {
        return localApplication.status;
      }

      // If not in local state or forcing refresh, fetch from database via store
      await fetchAppliedJobs(user.id, true);
      const application = appliedJobs.find((app) => app.job_id === jobId);
      return application ? application.status : null;
    },
    [appliedJobs, user, fetchAppliedJobs]
  );

  /**
   * Check if user has applied to a job
   */
  const hasAppliedToJob = useCallback(
    (jobId) => {
      return appliedJobs.some((app) => app.job_id === jobId);
    },
    [appliedJobs]
  );

  /**
   * Get application details for a job
   */
  const getApplicationDetails = useCallback(
    (jobId) => {
      return appliedJobs.find((app) => app.job_id === jobId) || null;
    },
    [appliedJobs]
  );

  /**
   * Update application status (for internal use)
   * Invalidates cache after update
   */
  const updateApplicationStatus = useCallback(
    async (applicationId, newStatus) => {
      if (!user || !profile) {
        return { success: false, error: 'User not authenticated' };
      }

      try {
        const { error } = await supabase
          .from('applications')
          .update({
            status: newStatus,
            updated_at: new Date().toISOString(),
          })
          .eq('id', applicationId)
          .eq('candidate_id', profile.id);

        if (error) throw error;

        // Refresh applied jobs
        await fetchAppliedJobs(profile.id, true);

        return { success: true };
      } catch (error) {
        console.error('Error updating application status:', error);
        return { success: false, error: error.message };
      }
    },
    [user, profile, fetchAppliedJobs]
  );

  /**
   * Get applications by status
   */
  const getApplicationsByStatus = useCallback(
    (status) => {
      return appliedJobs.filter((app) => app.status === status);
    },
    [appliedJobs]
  );

  /**
   * Get application statistics
   */
  const getApplicationStats = useCallback(() => {
    const stats = {
      total: appliedJobs.length,
      applied: 0,
      shortlisted: 0,
      interviewed: 0,
      hired: 0,
      rejected: 0,
      withdrawn: 0,
    };

    appliedJobs.forEach((app) => {
      if (stats.hasOwnProperty(app.status)) {
        stats[app.status]++;
      }
    });

    return stats;
  }, [appliedJobs]);

  return {
    // Data
    appliedJobs,

    // UI State
    loading,
    error,

    // Actions
    applyToJob,
    withdrawApplication,
    updateApplicationStatus,

    // Utilities
    getApplicationStatus,
    hasAppliedToJob,
    getApplicationDetails,
    getApplicationsByStatus,
    getApplicationStats,

    // Refresh
    refetch: () => fetchAppliedJobs(profile?.id, true),
  };
};

export default useJobApplications;
