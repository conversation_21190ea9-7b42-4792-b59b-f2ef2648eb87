/**
 * Combined Welcome and Stats Section Component for Candidate Dashboard
 */
import React from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Avatar,
  Skeleton,
  Space,
  Statistic,
  Progress,
  Tag,
  Badge,
} from 'antd';
import {
  User,
  MapPin,
  Mail,
  Phone,
  Briefcase,
  Search,
  FileText,
  Clock,
  Calendar,
  Heart,
  TrendingUp,
  Eye,
  Award,
  Target,
  Star,
  CheckCircle,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * WelcomeStatsSection component combining welcome message and stats
 * @param {Object} props
 * @param {Object} props.profile - User profile data
 * @param {Object} props.user - User authentication data
 * @param {Object} props.stats - User statistics data
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element}
 */
const WelcomeStatsSection = ({ profile, user, stats, loading }) => {
  const navigate = useNavigate();
  const displayName = profile?.full_name || user?.email || 'User';
  const firstName = displayName.split(' ')[0];

  // Get current time for greeting
  const currentHour = new Date().getHours();
  const getGreeting = () => {
    if (currentHour < 12) return 'Good Morning';
    if (currentHour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  // Calculate profile completion status
  const profileCompletion = stats?.profileCompletion || 0;
  const getProfileStatus = () => {
    if (profileCompletion >= 90) return { text: 'Excellent', color: '#52c41a' };
    if (profileCompletion >= 70) return { text: 'Good', color: '#1890ff' };
    if (profileCompletion >= 50) return { text: 'Fair', color: '#faad14' };
    return { text: 'Needs Work', color: '#ff4d4f' };
  };

  const profileStatus = getProfileStatus();

  const statCards = [
    {
      title: 'Total Applications',
      value: stats?.totalApplications || 0,
      icon: <FileText size={24} />,
      colorClass: 'text-blue-500',
      bgColorClass: 'bg-blue-50',
      trend: '+2 this week',
      trendUp: true,
    },
    {
      title: 'Pending Reviews',
      value: stats?.pendingApplications || 0,
      icon: <Clock size={24} />,
      colorClass: 'text-yellow-500',
      bgColorClass: 'bg-yellow-50',
      trend: '3 awaiting response',
      trendUp: false,
    },
    {
      title: 'Interviews Scheduled',
      value: stats?.interviewsScheduled || 0,
      icon: <Calendar size={24} />,
      colorClass: 'text-green-500',
      bgColorClass: 'bg-green-50',
      trend: 'Next: Tomorrow',
      trendUp: true,
    },
    {
      title: 'Profile Views',
      value: stats?.profileViews || 0,
      icon: <Eye size={24} />,
      colorClass: 'text-purple-500',
      bgColorClass: 'bg-purple-50',
      trend: '+12 this month',
      trendUp: true,
    },
  ];

  return (
    <div className="mb-8 bg-transparent">
      <Row
        gutter={[24, 24]}
        className="flex items-stretch"
      >
        {/* Welcome Section */}
        <Col
          xs={24}
          lg={16}
          className="flex"
        >
          <Card
            className="relative overflow-hidden p-0 border-0 rounded-2xl shadow-xl w-full h-full"
            styles={{ body: { padding: 0 } }}
          >
            {/* Background Gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800"></div>

            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full translate-y-12 -translate-x-12"></div>

            <div className="relative z-10 p-8 h-full flex flex-col">
              <Skeleton
                loading={loading}
                avatar
                paragraph={{ rows: 4 }}
                active
              >
                {/* Header Section */}
                <div className="flex items-start justify-between mb-6 flex-1">
                  <div className="flex items-center space-x-6">
                    {/* Avatar with Status */}
                    <div className="relative">
                      <Avatar
                        size={100}
                        src={profile?.profile_photo_url}
                        icon={
                          !profile?.profile_photo_url && (
                            <User
                              size={50}
                              className="text-gray-400"
                            />
                          )
                        }
                        className="bg-white/20 border-4 border-white/30 shadow-xl"
                      />
                      <Badge
                        status="success"
                        className="absolute -bottom-1 -right-1 scale-150"
                      />
                    </div>

                    {/* User Info */}
                    <div className="text-white">
                      <div className="flex items-center gap-3 mb-2">
                        <h1 className="text-3xl font-bold">
                          {getGreeting()}, {firstName}! 👋
                        </h1>
                      </div>

                      <div className="space-y-2 text-blue-100">
                        {profile?.role_applied_for && (
                          <div className="flex items-center space-x-2">
                            <Briefcase size={18} />
                            <span className="text-lg font-medium">{profile.role_applied_for}</span>
                            <Tag
                              color="blue"
                              className="ml-2"
                            >
                              {profile?.years_experience || '0'} years exp
                            </Tag>
                          </div>
                        )}

                        <div className="flex items-center space-x-6">
                          {profile?.city && (
                            <div className="flex items-center space-x-2">
                              <MapPin size={16} />
                              <span>{profile.city}</span>
                            </div>
                          )}
                          {user?.email && (
                            <div className="flex items-center space-x-2">
                              <Mail size={16} />
                              <span className="truncate max-w-xs">{user.email}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Profile Status Badge */}
                  <div className="text-right">
                    <Tag
                      color={profileStatus.color}
                      className="mb-2 px-3 py-1 text-sm font-medium"
                    >
                      <Star
                        size={14}
                        className="inline mr-1"
                      />
                      Profile: {profileStatus.text}
                    </Tag>
                    <div className="text-white/80 text-sm">{profileCompletion}% Complete</div>
                  </div>
                </div>

                {/* Profile Completion Progress */}
                <div className="mb-12">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/90 font-medium">Profile Strength</span>
                    <span className="text-white font-bold">{profileCompletion}%</span>
                  </div>
                  <Progress
                    percent={profileCompletion}
                    showInfo={false}
                    strokeColor={{
                      '100%': '#f0f9ff',
                      '0%': '#ffffff',
                    }}
                    trailColor="rgba(255,255,255,0.2)"
                    strokeWidth={8}
                    className="mb-2"
                  />
                  <div className="text-blue-100 text-sm">
                    {profileCompletion < 100 && (
                      <>
                        <CheckCircle
                          size={14}
                          className="inline mr-1"
                        />
                        Complete your profile to get more job matches
                      </>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  <Button
                    type="primary"
                    size="large"
                    icon={<Target size={18} />}
                    className="bg-white text-blue-600 hover:bg-blue-50 border-0 rounded-xl px-6 py-3 h-auto font-semibold shadow-lg"
                    onClick={() => navigate('/candidate/profile')}
                  >
                    Complete Profile
                  </Button>

                  <Button
                    size="large"
                    icon={<Search size={18} />}
                    className="bg-blue-500/20 hover:bg-blue-500/30 border-2 border-white/30 text-white rounded-xl px-6 py-3 h-auto font-semibold backdrop-blur-sm"
                    onClick={() => navigate('/candidate/jobs')}
                  >
                    Browse Jobs
                  </Button>

                  <Button
                    size="large"
                    icon={<Award size={18} />}
                    className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 border-0 text-white rounded-xl px-6 py-3 h-auto font-semibold shadow-lg"
                    onClick={() => navigate('/candidate/assessments')}
                  >
                    Take Assessment
                  </Button>
                </div>
              </Skeleton>
            </div>
          </Card>
        </Col>

        {/* Stats Section */}
        <Col
          xs={24}
          lg={8}
          className="flex"
        >
          <Card
            className="border-0 rounded-2xl shadow-lg w-full h-full"
            styles={{ body: { padding: '20px' } }}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold">Your Activity</h3>
              <TrendingUp
                size={20}
                className="text-green-500"
              />
            </div>

            <Skeleton
              loading={loading}
              paragraph={{ rows: 4 }}
              active
            >
              <div className="space-y-2">
                {statCards.map((stat, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between border border-gray-100 p-3 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`flex items-center justify-center w-10 h-10 rounded-lg ${stat.bgColorClass} ${stat.colorClass}`}
                      >
                        {stat.icon}
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">{stat.title}</div>
                        <div className="text-xs text-gray-500">{stat.trend}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${stat.colorClass}`}>{stat.value}</div>
                      {stat.trendUp && (
                        <div className="flex items-center text-xs text-green-500">
                          <TrendingUp
                            size={12}
                            className="mr-1"
                          />
                          Trending
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Skeleton>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WelcomeStatsSection;
