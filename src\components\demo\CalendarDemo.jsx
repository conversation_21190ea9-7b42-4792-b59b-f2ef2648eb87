/**
 * Calendar Demo Component
 * 
 * Demonstrates the calendar functionality with sample data
 * for testing and development purposes
 */

import React, { useState } from 'react';
import { Card, Typography, Space, Button, Alert, Divider } from 'antd';
import { CalendarOutlined, GoogleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import Calendar from '@/components/shared/Calendar';

const { Title, Text, Paragraph } = Typography;

const CalendarDemo = () => {
  const [demoMode, setDemoMode] = useState('candidate');

  // Sample event types for different roles
  const eventTypes = {
    candidate: [
      { id: 'assessment', name: 'Assessment', color: 'blue' },
      { id: 'application_deadline', name: 'Application Deadline', color: 'red' },
      { id: 'follow_up', name: 'Follow Up', color: 'orange' },
      { id: 'meeting', name: 'Meeting', color: 'green' }
    ],
    company: [
      { id: 'candidate_review', name: 'Candidate Review', color: 'blue' },
      { id: 'team_meeting', name: 'Team Meeting', color: 'green' },
      { id: 'hiring_deadline', name: 'Hiring Deadline', color: 'red' },
      { id: 'onboarding', name: 'Onboarding', color: 'cyan' }
    ],
    interviewer: [
      { id: 'assessment', name: 'Assessment Review', color: 'blue' },
      { id: 'preparation', name: 'Preparation Time', color: 'orange' },
      { id: 'feedback', name: 'Feedback Session', color: 'green' },
      { id: 'training', name: 'Training', color: 'purple' }
    ]
  };

  // Sample participants
  const participants = [
    { name: 'John Doe', email: '<EMAIL>' },
    { name: 'Jane Smith', email: '<EMAIL>' },
    { name: 'Mike Johnson', email: '<EMAIL>' }
  ];

  return (
    <div className="calendar-demo p-6">
      {/* Demo Header */}
      <Card className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <PlayCircleOutlined className="text-2xl text-primary mr-3" />
            <div>
              <Title level={3} className="m-0">
                Calendar Demo
              </Title>
              <Text type="secondary">
                Interactive demonstration of the calendar system with Google integration
              </Text>
            </div>
          </div>
        </div>

        <Divider />

        {/* Role Selector */}
        <div className="mb-4">
          <Text strong>Demo Mode: </Text>
          <Space>
            <Button
              type={demoMode === 'candidate' ? 'primary' : 'default'}
              onClick={() => setDemoMode('candidate')}
            >
              Candidate
            </Button>
            <Button
              type={demoMode === 'company' ? 'primary' : 'default'}
              onClick={() => setDemoMode('company')}
            >
              Company
            </Button>
            <Button
              type={demoMode === 'interviewer' ? 'primary' : 'default'}
              onClick={() => setDemoMode('interviewer')}
            >
              Interviewer
            </Button>
          </Space>
        </div>

        {/* Features Overview */}
        <Alert
          message="Calendar Features"
          description={
            <div className="mt-2">
              <Paragraph className="mb-2">
                This calendar system includes:
              </Paragraph>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><strong>Google Calendar Sync:</strong> Two-way synchronization with Google Calendar</li>
                <li><strong>Google Meet Integration:</strong> Create instant meetings or schedule them with events</li>
                <li><strong>Role-based Event Types:</strong> Different event types for candidates, companies, and interviewers</li>
                <li><strong>Multi-view Support:</strong> Month, day, and agenda views</li>
                <li><strong>Responsive Design:</strong> Works on desktop, tablet, and mobile devices</li>
                <li><strong>Real-time Updates:</strong> Events sync across all devices</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>

      {/* Calendar Component */}
      <Calendar
        userType={demoMode}
        eventTypes={eventTypes[demoMode]}
        participants={participants}
        viewOptions={{
          month: true,
          day: true,
          agenda: true
        }}
      />

      {/* Setup Instructions */}
      <Card className="mt-6" title="Setup Instructions">
        <div className="space-y-4">
          <div>
            <Title level={5}>1. Google API Setup</Title>
            <Paragraph>
              To enable Google Calendar integration, you need to:
            </Paragraph>
            <ul className="list-disc list-inside space-y-1 text-sm ml-4">
              <li>Create a Google Cloud Project</li>
              <li>Enable Google Calendar API and Google People API</li>
              <li>Create OAuth 2.0 credentials</li>
              <li>Add your domain to authorized origins</li>
              <li>Set environment variables in .env file</li>
            </ul>
          </div>

          <div>
            <Title level={5}>2. Environment Variables</Title>
            <Paragraph>
              Add these variables to your .env file:
            </Paragraph>
            <pre className="bg-gray-100 p-3 rounded text-sm">
{`VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_GOOGLE_API_KEY=your_google_api_key`}
            </pre>
          </div>

          <div>
            <Title level={5}>3. Database Setup</Title>
            <Paragraph>
              The calendar_events table has been created in Supabase with proper RLS policies.
              Events are stored locally and can be synced with Google Calendar.
            </Paragraph>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CalendarDemo;
