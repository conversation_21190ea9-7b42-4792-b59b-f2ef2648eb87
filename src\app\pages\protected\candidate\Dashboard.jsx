import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import {
  WelcomeStatsSection,
  QuickActionsSection,
  RecommendedJobsSection,
  RecentJobsSection,
  CompaniesSection,
  CoursesSection,
} from '@/features/candidate/components/dashboard';
import {
  DEMO_JOBS,
  DEMO_COMPANIES,
  DEMO_COURSES,
  DEMO_STATS,
} from '@/features/candidate/constants';

const Dashboard = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    recommendedJobs: [],
    recentJobs: [],
    companies: [],
    courses: [],
    stats: {},
  });

  // Simulate data loading
  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      try {
        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // For demo purposes, use mock data
        // In real implementation, fetch from APIs
        setDashboardData({
          recommendedJobs: DEMO_JOBS.filter((job) => job.match_percentage >= 80),
          recentJobs: DEMO_JOBS.sort((a, b) => new Date(b.posted_at) - new Date(a.posted_at)),
          companies: DEMO_COMPANIES,
          courses: DEMO_COURSES,
          stats: DEMO_STATS,
        });
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        message.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Event handlers
  const handleTakeAssessment = () => {
    message.info('Assessment feature coming soon!');
    // navigate('/candidate/assessments');
  };

  const handleViewJob = (job) => {
    navigate(`/candidate/jobs/${job.id}`);
  };

  const handleApplyJob = (job) => {
    message.success(`Applied to ${job.title} successfully!`);
    // In real implementation, call API to apply for job
  };

  const handleSaveJob = (job) => {
    message.success(`${job.title} saved to your list!`);
    // In real implementation, call API to save job
  };

  const handleViewAllJobs = () => {
    navigate('/candidate/jobs');
  };

  const handleViewCompany = (company) => {
    navigate(`/candidate/companies/${company.id}`);
  };

  const handleViewAllCompanies = () => {
    navigate('/candidate/companies');
  };

  const handleViewCourse = (course) => {
    navigate(`/candidate/courses/${course.id}`);
  };

  const handleViewAllCourses = () => {
    navigate('/candidate/courses');
  };

  return (
    <div className="w-full">
      {/* Welcome and Stats Section */}
      <WelcomeStatsSection
        profile={profile}
        user={user}
        stats={dashboardData.stats}
        loading={loading}
      />

      {/* Quick Actions Section */}
      <QuickActionsSection onTakeAssessment={handleTakeAssessment} />

      {/* Recommended Jobs Section */}
      <RecommendedJobsSection
        jobs={dashboardData.recommendedJobs}
        loading={loading}
        onViewJob={handleViewJob}
        onApplyJob={handleApplyJob}
        onSaveJob={handleSaveJob}
        onViewAllJobs={handleViewAllJobs}
        appliedJobs={[]} // In real implementation, get from store
        savedJobs={[]} // In real implementation, get from store
      />

      {/* Latest Jobs Section */}
      <RecentJobsSection
        jobs={dashboardData.recentJobs}
        loading={loading}
        onViewJob={handleViewJob}
        onApplyJob={handleApplyJob}
        onSaveJob={handleSaveJob}
        onViewAllJobs={handleViewAllJobs}
        appliedJobs={[]} // In real implementation, get from store
        savedJobs={[]} // In real implementation, get from store
      />

      {/* Companies Section */}
      <CompaniesSection
        companies={dashboardData.companies}
        loading={loading}
        onViewCompany={handleViewCompany}
        onViewAllCompanies={handleViewAllCompanies}
      />

      {/* Courses Section */}
      <CoursesSection
        courses={dashboardData.courses}
        loading={loading}
        onViewCourse={handleViewCourse}
        onViewAllCourses={handleViewAllCourses}
      />
    </div>
  );
};

export default Dashboard;
