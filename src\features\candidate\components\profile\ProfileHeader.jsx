/**
 * Profile Header Component
 * Clean, professional profile header
 */
import React from 'react';
import { Card, Avatar, Typography, Button, Tag, Row, Col, Progress, Space } from 'antd';
import {
  EditOutlined,
  MailOutlined,
  PhoneOutlined,
  LinkedinOutlined,
  FileTextOutlined,
  EyeOutlined,
  EnvironmentOutlined,
  StarFilled,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const ProfileHeader = ({
  profile,
  user,
  onEditProfile,
  onPreviewProfile,
  onViewResume,
  ratings = [],
}) => {
  if (!profile) return null;

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getExperienceLevel = (years) => {
    if (years === 0) return { text: 'Fresher', color: 'blue' };
    if (years <= 2) return { text: 'Junior', color: 'green' };
    if (years <= 5) return { text: 'Mid-level', color: 'orange' };
    if (years <= 10) return { text: 'Senior', color: 'purple' };
    return { text: 'Expert', color: 'red' };
  };

  const experienceLevel = getExperienceLevel(profile.years_experience || 0);

  // Simple profile completion calculation
  const getProfileCompletion = () => {
    const fields = [
      profile?.full_name,
      profile?.email,
      profile?.phone_number,
      profile?.role_applied_for,
      profile?.years_experience >= 0,
      profile?.current_job_title,
      profile?.skills?.length > 0,
      profile?.resume_url,
    ];
    const completed = fields.filter(Boolean).length;
    return Math.round((completed / fields.length) * 100);
  };

  const profileCompletion = getProfileCompletion();

  // Get average rating
  const averageRating =
    ratings.length > 0 ? ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length : 0;

  return (
    <Card
      className="shadow-lg rounded-lg overflow-hidden"
      style={{ marginBottom: '1.5rem' }}
    >
      <Row gutter={[24, 24]}>
        {/* Left Section - Profile Info */}
        <Col
          xs={24}
          lg={16}
        >
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
            {/* Avatar */}
            <div className="relative">
              <Avatar
                size={100}
                src={profile.profile_photo_url}
                className="border-4 border-blue-100 shadow-lg"
                style={{ backgroundColor: '#1890ff' }}
              >
                <span className="text-2xl font-bold">{getInitials(profile.full_name)}</span>
              </Avatar>
              {profileCompletion >= 80 && (
                <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1">
                  <StarFilled className="text-white text-xs" />
                </div>
              )}
            </div>

            {/* Profile Details */}
            <div className="flex-1 min-w-0">
              <div className="mb-3">
                <Title
                  level={2}
                  className="mb-2 text-gray-800"
                >
                  {profile.full_name || 'Complete Your Profile'}
                </Title>

                {profile.current_job_title && (
                  <Text className="text-lg text-gray-600 block mb-2">
                    {profile.current_job_title}
                    {profile.current_company && (
                      <span className="text-gray-500"> at {profile.current_company}</span>
                    )}
                  </Text>
                )}

                {/* Tags */}
                <Space
                  wrap
                  className="mb-3"
                >
                  {profile.years_experience >= 0 && (
                    <Tag
                      color={experienceLevel.color}
                      className="px-3 py-1"
                    >
                      {experienceLevel.text} ({profile.years_experience} years)
                    </Tag>
                  )}
                  {profile.role_applied_for && (
                    <Tag
                      color="purple"
                      className="px-3 py-1"
                    >
                      {profile.role_applied_for}
                    </Tag>
                  )}
                </Space>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-gray-600">
                <div className="flex items-center space-x-2">
                  <MailOutlined className="text-blue-500" />
                  <Text className="text-sm">{profile.email || user?.email}</Text>
                </div>
                {profile.phone_number && (
                  <div className="flex items-center space-x-2">
                    <PhoneOutlined className="text-green-500" />
                    <Text className="text-sm">{profile.phone_number}</Text>
                  </div>
                )}
                {profile.city && (
                  <div className="flex items-center space-x-2">
                    <EnvironmentOutlined className="text-red-500" />
                    <Text className="text-sm">{profile.city}</Text>
                  </div>
                )}
                {profile.linkedin_url && (
                  <div className="flex items-center space-x-2">
                    <LinkedinOutlined className="text-blue-600" />
                    <a
                      href={profile.linkedin_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      LinkedIn Profile
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Col>

        {/* Right Section - Actions & Stats */}
        <Col
          xs={24}
          lg={8}
        >
          <div className="space-y-4">
            {/* Action Buttons */}
            <Space
              wrap
              className="w-full"
            >
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={onEditProfile}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Edit Profile
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={onPreviewProfile}
              >
                Preview
              </Button>
              {profile.resume_url && (
                <Button
                  icon={<FileTextOutlined />}
                  onClick={onViewResume}
                >
                  Resume
                </Button>
              )}
            </Space>

            {/* Profile Completion */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <Text
                  strong
                  className="text-sm"
                >
                  Profile Completion
                </Text>
                <Text
                  strong
                  className="text-sm"
                >
                  {profileCompletion}%
                </Text>
              </div>
              <Progress
                percent={profileCompletion}
                showInfo={false}
                strokeColor={{
                  '0%': '#ff4d4f',
                  '50%': '#faad14',
                  '100%': '#52c41a',
                }}
              />
              <Text className="text-xs text-gray-500 mt-1">
                {profileCompletion >= 80
                  ? 'Excellent profile!'
                  : profileCompletion >= 60
                    ? 'Good progress'
                    : 'Complete your profile'}
              </Text>
            </div>

            {/* Rating */}
            {ratings.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <Text
                    strong
                    className="text-sm"
                  >
                    Company Rating
                  </Text>
                  <div className="flex items-center space-x-1">
                    <StarFilled className="text-yellow-500" />
                    <Text strong>{averageRating.toFixed(1)}</Text>
                    <Text className="text-xs text-gray-500">({ratings.length} reviews)</Text>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default ProfileHeader;
