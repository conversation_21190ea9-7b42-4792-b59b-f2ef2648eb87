/**
 * Certifications Card Component
 * Shows professional certifications and achievements
 */
import React from 'react';
import { Card, Alert, Button } from 'antd';
import { TrophyOutlined } from '@ant-design/icons';
import { Award } from 'lucide-react';

const CertificationsCard = ({ profile, onEditProfile }) => {
  return (
    <Card
      title={
        <span className="flex items-center">
          <Award size={18} className="mr-2 text-yellow-500" />
          Certifications
        </span>
      }
      className="shadow-sm rounded-lg"
    >
      {profile?.certifications && Array.isArray(profile.certifications) && profile.certifications.length > 0 ? (
        <div className="space-y-4">
          {profile.certifications.map((cert, index) => (
            <div key={index} className="border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-start space-x-3">
                <TrophyOutlined className="text-yellow-500 mt-1" style={{ fontSize: 18 }} />
                <div className="flex-1">
                  <div className="font-semibold text-gray-800">{cert.name}</div>
                  <div className="text-gray-600 text-sm mt-1">{cert.issuer}</div>
                  <div className="text-gray-500 text-xs mt-2">
                    Issued: {cert.issue_date}
                    {cert.expiry_date && ` • Expires: ${cert.expiry_date}`}
                  </div>
                  {cert.credential_id && (
                    <div className="text-xs text-gray-400 mt-1">
                      ID: {cert.credential_id}
                    </div>
                  )}
                  {cert.credential_url && (
                    <div className="mt-2">
                      <a 
                        href={cert.credential_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-xs"
                      >
                        View Certificate →
                      </a>
                    </div>
                  )}
                  {cert.skills && Array.isArray(cert.skills) && cert.skills.length > 0 && (
                    <div className="mt-2">
                      <div className="text-xs text-gray-600 mb-1">Skills:</div>
                      <div className="flex flex-wrap gap-1">
                        {cert.skills.map((skill, i) => (
                          <span key={i} className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Alert
          message="Add certifications"
          description="Showcase your professional certifications and achievements."
          type="info"
          showIcon
          action={
            <Button size="small" type="primary" onClick={onEditProfile}>
              Add Certifications
            </Button>
          }
        />
      )}
    </Card>
  );
};

export default CertificationsCard;
