import React from 'react';
import { <PERSON>po<PERSON>, <PERSON>, <PERSON>, Card, Divider, Avatar, Space, Button } from 'antd';
import { TeamOutlined, TrophyOutlined, HistoryOutlined, AimOutlined } from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Paragraph } = Typography;

const About = () => {
  const { isMobile, isTablet } = useDeviceDetect();

  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'CEO & Founder',
      bio: 'Former HR executive with 15+ years of experience in talent acquisition.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      bio: 'Tech leader with background in building scalable platforms.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Head of Product',
      bio: 'Product strategist focused on user-centered design and innovation.',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Head of Customer Success',
      bio: 'Dedicated to ensuring clients achieve their hiring goals.',
      avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
    },
  ];

  const values = [
    {
      title: 'Innovation',
      description: 'We constantly push boundaries to improve the interview experience.',
      icon: <AimOutlined className="text-primary text-2xl" />,
    },
    {
      title: 'Transparency',
      description: 'We believe in open communication with our users and partners.',
      icon: <TeamOutlined className="text-primary text-2xl" />,
    },
    {
      title: 'Excellence',
      description: 'We strive for the highest quality in everything we do.',
      icon: <TrophyOutlined className="text-primary text-2xl" />,
    },
  ];

  return (
    <div className="about-page max-w-5xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-12 mb-12">
        <Title
          level={1}
          className="mb-4"
        >
          About Us
        </Title>
        <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
          We're on a mission to transform the interview process, making it more efficient,
          effective, and equitable for everyone involved.
        </Paragraph>
      </div>

      {/* Our Story Section */}
      <Card className="mb-12">
        <Row
          gutter={[32, 32]}
          align="middle"
        >
          <Col
            xs={24}
            md={12}
          >
            <div className="story-content">
              <div className="inline-block p-3 bg-secondary rounded-full mb-4">
                <HistoryOutlined className="text-primary text-2xl" />
              </div>
              <Title
                level={2}
                className="mb-4"
              >
                Our Story
              </Title>
              <Paragraph className="text-muted mb-4">
                Founded in 2020, our platform was born from a simple observation: the interview
                process was broken for both candidates and companies.
              </Paragraph>
              <Paragraph className="text-muted mb-4">
                After experiencing the frustrations of traditional hiring processes firsthand, our
                founder set out to create a solution that would streamline interviews while making
                them more effective and fair.
              </Paragraph>
              <Paragraph className="text-muted">
                Today, we've helped thousands of companies and candidates connect through better
                interviews, saving time and reducing bias in the hiring process.
              </Paragraph>
            </div>
          </Col>
          <Col
            xs={24}
            md={12}
          >
            <img
              src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
              alt="Team collaboration"
              className="w-full h-auto rounded-lg shadow-md"
            />
          </Col>
        </Row>
      </Card>

      {/* Our Values Section */}
      <div className="values-section mb-12">
        <div className="text-center mb-8">
          <Title
            level={2}
            className="relative inline-block"
          >
            Our Values
            <div className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-70"></div>
          </Title>
        </div>

        <Row gutter={[24, 24]}>
          {values.map((value, index) => (
            <Col
              xs={24}
              md={8}
              key={index}
            >
              <Card className="value-card text-center h-full">
                <div className="inline-block p-3 bg-secondary rounded-full mb-4">{value.icon}</div>
                <Title level={4}>{value.title}</Title>
                <Paragraph className="text-muted">{value.description}</Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Team Section */}
      <div className="team-section mb-12">
        <div className="text-center mb-8">
          <Title
            level={2}
            className="relative inline-block"
          >
            Our Team
            <div className="absolute bottom-0 left-0 w-full h-1 bg-primary opacity-70"></div>
          </Title>
          <Paragraph className="text-lg max-w-2xl mx-auto text-muted mt-4">
            Meet the passionate people behind our platform.
          </Paragraph>
        </div>

        <Row gutter={[24, 24]}>
          {teamMembers.map((member, index) => (
            <Col
              xs={24}
              sm={12}
              md={6}
              key={index}
            >
              <Card className="team-card text-center h-full hover:shadow-md transition-shadow">
                <Avatar
                  src={member.avatar}
                  size={96}
                  className="mb-4"
                />
                <Title
                  level={5}
                  className="mb-1"
                >
                  {member.name}
                </Title>
                <Paragraph className="text-primary font-medium mb-2">{member.role}</Paragraph>
                <Paragraph className="text-muted text-sm">{member.bio}</Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Stats Section */}
      <Card
        className="stats-card mb-12"
        style={{ background: 'var(--primary)', color: 'white' }}
      >
        <Row
          gutter={[32, 32]}
          className="text-center py-6"
        >
          <Col
            xs={24}
            sm={8}
          >
            <Title
              level={2}
              style={{ color: 'white', margin: 0 }}
            >
              500+
            </Title>
            <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)' }}>Companies</Paragraph>
          </Col>
          <Col
            xs={24}
            sm={8}
          >
            <Title
              level={2}
              style={{ color: 'white', margin: 0 }}
            >
              10,000+
            </Title>
            <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)' }}>Candidates</Paragraph>
          </Col>
          <Col
            xs={24}
            sm={8}
          >
            <Title
              level={2}
              style={{ color: 'white', margin: 0 }}
            >
              25,000+
            </Title>
            <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              Interviews Conducted
            </Paragraph>
          </Col>
        </Row>
      </Card>

      {/* CTA Section */}
      <div className="cta-section text-center mb-12 mt-12">
        <Title
          level={3}
          className="mb-4"
        >
          Ready to transform your interview experience?
        </Title>
        <Paragraph className="text-muted mb-6 max-w-2xl mx-auto">
          Join thousands of companies and candidates already using our platform to make interviews
          better.
        </Paragraph>
        <Space size="large">
          <Button
            href="/register"
            type="primary"
            size="large"
          >
            Get Started
          </Button>
          <Button
            href="/contact"
            size="large"
          >
            Contact Us
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default About;
