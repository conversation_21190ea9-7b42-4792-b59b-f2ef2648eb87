import React from 'react';
import { <PERSON><PERSON>, Tooltip, Switch } from 'antd';
import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { useColorModeStore } from '@/store/colorMode.store';

const ColorModeToggle = ({ type = 'icon', size = 'large', showTooltip = true, className = '' }) => {
  const { colorMode, toggleColorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Theme class is applied in App.jsx to avoid conflicts

  if (type === 'switch') {
    return (
      <div className={`flex items-center ${className}`}>
        <SunOutlined className="mr-2 text-yellow-500" />
        <Switch
          checked={isDark}
          onChange={toggleColorMode}
          size={size === 'small' ? 'small' : 'default'}
          className="bg-gray-300 dark:bg-gray-700"
        />
        <MoonOutlined className="ml-2 text-blue-400" />
      </div>
    );
  }

  const button = (
    <Button
      type="text"
      icon={isDark ? <SunOutlined /> : <MoonOutlined />}
      onClick={toggleColorMode}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      style={{
        color: 'var(--text-primary)',
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
      }}
      size={size}
      className={`hover:shadow-md ${className}`}
    />
  );

  if (showTooltip) {
    return (
      <Tooltip title={isDark ? 'Switch to light mode' : 'Switch to dark mode'}>{button}</Tooltip>
    );
  }

  return button;
};

export default ColorModeToggle;
