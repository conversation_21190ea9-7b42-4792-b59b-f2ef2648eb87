/**
 * Quick Actions Section Component for Candidate Dashboard
 */
import React from 'react';
import { Card, Row, Col, Button, Typography, Space } from 'antd';
import {
  Search,
  FileText,
  User,
  Calendar,
  BookOpen,
  Target,
  MessageSquare,
  Settings,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;

/**
 * QuickActionsSection component for displaying quick action buttons
 * @param {Object} props
 * @param {Function} props.onTakeAssessment - Handler for take assessment action
 * @returns {JSX.Element}
 */
const QuickActionsSection = ({ onTakeAssessment }) => {
  const navigate = useNavigate();

  const quickActions = [
    {
      key: 'browse-jobs',
      title: 'Browse Jobs',
      description: 'Find your next opportunity',
      icon: <Search size={24} />,
      color: '#1890ff',
      onClick: () => navigate('/candidate/jobs'),
    },
    {
      key: 'take-assessment',
      title: 'Take Assessment',
      description: 'Test your skills',
      icon: <Target size={24} />,
      color: '#52c41a',
      onClick: onTakeAssessment,
    },
    {
      key: 'complete-profile',
      title: 'Complete Profile',
      description: 'Boost your visibility',
      icon: <User size={24} />,
      color: '#faad14',
      onClick: () => navigate('/candidate/profile'),
    },
    {
      key: 'schedule-interview',
      title: 'Schedule Interview',
      description: 'Book your interviews',
      icon: <Calendar size={24} />,
      color: '#eb2f96',
      onClick: () => navigate('/candidate/interviews'),
    },
    {
      key: 'view-applications',
      title: 'My Applications',
      description: 'Track your progress',
      icon: <FileText size={24} />,
      color: '#722ed1',
      onClick: () => navigate('/candidate/applications'),
    },
    {
      key: 'courses',
      title: 'Skill Courses',
      description: 'Enhance your skills',
      icon: <BookOpen size={24} />,
      color: '#13c2c2',
      onClick: () => navigate('/candidate/courses'),
    },
    {
      key: 'messages',
      title: 'Messages',
      description: 'Chat with employers',
      icon: <MessageSquare size={24} />,
      color: '#f5222d',
      onClick: () => navigate('/candidate/messages'),
    },
    {
      key: 'settings',
      title: 'Settings',
      description: 'Manage preferences',
      icon: <Settings size={24} />,
      color: '#595959',
      onClick: () => navigate('/candidate/settings'),
    },
  ];

  return (
    <Card
      title={
        <Title
          level={4}
          className="mb-0"
        >
          Quick Actions
        </Title>
      }
      style={{ marginBottom: '1.5rem' }}
    >
      <Row gutter={[16, 16]}>
        {quickActions.map((action) => (
          <Col
            xs={12}
            sm={8}
            md={6}
            lg={6}
            xl={3}
            key={action.key}
          >
            <Card
              hoverable
              className="text-center h-full transition-all duration-200 hover:shadow-lg"
              styles={{ body: { padding: '20px 16px' } }}
              onClick={action.onClick}
            >
              <Space
                direction="vertical"
                size="small"
                className="w-full"
              >
                <div
                  className="flex items-center justify-center w-12 h-12 rounded-full mx-auto mb-2"
                  style={{ backgroundColor: `${action.color}15`, color: action.color }}
                >
                  {action.icon}
                </div>
                <div>
                  <div className="font-medium text-sm mb-1">{action.title}</div>
                  <div className="text-xs text-gray-500">{action.description}</div>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );
};

export default QuickActionsSection;
