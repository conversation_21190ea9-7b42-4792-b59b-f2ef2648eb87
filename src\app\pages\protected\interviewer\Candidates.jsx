/**
 * Interviewer Candidates Page
 *
 * Modern UI for interviewers to browse and manage candidates with:
 * - Grid layout with candidate cards
 * - Search and filter functionality
 * - Send interview request actions
 * - Candidate details and experience
 * - Professional card design
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Space,
  Button,
  Row,
  Col,
  Input,
  Select,
  Tag,
  Avatar,
  Pagination,
  Skeleton,
  Empty,
  Badge,
  Tooltip,
  message,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  EnvironmentOutlined,
  DollarOutlined,
  CalendarOutlined,
  SendOutlined,
  EyeOutlined,
  StarOutlined,
  BookOutlined,
} from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';
// Candidate service removed - candidates functionality not available
import { createInterviewRequest } from '@/services/interviewRequest.service';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const InterviewerCandidates = () => {
  const { user, loading: authLoading, isAuthenticated } = useAuth();

  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    experience: '',
    location: '',
    skills: '',
    availability: '',
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const [total, setTotal] = useState(0);

  const loadCandidates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Candidate functionality has been removed
      setCandidates([]);
      setTotal(0);
      setError('Candidate functionality is not available in this version.');
    } catch (error) {
      console.error('Error loading candidates:', error);
      setError(error.message || 'Failed to load candidates');
      message.error('Failed to load candidates');
      setCandidates([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, filters, pageSize]);

  // Load candidates on component mount and when auth state changes
  useEffect(() => {
    if (user && isAuthenticated && !authLoading) {
      loadCandidates();
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false);
      setError('Authentication required');
    }
  }, [user, isAuthenticated, authLoading, loadCandidates]);

  // Handle send interview request
  const handleSendRequest = async (candidateId, jobTitle = 'General Interview') => {
    try {
      const requestData = {
        candidate_id: candidateId,
        interviewer_id: user.id,
        job_title: jobTitle,
        status: 'pending',
        message: 'Interview request from interviewer',
      };

      const { success, error } = await createInterviewRequest(requestData);

      if (success) {
        message.success('Interview request sent successfully!');
      } else {
        message.error(error || 'Failed to send request');
      }
    } catch (error) {
      console.error('Error sending request:', error);
      message.error('Failed to send request');
    }
  };

  // Filter candidates based on search and filters
  const filteredCandidates = candidates.filter((candidate) => {
    const matchesSearch =
      !searchTerm ||
      candidate.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.skills?.some((skill) => skill.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesSearch;
  });

  // Experience levels for filtering
  const experienceLevels = [
    { value: '', label: 'All Experience' },
    { value: '0-2', label: '0-2 years' },
    { value: '3-5', label: '3-5 years' },
    { value: '6-10', label: '6-10 years' },
    { value: '10+', label: '10+ years' },
  ];

  // Mock candidate data structure for demonstration
  const mockCandidates = [
    {
      id: 1,
      name: 'Rohit Kumar',
      title: 'Senior Sales Manager',
      experience: '5 years 5 Month',
      location: 'Bangalore',
      expectedSalary: '₹47K - ₹67K',
      skills: ['Sales', 'Management', 'CRM'],
      avatar: null,
      rating: 4.5,
      availability: 'Available',
    },
    {
      id: 2,
      name: 'Rajesh Kumar',
      title: 'Senior Sales Manager',
      experience: '3 years 5 Month',
      location: 'Bangalore',
      expectedSalary: '₹47K - ₹67K',
      skills: ['Sales', 'Management', 'CRM'],
      avatar: null,
      rating: 4.2,
      availability: 'Available',
    },
    {
      id: 3,
      name: 'Rajesh Kumar',
      title: 'Senior Sales Manager',
      experience: '2 years 5 Month',
      location: 'Bangalore',
      expectedSalary: '₹47K - ₹67K',
      skills: ['Sales', 'Management', 'CRM'],
      avatar: null,
      rating: 4.0,
      availability: 'Available',
    },
    {
      id: 4,
      name: 'Rajesh Kumar',
      title: 'Senior Sales Manager',
      experience: '3 years 5 Month',
      location: 'Bangalore',
      expectedSalary: '₹47K - ₹67K',
      skills: ['Sales', 'Management', 'CRM'],
      avatar: null,
      rating: 4.3,
      availability: 'Available',
    },
  ];

  // Use mock data if no real candidates
  const displayCandidates = candidates.length > 0 ? filteredCandidates : mockCandidates;

  return (
    <div className="interviewer-candidates-page p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Title
              level={2}
              className="m-0"
            >
              Candidates
            </Title>
            <Text type="secondary">Browse and connect with talented candidates</Text>
          </div>
          <Space>
            <Button
              icon={<FilterOutlined />}
              size="large"
            >
              Advanced Filters
            </Button>
          </Space>
        </div>
      </div>

      {/* Search and Filters */}
      <Card
        className="mb-6"
        size="small"
      >
        <Row
          gutter={16}
          align="middle"
        >
          <Col
            xs={24}
            sm={12}
            md={8}
          >
            <Search
              placeholder="Search candidates..."
              allowClear
              size="large"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onSearch={loadCandidates}
            />
          </Col>
          <Col
            xs={24}
            sm={6}
            md={4}
          >
            <Select
              placeholder="Experience"
              size="large"
              style={{ width: '100%' }}
              value={filters.experience}
              onChange={(value) => setFilters((prev) => ({ ...prev, experience: value }))}
            >
              {experienceLevels.map((level) => (
                <Option
                  key={level.value}
                  value={level.value}
                >
                  {level.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col
            xs={24}
            sm={6}
            md={4}
          >
            <Select
              placeholder="Location"
              size="large"
              style={{ width: '100%' }}
              value={filters.location}
              onChange={(value) => setFilters((prev) => ({ ...prev, location: value }))}
            >
              <Option value="">All Locations</Option>
              <Option value="bangalore">Bangalore</Option>
              <Option value="mumbai">Mumbai</Option>
              <Option value="delhi">Delhi</Option>
              <Option value="pune">Pune</Option>
            </Select>
          </Col>
          <Col
            xs={24}
            sm={6}
            md={4}
          >
            <Select
              placeholder="Availability"
              size="large"
              style={{ width: '100%' }}
              value={filters.availability}
              onChange={(value) => setFilters((prev) => ({ ...prev, availability: value }))}
            >
              <Option value="">All</Option>
              <Option value="available">Available</Option>
              <Option value="busy">Busy</Option>
              <Option value="interviewing">Interviewing</Option>
            </Select>
          </Col>
          <Col
            xs={24}
            sm={6}
            md={4}
          >
            <Text type="secondary">{total || displayCandidates.length} candidates found</Text>
          </Col>
        </Row>
      </Card>

      {/* Candidates Grid */}
      {authLoading || loading ? (
        <Row gutter={[16, 16]}>
          {Array.from({ length: 8 }).map((_, index) => (
            <Col
              key={index}
              xs={24}
              sm={12}
              md={8}
              lg={6}
            >
              <Card>
                <Skeleton
                  avatar
                  active
                  paragraph={{ rows: 4 }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      ) : error ? (
        <Card>
          <Empty
            description={error}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button
              type="primary"
              onClick={loadCandidates}
              loading={loading}
            >
              Retry
            </Button>
          </Empty>
        </Card>
      ) : displayCandidates.length === 0 ? (
        <Empty
          description="No candidates found"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <>
          <Row gutter={[16, 16]}>
            {displayCandidates.map((candidate) => (
              <Col
                key={candidate.id}
                xs={24}
                sm={12}
                md={8}
                lg={6}
              >
                <Card
                  className="candidate-card hover:shadow-lg transition-shadow duration-300"
                  actions={[
                    <Tooltip
                      title="View Details"
                      key="view"
                    >
                      <Button
                        type="text"
                        icon={<EyeOutlined />}
                        size="small"
                      />
                    </Tooltip>,
                    <Button
                      key="request"
                      type="primary"
                      icon={<SendOutlined />}
                      size="small"
                      onClick={() => handleSendRequest(candidate.id, candidate.title)}
                    >
                      Send Request
                    </Button>,
                  ]}
                >
                  <div className="text-center mb-4">
                    <Avatar
                      size={64}
                      src={candidate.avatar}
                      icon={<UserOutlined />}
                      className="mb-2"
                    />
                    <div>
                      <Title
                        level={5}
                        className="m-0"
                      >
                        {candidate.name}
                      </Title>
                      <Text
                        type="secondary"
                        className="text-sm"
                      >
                        {candidate.title}
                      </Text>
                    </div>
                  </div>

                  <Space
                    direction="vertical"
                    size="small"
                    style={{ width: '100%' }}
                  >
                    <div className="flex items-center justify-between">
                      <Text className="text-xs">
                        <BookOutlined className="mr-1" />
                        Experience
                      </Text>
                      <Text className="text-xs font-medium">{candidate.experience}</Text>
                    </div>

                    <div className="flex items-center justify-between">
                      <Text className="text-xs">
                        <EnvironmentOutlined className="mr-1" />
                        Location
                      </Text>
                      <Text className="text-xs font-medium">{candidate.location}</Text>
                    </div>

                    <div className="flex items-center justify-between">
                      <Text className="text-xs">
                        <DollarOutlined className="mr-1" />
                        Exp Salary
                      </Text>
                      <Text className="text-xs font-medium">{candidate.expectedSalary}</Text>
                    </div>

                    {candidate.rating && (
                      <div className="flex items-center justify-between">
                        <Text className="text-xs">
                          <StarOutlined className="mr-1" />
                          Rating
                        </Text>
                        <div className="flex items-center">
                          <Text className="text-xs font-medium mr-1">{candidate.rating}</Text>
                          <StarOutlined className="text-yellow-500 text-xs" />
                        </div>
                      </div>
                    )}
                  </Space>

                  {candidate.skills && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-1">
                        {candidate.skills.slice(0, 3).map((skill, index) => (
                          <Tag
                            key={index}
                            size="small"
                            color="blue"
                          >
                            {skill}
                          </Tag>
                        ))}
                        {candidate.skills.length > 3 && (
                          <Tag
                            size="small"
                            color="default"
                          >
                            +{candidate.skills.length - 3}
                          </Tag>
                        )}
                      </div>
                    </div>
                  )}

                  {candidate.availability && (
                    <div className="mt-2">
                      <Badge
                        status={candidate.availability === 'Available' ? 'success' : 'warning'}
                        text={<Text className="text-xs">{candidate.availability}</Text>}
                      />
                    </div>
                  )}
                </Card>
              </Col>
            ))}
          </Row>

          {/* Pagination */}
          <div className="flex justify-center mt-8">
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={total || displayCandidates.length}
              onChange={setCurrentPage}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} candidates`}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default InterviewerCandidates;
