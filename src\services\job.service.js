/**
 * Job Service
 *
 * This service handles all API calls related to jobs.
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Get a job by ID
 * @param {string} id - Job ID
 * @returns {Promise<Object>} - Job data
 */
export const getJob = async (id) => {
  try {
    const { data, error } = await supabase
      .from('jobs')
      .select(
        `
        *,
        companies:company_id (
          id,
          company_name,
          company_logo_url,
          company_type,
          company_size
        )
      `
      )
      .eq('id', id)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching job:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a new job posting
 * @param {Object} jobData - Job data
 * @returns {Promise<Object>} - Result of the operation
 */
export const createJob = async (jobData) => {
  try {
    const { data, error } = await supabase.from('jobs').insert(jobData).select();

    if (error) throw error;
    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error creating job:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update a job posting
 * @param {string} id - Job ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object>} - Result of the operation
 */
export const updateJob = async (id, updates) => {
  try {
    const { data, error } = await supabase.from('jobs').update(updates).eq('id', id).select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating job:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete a job posting
 * @param {string} id - Job ID
 * @returns {Promise<Object>} - Result of the operation
 */
export const deleteJob = async (id) => {
  try {
    const { error } = await supabase.from('jobs').delete().eq('id', id);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error deleting job:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all active jobs
 * @param {Object} filters - Optional filters
 * @returns {Promise<Object>} - Active jobs
 */
export const getActiveJobs = async (filters = {}) => {
  try {
    let query = supabase
      .from('jobs')
      .select(
        `
        *,
        companies:company_id (
          id,
          company_name,
          company_logo_url,
          company_type,
          company_size
        )
      `
      )
      .eq('status', 'active');

    // Apply filters if provided
    if (filters.location) {
      query = query.ilike('location', `%${filters.location}%`);
    }

    if (filters.experienceLevel) {
      query = query.eq('experience_level', filters.experienceLevel);
    }

    if (filters.title) {
      query = query.ilike('title', `%${filters.title}%`);
    }

    if (filters.companyType) {
      query = query.eq('companies.company_type', filters.companyType);
    }

    const { data, error } = await query;

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching active jobs:', error);
    return { success: false, error: error.message };
  }
};

// Note: Job application functionality moved to useJobApplication hook for better state management

/**
 * Get applications for a job
 * @param {string} jobId - Job ID
 * @returns {Promise<Object>} - Job applications
 */
export const getJobApplications = async (jobId) => {
  try {
    const { data, error } = await supabase
      .from('applications')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          mobile_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url,
          skills,
          expected_ctc,
          notice_period
        )
      `
      )
      .eq('job_id', jobId);

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching job applications:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update application status
 * @param {string} applicationId - Application ID
 * @param {string} status - New status
 * @returns {Promise<Object>} - Result of the operation
 */
export const updateApplicationStatus = async (applicationId, status) => {
  try {
    const { data, error } = await supabase
      .from('applications')
      .update({ status })
      .eq('id', applicationId)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating application status:', error);
    return { success: false, error: error.message };
  }
};
