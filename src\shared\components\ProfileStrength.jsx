/**
 * Profile Strength Component
 * Reusable component for displaying profile completion strength
 */
import React from 'react';
import { Card, Progress, Typography, Row, Col, Tag } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Target, TrendingUp } from 'lucide-react';

const { Title, Text } = Typography;

/**
 * Calculate profile strength based on completed fields
 */
const calculateProfileStrength = (profile) => {
  if (!profile) return { score: 0, breakdown: { critical: 0, professional: 0, enhancement: 0 } };

  const weights = {
    critical: 6,      // 30% weight (5 fields × 6 points = 30)
    professional: 10, // 40% weight (4 fields × 10 points = 40) 
    enhancement: 6    // 30% weight (5 fields × 6 points = 30)
  };

  // Critical Fields (30% - Essential for any application)
  const criticalFields = [
    { field: 'full_name', weight: weights.critical },
    { field: 'email', weight: weights.critical },
    { field: 'phone_number', weight: weights.critical },
    { field: 'role_applied_for', weight: weights.critical },
    { field: 'years_experience', weight: weights.critical, condition: (val) => val >= 0 }
  ];

  // Professional Fields (40% - Core professional information)
  const professionalFields = [
    { field: 'current_job_title', weight: weights.professional },
    { field: 'current_company', weight: weights.professional },
    { field: 'skills', weight: weights.professional, condition: (val) => Array.isArray(val) && val.length > 0 },
    { field: 'resume_url', weight: weights.professional }
  ];

  // Enhancement Fields (30% - Profile enhancement and additional info)
  const enhancementFields = [
    { field: 'profile_photo_url', weight: weights.enhancement },
    { field: 'linkedin_url', weight: weights.enhancement },
    { field: 'languages', weight: weights.enhancement, condition: (val) => Array.isArray(val) && val.length > 0 },
    { field: 'education', weight: weights.enhancement, condition: (val) => Array.isArray(val) && val.length > 0 },
    { field: 'certifications', weight: weights.enhancement, condition: (val) => Array.isArray(val) && val.length > 0 }
  ];

  // Calculate scores for each category
  const calculateCategoryScore = (fields) => {
    return fields.reduce((categoryScore, { field, weight, condition }) => {
      const value = profile[field];
      let isValid = false;

      if (condition) {
        isValid = condition(value);
      } else {
        isValid = value !== null && value !== undefined && value !== '';
      }

      return categoryScore + (isValid ? weight : 0);
    }, 0);
  };

  const criticalScore = calculateCategoryScore(criticalFields);
  const professionalScore = calculateCategoryScore(professionalFields);
  const enhancementScore = calculateCategoryScore(enhancementFields);

  const totalScore = criticalScore + professionalScore + enhancementScore;

  return {
    score: Math.min(100, Math.round(totalScore)),
    breakdown: {
      critical: criticalScore,
      professional: professionalScore,
      enhancement: enhancementScore
    }
  };
};

const ProfileStrength = ({ profile, showBreakdown = true, size = 'default' }) => {
  const { score: profileStrength, breakdown } = calculateProfileStrength(profile);

  // Calculate completion counts for display
  const criticalCount = [
    profile?.full_name,
    profile?.email,
    profile?.phone_number,
    profile?.role_applied_for,
    profile?.years_experience >= 0,
  ].filter(Boolean).length;

  const professionalCount = [
    profile?.current_job_title,
    profile?.current_company,
    profile?.skills?.length > 0,
    profile?.resume_url,
  ].filter(Boolean).length;

  const enhancementCount = [
    profile?.profile_photo_url,
    profile?.linkedin_url,
    profile?.languages?.length > 0,
    profile?.education?.length > 0,
    profile?.certifications?.length > 0,
  ].filter(Boolean).length;

  const getStrengthColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  const getStrengthStatus = (score) => {
    if (score >= 80) return { text: 'Excellent', icon: <CheckCircleOutlined />, color: 'success' };
    if (score >= 60) return { text: 'Good', icon: <ExclamationCircleOutlined />, color: 'warning' };
    return { text: 'Needs Improvement', icon: <ExclamationCircleOutlined />, color: 'error' };
  };

  const status = getStrengthStatus(profileStrength);

  if (size === 'compact') {
    return (
      <div className="flex items-center space-x-3">
        <Progress
          type="circle"
          percent={profileStrength}
          size={60}
          strokeColor={getStrengthColor(profileStrength)}
          format={() => `${profileStrength}%`}
        />
        <div>
          <Text strong className="text-sm">Profile Strength</Text>
          <div>
            <Tag color={status.color} className="text-xs">
              {status.icon} {status.text}
            </Tag>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card
      title={
        <span className="flex items-center">
          <Target size={18} className="mr-2 text-blue-500" />
          Profile Strength
        </span>
      }
      className="shadow-sm rounded-lg"
    >
      <Row gutter={[24, 16]}>
        <Col xs={24} md={8}>
          <div className="text-center">
            <Progress
              type="circle"
              percent={profileStrength}
              size={120}
              strokeColor={{
                '0%': '#ff4d4f',
                '50%': '#faad14', 
                '100%': '#52c41a'
              }}
              format={() => (
                <div>
                  <div className="text-2xl font-bold">{profileStrength}%</div>
                  <div className="text-xs text-gray-500">Complete</div>
                </div>
              )}
            />
            <div className="mt-3">
              <Tag color={status.color} className="px-3 py-1">
                {status.icon} {status.text}
              </Tag>
            </div>
          </div>
        </Col>
        
        {showBreakdown && (
          <Col xs={24} md={16}>
            <div className="space-y-4">
              <Title level={5} className="mb-3 flex items-center">
                <TrendingUp size={16} className="mr-2" />
                Completion Breakdown
              </Title>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                  <span className="font-medium text-gray-700">Critical Information</span>
                  <span className="font-bold text-red-600">{criticalCount}/5</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                  <span className="font-medium text-gray-700">Professional Details</span>
                  <span className="font-bold text-blue-600">{professionalCount}/4</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                  <span className="font-medium text-gray-700">Profile Enhancement</span>
                  <span className="font-bold text-green-600">{enhancementCount}/5</span>
                </div>
              </div>
            </div>
          </Col>
        )}
      </Row>
    </Card>
  );
};

export default ProfileStrength;
export { calculateProfileStrength };
