import React from 'react';
import { Card, Typography, Tag, Button, Space, Tooltip, Progress, Badge } from 'antd';
import {
  EnvironmentOutlined,
  ClockCircleOutlined,
  BookOutlined,
  HeartOutlined,
  HeartFilled,
  CheckCircleOutlined,
  EyeOutlined,
} from '@ant-design/icons';

import {
  formatSalaryRange,
  formatJobDate,
  getMatchColor,
  truncateText,
} from '@/features/candidate/utils/jobUtils';

// Rupee Icon Component
const RupeeIcon = () => <span style={{ fontWeight: 'bold', fontSize: '14px' }}>₹</span>;

const { Title, Text } = Typography;

const JobCard = ({
  job,
  isApplied = false,
  isSaved = false,
  onApply,
  onSave,
  onClick,
  showActions = true,
  viewMode = 'grid',
  showMatchPercentage = false,
  loading = false,
}) => {
  const company = job.companies || job.company_profiles;
  const matchPercentage = job.matchPercentage || 0;

  const handleApply = (e) => {
    e.stopPropagation();
    onApply?.(job);
  };

  const handleSave = (e) => {
    e.stopPropagation();
    onSave?.(job);
  };

  const handleCardClick = () => {
    onClick?.(job);
  };

  return (
    <Card
      hoverable
      className="cursor-pointer"
      onClick={handleCardClick}
      extra={
        showActions && (
          <Space>
            {showMatchPercentage && matchPercentage > 0 && (
              <Progress
                type="circle"
                size={32}
                percent={matchPercentage}
                strokeColor={getMatchColor(matchPercentage)}
                format={(percent) => `${percent}%`}
                strokeWidth={8}
              />
            )}
            <Tooltip title={isSaved ? 'Remove from saved' : 'Save job'}>
              <Button
                type="text"
                icon={isSaved ? <HeartFilled style={{ color: '#ef4444' }} /> : <HeartOutlined />}
                onClick={handleSave}
                loading={loading}
              />
            </Tooltip>
          </Space>
        )
      }
    >
      <div className={`flex ${viewMode === 'list' ? 'flex-row gap-6' : 'flex-col'}`}>
        {/* Job Header */}
        <div className={`${viewMode === 'list' ? 'flex-1' : 'w-full'}`}>
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <div className="flex items-start gap-3">
                {company?.company_logo_url && (
                  <img
                    src={company.company_logo_url}
                    alt={`${company.company_name} logo`}
                    className="w-12 h-12 object-contain rounded-lg border"
                  />
                )}
                <div className="flex-1">
                  <Title
                    level={4}
                    className="mb-1"
                  >
                    {job.title}
                  </Title>
                  <Text className="font-medium">{company?.company_name || job.company_name}</Text>
                  {isApplied && (
                    <Badge
                      status="success"
                      text="Applied"
                      className="mt-1 text-xs"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Job Tags */}
          <div className="mb-4">
            <Space
              wrap
              size="small"
            >
              <Tag
                icon={<EnvironmentOutlined />}
                color="blue"
              >
                {job.location}
              </Tag>
              {job.salary_range && (
                <Tag
                  icon={<RupeeIcon />}
                  color="green"
                >
                  {typeof job.salary_range === 'string'
                    ? job.salary_range
                    : formatSalaryRange(job.salary_range)}
                </Tag>
              )}
              <Tag
                icon={<ClockCircleOutlined />}
                color="orange"
              >
                {job.experience_level}
              </Tag>
              {job.employment_type && (
                <Tag
                  icon={<BookOutlined />}
                  color="purple"
                >
                  {job.employment_type}
                </Tag>
              )}
            </Space>
          </div>

          {/* Job Description */}
          <div className="mb-4">
            <Text type="secondary">
              {truncateText(job.description, viewMode === 'list' ? 200 : 150)}
            </Text>
          </div>

          {/* Skills */}
          {job.required_skills && job.required_skills.length > 0 && (
            <div className="mb-4">
              <Space
                wrap
                size="small"
              >
                {job.required_skills.slice(0, viewMode === 'list' ? 6 : 4).map((skill, index) => (
                  <Tag
                    key={index}
                    className="text-xs"
                  >
                    {skill}
                  </Tag>
                ))}
                {job.required_skills.length > (viewMode === 'list' ? 6 : 4) && (
                  <Tag className="text-xs">
                    +{job.required_skills.length - (viewMode === 'list' ? 6 : 4)} more
                  </Tag>
                )}
              </Space>
            </div>
          )}
        </div>

        {/* Actions Section */}
        {showActions && (
          <div
            className={
              viewMode === 'list'
                ? 'flex-shrink-0 w-48 flex flex-col justify-between'
                : 'w-full pt-4 border-t'
            }
          >
            <div
              className={
                viewMode === 'list' ? 'flex flex-col gap-2' : 'flex justify-between items-center'
              }
            >
              <Text
                type="secondary"
                className="text-sm"
              >
                Posted {formatJobDate(job.created_at)}
              </Text>

              <Space className={viewMode === 'list' ? 'flex-col w-full' : ''}>
                <Button
                  icon={<EyeOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCardClick();
                  }}
                >
                  View Details
                </Button>
                <Button
                  type="primary"
                  onClick={handleApply}
                  disabled={isApplied || loading}
                  loading={loading}
                  icon={isApplied ? <CheckCircleOutlined /> : null}
                >
                  {isApplied ? 'Applied' : 'Apply Now'}
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default JobCard;
