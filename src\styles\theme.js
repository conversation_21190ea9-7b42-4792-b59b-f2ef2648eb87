// Theme configuration with light and dark mode support
export const theme = {
  colors: {
    // Base colors that don't change with theme
    primary: '#0056D2',
    primaryHover: '#1A73E8',
    success: '#28A745',
    warning: '#FFC107',
    error: '#DC3545',

    // Theme-specific colors (light mode defaults)
    light: {
      background: '#FFFFFF',
      foreground: '#1E1E1E',
      secondary: '#F0F4FF',
      secondaryHover: '#DCE6FF',
      muted: '#6C757D',
      border: '#E2E8F0',
      card: '#FFFFFF',
      cardShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
      textPrimary: '#1E1E1E',
      textSecondary: '#4A5568',
      textMuted: '#6C757D',
    },

    // Dark mode colors
    dark: {
      background: '#121212',
      foreground: '#FFFFFF',
      secondary: '#2C2C2C',
      secondaryHover: '#3A3A3A',
      muted: '#A0A0A0',
      border: '#3A3A3A',
      card: '#1E1E1E',
      cardShadow: '0px 4px 8px rgba(0, 0, 0, 0.3)',
      textPrimary: '#FFFFFF',
      textSecondary: '#D1D5DB',
      textMuted: '#9CA3AF',
    },
  },
  typography: {
    fontFamily: ['Poppins', 'sans-serif'],
    fontSizes: {
      h1: '48px',
      h2: '36px',
      h3: '24px',
      body: '16px',
      small: '14px',
    },
    fontWeights: {
      h1: '700',
      h2: '600',
      h3: '500',
      body: '400',
    },
  },
  radii: {
    button: '8px',
    card: '12px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
  transitions: {
    default: '0.3s ease',
    fast: '0.15s ease',
    slow: '0.5s ease',
  },
};

// Helper function to get current theme colors
export const getThemeColors = (mode = 'light') => {
  return {
    ...theme.colors,
    ...(mode === 'dark' ? theme.colors.dark : theme.colors.light),
  };
};
