import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Divider,
  Upload,
  Select,
  Spin,
  message,
  Avatar,
  Tag,
  Row,
  Col,
  List,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  GlobalOutlined,
  UploadOutlined,
  BankOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  getCompanyProfile,
  updateCompanyProfile,
  uploadCompanyLogo,
} from '../services/company.service';
import useAuth from '@/hooks/useAuth';
import useCompanyStore from '../store/company.store';
import { COMPANY_TYPES, COMPANY_SIZES } from '@/utils/constants';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Profile = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [logoFile, setLogoFile] = useState(null);
  const [locations, setLocations] = useState([]);
  const [newLocation, setNewLocation] = useState('');
  const { user } = useAuth();
  const { profile, setCompanyProfile } = useCompanyStore();

  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.id) {
        setLoading(true);
        const { success, data } = await getCompanyProfile(user.id);
        if (success && data) {
          setCompanyProfile(data);
          form.setFieldsValue(data);

          // Set locations if available
          if (data.office_locations) {
            setLocations(data.office_locations);
          }
        }
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user?.id]);

  const handleSubmit = async (values) => {
    setSaving(true);
    try {
      const updatedProfile = {
        ...values,
        id: user.id,
        office_locations: locations,
      };

      const { success, error } = await updateCompanyProfile(user.id, updatedProfile);

      if (!success) {
        throw new Error(error || 'Failed to update profile');
      }

      // Upload logo if provided
      if (logoFile) {
        const { success: logoSuccess, error: logoError } = await uploadCompanyLogo(
          user.id,
          logoFile
        );
        if (!logoSuccess) {
          console.error('Logo upload failed:', logoError);
        }
      }

      // Refresh profile data
      const { success: refreshSuccess, data: refreshedData } = await getCompanyProfile(user.id);
      if (refreshSuccess && refreshedData) {
        setCompanyProfile(refreshedData);
      }

      message.success('Company profile updated successfully');
    } catch (error) {
      console.error('Profile update error:', error);
      message.error(error.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const beforeLogoUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG file!');
      return Upload.LIST_IGNORE;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must be smaller than 2MB!');
      return Upload.LIST_IGNORE;
    }

    setLogoFile(file);
    return false; // Prevent auto upload
  };

  const addLocation = () => {
    if (newLocation.trim()) {
      setLocations([...locations, newLocation.trim()]);
      setNewLocation('');
    }
  };

  const removeLocation = (index) => {
    const updatedLocations = [...locations];
    updatedLocations.splice(index, 1);
    setLocations(updatedLocations);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="company-profile">
      <Card className="mb-6">
        <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
          <Avatar
            size={100}
            src={profile?.company_logo_url}
            icon={!profile?.company_logo_url && <BankOutlined />}
          />
          <div className="flex-1">
            <Title level={3}>{profile?.company_name || 'Complete Your Company Profile'}</Title>
            <Text type="secondary">
              {profile?.primary_recruiter_name &&
                `Primary Recruiter: ${profile.primary_recruiter_name}`}
            </Text>

            <div className="mt-3 flex flex-wrap gap-2">
              {profile?.company_type && <Tag color="blue">{profile.company_type}</Tag>}
              {profile?.company_size && <Tag color="green">{profile.company_size}</Tag>}
              {profile?.office_locations?.length > 0 && (
                <Tag color="orange">{profile.office_locations.length} location(s)</Tag>
              )}
            </div>
          </div>
        </div>
      </Card>

      <Card title="Edit Company Profile">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={profile || {}}
        >
          <Divider orientation="left">Basic Information</Divider>
          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="company_name"
                label="Company Name"
                rules={[{ required: true, message: 'Please enter your company name' }]}
              >
                <Input
                  prefix={<BankOutlined />}
                  placeholder="Company Legal Name"
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="primary_recruiter_name"
                label="Primary Recruiter Name"
                rules={[{ required: true, message: 'Please enter primary recruiter name' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="Primary Recruiter Full Name"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="corporate_email"
                label="Corporate Email"
                rules={[
                  { required: true, message: 'Please enter your corporate email' },
                  { type: 'email', message: 'Please enter a valid email' },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="Corporate Email"
                  disabled
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="phone_number"
                label="Phone Number"
                rules={[
                  { required: true, message: 'Please enter your phone number' },
                  { pattern: /^[0-9]{10}$/, message: 'Please enter a valid 10-digit phone number' },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="Phone Number"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="website_url"
            label="Company Website"
            rules={[{ type: 'url', message: 'Please enter a valid URL' }]}
          >
            <Input
              prefix={<GlobalOutlined />}
              placeholder="Company Website URL"
            />
          </Form.Item>

          <Divider orientation="left">Company Details</Divider>
          <Row gutter={16}>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="company_type"
                label="Company Type"
              >
                <Select placeholder="Select company type">
                  {COMPANY_TYPES.map((type) => (
                    <Option
                      key={type}
                      value={type}
                    >
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col
              xs={24}
              md={12}
            >
              <Form.Item
                name="company_size"
                label="Company Size"
              >
                <Select placeholder="Select company size">
                  {COMPANY_SIZES.map((size) => (
                    <Option
                      key={size}
                      value={size}
                    >
                      {size}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="company_description"
            label="Company Description"
          >
            <TextArea
              rows={4}
              placeholder="Describe your company..."
            />
          </Form.Item>

          <Form.Item label="Office Locations">
            <List
              size="small"
              bordered
              dataSource={locations}
              renderItem={(item, index) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      danger
                      onClick={() => removeLocation(index)}
                    >
                      Remove
                    </Button>,
                  ]}
                >
                  <EnvironmentOutlined className="mr-2" /> {item}
                </List.Item>
              )}
              footer={
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a location"
                    value={newLocation}
                    onChange={(e) => setNewLocation(e.target.value)}
                    onPressEnter={addLocation}
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={addLocation}
                  >
                    Add
                  </Button>
                </div>
              }
            />
          </Form.Item>

          <Form.Item
            name="company_logo"
            label="Company Logo"
          >
            <Upload
              name="logo"
              listType="picture"
              beforeUpload={beforeLogoUpload}
              maxCount={1}
              showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
            >
              <Button icon={<UploadOutlined />}>Upload Logo</Button>
            </Upload>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={saving}
            >
              Save Company Profile
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Profile;
