/**
 * Company Calendar Page
 * 
 * Calendar page specifically designed for companies with:
 * - Candidate review scheduling
 * - Team meetings
 * - Hiring deadlines
 * - Onboarding sessions
 * - Google Calendar integration
 */

import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, Button, Alert, Row, Col, Statistic, Tag } from 'antd';
import { 
  CalendarOutlined, 
  TeamOutlined, 
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  GoogleOutlined,
  PlusOutlined
} from '@ant-design/icons';
import Calendar from '@/components/shared/Calendar';
import useAuth from '@/hooks/useAuth';
import useCompanyStore from '@/features/company/store/company.store';

const { Title, Text } = Typography;

const CompanyCalendar = () => {
  const { user, profile } = useAuth();
  const { jobs, applications } = useCompanyStore();
  const [upcomingEvents, setUpcomingEvents] = useState([]);

  // Company-specific event types
  const companyEventTypes = [
    { id: 'candidate_review', name: 'Candidate Review', color: 'blue' },
    { id: 'team_meeting', name: 'Team Meeting', color: 'green' },
    { id: 'hiring_deadline', name: 'Hiring Deadline', color: 'red' },
    { id: 'onboarding', name: 'Onboarding', color: 'cyan' },
    { id: 'strategy_session', name: 'Strategy Session', color: 'purple' },
    { id: 'client_meeting', name: 'Client Meeting', color: 'orange' }
  ];

  // Get participants (team members, candidates, clients)
  const getParticipants = () => {
    const participants = [];
    
    // Add team members (this would come from company team data)
    // For now, we'll use placeholder data
    participants.push(
      { name: 'HR Manager', email: '<EMAIL>' },
      { name: 'Technical Lead', email: '<EMAIL>' },
      { name: 'Hiring Manager', email: '<EMAIL>' }
    );

    // Add candidates from applications
    applications?.forEach(application => {
      if (application.candidates?.email) {
        participants.push({
          name: application.candidates.full_name,
          email: application.candidates.email
        });
      }
    });

    return participants;
  };

  // Calculate upcoming events from jobs and applications
  useEffect(() => {
    const events = [];
    const now = new Date();

    // Add job posting deadlines
    jobs?.forEach(job => {
      if (job.application_deadline) {
        const deadline = new Date(job.application_deadline);
        if (deadline > now) {
          events.push({
            type: 'hiring_deadline',
            title: `Application Deadline: ${job.title}`,
            date: deadline,
            priority: 'high',
            data: job
          });
        }
      }
    });

    // Add candidate review sessions (for shortlisted candidates)
    applications?.filter(app => app.status === 'shortlisted')
      .forEach(application => {
        // Schedule review 2 days from now (example logic)
        const reviewDate = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000);
        events.push({
          type: 'candidate_review',
          title: `Review: ${application.candidates?.full_name}`,
          date: reviewDate,
          priority: 'medium',
          data: application
        });
      });

    setUpcomingEvents(events.slice(0, 5)); // Show top 5 upcoming events
  }, [jobs, applications]);

  // Get calendar statistics
  const getCalendarStats = () => {
    const now = new Date();
    const thisWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const activeJobs = jobs?.filter(job => job.status === 'active').length || 0;
    
    const pendingReviews = applications?.filter(app => 
      app.status === 'applied' || app.status === 'under_review'
    ).length || 0;

    const shortlistedCandidates = applications?.filter(app => 
      app.status === 'shortlisted'
    ).length || 0;

    const upcomingDeadlines = jobs?.filter(job => {
      if (!job.application_deadline) return false;
      const deadline = new Date(job.application_deadline);
      return deadline > now && deadline <= thisWeek;
    }).length || 0;

    return {
      activeJobs,
      pendingReviews,
      shortlistedCandidates,
      upcomingDeadlines,
      totalEvents: upcomingEvents.length
    };
  };

  const stats = getCalendarStats();

  if (!user) {
    return (
      <div className="flex justify-center items-center h-64">
        <Alert
          message="Authentication Required"
          description="Please log in to access your calendar."
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className="company-calendar p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarOutlined className="text-2xl text-primary mr-3" />
            <div>
              <Title level={2} className="m-0">
                Company Calendar
              </Title>
              <Text type="secondary">
                Manage hiring schedules, team meetings, and deadlines
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Statistics */}
      <Row gutter={16} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Job Postings"
              value={stats.activeJobs}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending Reviews"
              value={stats.pendingReviews}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Shortlisted Candidates"
              value={stats.shortlistedCandidates}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Upcoming Deadlines"
              value={stats.upcomingDeadlines}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Card className="mb-6" size="small">
        <div className="flex items-center justify-between">
          <Text strong>Quick Actions</Text>
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              size="small"
            >
              Schedule Review
            </Button>
            <Button 
              icon={<TeamOutlined />}
              size="small"
            >
              Team Meeting
            </Button>
            <Button 
              icon={<UserOutlined />}
              size="small"
            >
              Onboarding Session
            </Button>
            <Button 
              icon={<GoogleOutlined />}
              size="small"
            >
              Sync Google Calendar
            </Button>
          </Space>
        </div>
      </Card>

      {/* Calendar Component */}
      <Calendar
        userType="company"
        eventTypes={companyEventTypes}
        participants={getParticipants()}
        viewOptions={{
          month: true,
          day: true,
          agenda: true
        }}
      />

      {/* Upcoming Events Summary */}
      {upcomingEvents.length > 0 && (
        <Card className="mt-6" title="Upcoming Events" size="small">
          <div className="space-y-3">
            {upcomingEvents.map((event, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    event.priority === 'high' ? 'bg-red-500' :
                    event.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`} />
                  <div>
                    <Text strong>{event.title}</Text>
                    <div className="text-sm text-gray-500">
                      {event.date.toLocaleDateString()} at {event.date.toLocaleTimeString()}
                    </div>
                    <div className="mt-1">
                      <Tag color={companyEventTypes.find(t => t.id === event.type)?.color}>
                        {companyEventTypes.find(t => t.id === event.type)?.name}
                      </Tag>
                    </div>
                  </div>
                </div>
                <Button size="small" type="link">
                  View Details
                </Button>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Active Jobs Summary */}
      {jobs && jobs.length > 0 && (
        <Card className="mt-6" title="Active Job Postings" size="small">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {jobs.filter(job => job.status === 'active').slice(0, 6).map(job => (
              <div key={job.id} className="p-3 border rounded">
                <Text strong className="block">{job.title}</Text>
                <div className="text-sm text-gray-500 mt-1">
                  Applications: {applications?.filter(app => app.job_id === job.id).length || 0}
                </div>
                {job.application_deadline && (
                  <div className="text-sm text-gray-500">
                    Deadline: {new Date(job.application_deadline).toLocaleDateString()}
                  </div>
                )}
                <div className="mt-2">
                  <Tag color={job.status === 'active' ? 'green' : 'orange'}>
                    {job.status}
                  </Tag>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Help Section */}
      <Card className="mt-6" size="small">
        <Title level={5}>Calendar Tips for Companies</Title>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Text strong>Candidate Reviews</Text>
            <div className="text-sm text-gray-600 mt-1">
              Schedule regular review sessions for shortlisted candidates
            </div>
          </div>
          <div>
            <Text strong>Team Coordination</Text>
            <div className="text-sm text-gray-600 mt-1">
              Coordinate with team members for hiring decisions and onboarding
            </div>
          </div>
          <div>
            <Text strong>Deadline Management</Text>
            <div className="text-sm text-gray-600 mt-1">
              Track application deadlines and hiring milestones
            </div>
          </div>
          <div>
            <Text strong>Google Integration</Text>
            <div className="text-sm text-gray-600 mt-1">
              Sync with Google Calendar for seamless team collaboration
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CompanyCalendar;
