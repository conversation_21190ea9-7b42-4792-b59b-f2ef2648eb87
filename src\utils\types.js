/**
 * Type definitions for the real estate job platform
 */

// ==================== CANDIDATE TYPES ====================

/**
 * @typedef {Object} Skill
 * @property {string} name - Name of the skill
 * @property {string} category - Category of the skill (e.g., Sales, CRM, Property Management)
 * @property {number} [level] - Proficiency level (1-5)
 */

/**
 * @typedef {Object} Language
 * @property {string} name - Name of the language
 * @property {string} proficiency - Proficiency level (Basic, Intermediate, Fluent, Native)
 */

/**
 * @typedef {Object} AvailabilitySlot
 * @property {string} date - Date in ISO format
 * @property {string} startTime - Start time in 24-hour format (HH:MM)
 * @property {string} endTime - End time in 24-hour format (HH:MM)
 */

/**
 * @typedef {Object} CandidateProfile
 * @property {string} id - Unique identifier (UUID)
 * @property {string} full_name - Full name of the candidate
 * @property {string} phone_number - Phone number (verified via OTP)
 * @property {string} email - Email address
 * @property {string} city - City of residence
 * @property {string} role_applied_for - Specific role applied for
 * @property {number} years_experience - Years of experience
 * @property {string} [resume_url] - URL to the uploaded resume
 * @property {string} [current_job_title] - Current job title
 * @property {string} [current_company] - Current company
 * @property {Skill[]} [skills] - Array of skills
 * @property {string} [expected_ctc] - Expected compensation
 * @property {string} [notice_period] - Notice period duration
 * @property {string} [linkedin_url] - LinkedIn profile URL
 * @property {Language[]} [languages] - Languages spoken
 * @property {AvailabilitySlot[]} [availability_calendar] - Availability calendar for interviews
 * @property {string} [profile_photo_url] - URL to profile photo
 * @property {string} [video_intro_url] - URL to video introduction
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

// ==================== COMPANY TYPES ====================

/**
 * @typedef {Object} OfficeLocation
 * @property {string} address - Full address of the office
 * @property {string} city - City
 * @property {string} state - State/Province
 * @property {string} [country] - Country (default: India)
 * @property {boolean} [isHeadquarters] - Whether this is the headquarters
 */

/**
 * @typedef {Object} NotificationSettings
 * @property {boolean} email - Whether to receive email notifications
 * @property {boolean} sms - Whether to receive SMS notifications
 * @property {boolean} inApp - Whether to receive in-app notifications
 * @property {string[]} notificationTypes - Types of notifications to receive
 */

/**
 * @typedef {Object} InterviewPreferences
 * @property {string[]} formats - Preferred interview formats (video, phone, in-person)
 * @property {number} defaultDuration - Default interview duration in minutes
 * @property {string[]} preferredDays - Preferred days for interviews
 * @property {string[]} preferredTimeSlots - Preferred time slots for interviews
 */

/**
 * @typedef {Object} BillingInformation
 * @property {string} billingName - Name for billing
 * @property {string} billingEmail - Email for billing
 * @property {string} billingAddress - Address for billing
 * @property {string} gstNumber - GST number
 * @property {string} [panNumber] - PAN number
 * @property {string} [paymentMethod] - Preferred payment method
 */

/**
 * @typedef {Object} CompanyProfile
 * @property {string} id - Unique identifier (UUID)
 * @property {string} company_name - Legal name of the company
 * @property {string} primary_recruiter_name - Name of the primary recruiter
 * @property {string} corporate_email - Corporate email address
 * @property {string} phone_number - Phone number (verified via OTP)
 * @property {string} [company_logo_url] - URL to company logo
 * @property {string} [website_url] - Official website URL
 * @property {OfficeLocation[]} [office_locations] - Array of office locations
 * @property {string} [company_type] - Type of company (Developer, Brokerage, etc.)
 * @property {string} [company_size] - Size of company (employee count range)
 * @property {string} [company_description] - Description/bio of the company
 * @property {NotificationSettings} [chat_notification_settings] - Notification preferences
 * @property {InterviewPreferences} [interview_preferences] - Interview format preferences
 * @property {BillingInformation} [billing_information] - Billing information
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

// ==================== INTERVIEWER TYPES ====================

/**
 * @typedef {Object} PreferredRole
 * @property {string} role - Role name (e.g., Sales Manager, Property Consultant)
 * @property {number} experienceYears - Years of experience in this role
 */

/**
 * @typedef {Object} PaymentDetails
 * @property {string} accountHolderName - Name of the account holder
 * @property {string} accountNumber - Bank account number
 * @property {string} ifscCode - IFSC code
 * @property {string} bankName - Name of the bank
 * @property {string} [upiId] - UPI ID for payments
 */

/**
 * @typedef {Object} PerformanceMetrics
 * @property {number} averageRating - Average rating (1-5)
 * @property {number} interviewsCompleted - Number of interviews completed
 * @property {number} candidatesSatisfaction - Candidate satisfaction score
 * @property {number} companiesSatisfaction - Companies satisfaction score
 * @property {number} timelinessScore - Score for timeliness
 */

/**
 * @typedef {Object} InterviewerProfile
 * @property {string} id - Unique identifier (UUID)
 * @property {string} full_name - Full name of the interviewer
 * @property {string} professional_email - Professional email address
 * @property {string} phone_number - Phone number (verified via OTP)
 * @property {string} [current_designation] - Current job designation
 * @property {string} [current_company] - Current company
 * @property {string} [linkedin_url] - LinkedIn profile URL
 * @property {number} [years_experience] - Years of industry experience
 * @property {PreferredRole[]} [preferred_interview_roles] - Roles they can interview for
 * @property {boolean} [equipment_confirmation] - Audio/video equipment confirmation
 * @property {PaymentDetails} [payment_details] - Payment details
 * @property {number} [performance_rating] - Performance rating (system-generated)
 * @property {PerformanceMetrics} [performance_metrics] - Detailed performance metrics
 * @property {AvailabilitySlot[]} [availability_calendar] - Availability calendar
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

// ==================== INTERVIEW TYPES ====================

/**
 * @typedef {Object} FeedbackSection
 * @property {string} category - Category of feedback (e.g., Technical Skills, Communication)
 * @property {number} score - Score for this category (1-5)
 * @property {string} comments - Detailed comments
 */

/**
 * @typedef {Object} InterviewFeedback
 * @property {FeedbackSection[]} sections - Sections of feedback
 * @property {string} overallComments - Overall comments
 * @property {number} overallScore - Overall score (1-100)
 * @property {string[]} strengths - List of strengths
 * @property {string[]} areasForImprovement - List of areas for improvement
 * @property {boolean} recommended - Whether the candidate is recommended
 */

/**
 * @typedef {Object} Interview
 * @property {string} id - Unique identifier (UUID)
 * @property {string} candidate_id - ID of the candidate
 * @property {string} interviewer_id - ID of the interviewer
 * @property {string} [job_id] - ID of the job (if applicable)
 * @property {string} [company_id] - ID of the company (if applicable)
 * @property {string} status - Status of the interview (requested, scheduled, completed)
 * @property {number} [score] - Score given to the candidate (1-100)
 * @property {InterviewFeedback} [feedback] - Detailed feedback
 * @property {string} [interview_date] - Date and time of the interview
 * @property {number} [duration_minutes] - Duration of the interview in minutes
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

// ==================== JOB TYPES ====================

/**
 * @typedef {Object} RequiredSkill
 * @property {string} name - Name of the skill
 * @property {string} category - Category of the skill
 * @property {number} [minimumLevel] - Minimum proficiency level required (1-5)
 * @property {boolean} [isMandatory] - Whether this skill is mandatory
 */

/**
 * @typedef {Object} SalaryRange
 * @property {number} min - Minimum salary
 * @property {number} max - Maximum salary
 * @property {string} currency - Currency (default: INR)
 * @property {string} period - Period (annual, monthly)
 */

/**
 * @typedef {Object} JobLocation
 * @property {string} city - City
 * @property {string} state - State
 * @property {string} [country] - Country (default: India)
 * @property {boolean} [isRemote] - Whether this job is remote
 * @property {boolean} [isHybrid] - Whether this job is hybrid
 */

/**
 * @typedef {Object} Job
 * @property {string} id - Unique identifier (UUID)
 * @property {string} company_id - ID of the company posting the job
 * @property {string} title - Job title
 * @property {string} description - Job description
 * @property {RequiredSkill[]} required_skills - Required skills
 * @property {string} experience_level - Required experience level
 * @property {JobLocation} location - Job location
 * @property {SalaryRange} salary_range - Salary range
 * @property {string} status - Status of the job (active, filled, closed)
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

/**
 * @typedef {Object} JobApplication
 * @property {string} id - Unique identifier (UUID)
 * @property {string} candidate_id - ID of the candidate
 * @property {string} job_id - ID of the job
 * @property {string} status - Status of the application
 * @property {string} application_date - Date of application
 * @property {number} [interview_score] - Score from interview
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */
