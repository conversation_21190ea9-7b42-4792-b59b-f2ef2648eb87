/**
 * Job Application Modal Component
 * Handles job application submission with additional information
 */

import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Typography,
  Space,
  Upload,
  Select,
  Checkbox,
  Divider,
  Alert,
  Row,
  Col,
  Avatar,
} from 'antd';
import {
  UploadOutlined,
  FileTextOutlined,
  SendOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const JobApplicationModal = ({ job, open, onClose, onSubmit, loading = false }) => {
  const [form] = Form.useForm();
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const { profile } = useAuth();

  if (!job) return null;

  const company = job.companies || job.company_profiles;

  const handleSubmit = async (values) => {
    if (!agreedToTerms) {
      return;
    }

    const applicationData = {
      ...values,
      cover_letter: values.cover_letter || '',
      expected_salary: values.expected_salary || profile?.expected_ctc,
      notice_period: values.notice_period || profile?.notice_period,
      additional_info: values.additional_info || '',
    };

    await onSubmit(job.id, applicationData);
    form.resetFields();
    setAgreedToTerms(false);
  };

  const handleCancel = () => {
    form.resetFields();
    setAgreedToTerms(false);
    onClose();
  };

  return (
    <Modal
      title={null}
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={700}
      className="job-application-modal"
    >
      <div className="job-application-content">
        {/* Header */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center gap-4">
            {company?.company_logo_url && (
              <Avatar
                src={company.company_logo_url}
                size={48}
                shape="square"
                className="border"
              />
            )}
            <div>
              <Title
                level={4}
                className="mb-1"
              >
                Apply for {job.title}
              </Title>
              <Text type="secondary">at {company?.company_name}</Text>
            </div>
          </div>
        </div>

        {/* Application Form */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            expected_salary: profile?.expected_ctc,
            notice_period: profile?.notice_period,
          }}
        >
          {/* Cover Letter */}
          <Form.Item
            name="cover_letter"
            label="Cover Letter"
            extra="Tell the employer why you're interested in this role and what makes you a great fit."
          >
            <TextArea
              rows={6}
              placeholder="Write a compelling cover letter that highlights your relevant experience and enthusiasm for this position..."
              maxLength={1000}
              showCount
            />
          </Form.Item>

          {/* Expected Salary and Notice Period */}
          <Row gutter={16}>
            <Col
              xs={24}
              sm={12}
            >
              <Form.Item
                name="expected_salary"
                label="Expected Salary (Annual)"
                extra="Your expected annual salary in INR"
              >
                <Input
                  placeholder="e.g., 12,00,000"
                  suffix="per annum"
                  addonBefore="₹"
                />
              </Form.Item>
            </Col>
            <Col
              xs={24}
              sm={12}
            >
              <Form.Item
                name="notice_period"
                label="Notice Period"
                extra="How soon can you join?"
              >
                <Select placeholder="Select notice period">
                  <Option value="Immediate">Immediate</Option>
                  <Option value="15 days">15 days</Option>
                  <Option value="1 month">1 month</Option>
                  <Option value="2 months">2 months</Option>
                  <Option value="3 months">3 months</Option>
                  <Option value="More than 3 months">More than 3 months</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* Resume Upload */}
          <Form.Item
            name="resume"
            label="Resume"
            extra="Upload your latest resume (PDF, DOC, DOCX - Max 5MB)"
          >
            <Upload
              beforeUpload={() => false} // Prevent auto upload
              accept=".pdf,.doc,.docx"
              maxCount={1}
              listType="text"
            >
              <Button icon={<UploadOutlined />}>Upload Resume</Button>
            </Upload>
          </Form.Item>

          {/* Additional Information */}
          <Form.Item
            name="additional_info"
            label="Additional Information"
            extra="Any additional information you'd like to share (portfolio links, certifications, etc.)"
          >
            <TextArea
              rows={3}
              placeholder="Share any additional information that might be relevant for this application..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Divider />

          {/* Profile Summary */}
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-start gap-2 mb-2">
              <InfoCircleOutlined className="text-blue-500 mt-1" />
              <Text strong>Your Profile Summary</Text>
            </div>
            <Space
              direction="vertical"
              size="small"
              className="w-full"
            >
              <Text>
                <strong>Experience:</strong> {profile?.years_experience || 'Not specified'} years
              </Text>
              <Text>
                <strong>Current Role:</strong> {profile?.current_job_title || 'Not specified'}
              </Text>
              <Text>
                <strong>Location:</strong> {profile?.city || 'Not specified'}
              </Text>
              {profile?.skills && profile.skills.length > 0 && (
                <Text>
                  <strong>Skills:</strong>{' '}
                  {profile.skills
                    .slice(0, 5)
                    .map((skill) => (typeof skill === 'string' ? skill : skill.name))
                    .join(', ')}
                  {profile.skills.length > 5 && '...'}
                </Text>
              )}
            </Space>
          </div>

          {/* Terms and Conditions */}
          <Form.Item>
            <Checkbox
              checked={agreedToTerms}
              onChange={(e) => setAgreedToTerms(e.target.checked)}
            >
              I agree to the{' '}
              <a
                href="/terms"
                target="_blank"
                rel="noopener noreferrer"
              >
                Terms and Conditions
              </a>{' '}
              and{' '}
              <a
                href="/privacy"
                target="_blank"
                rel="noopener noreferrer"
              >
                Privacy Policy
              </a>
            </Checkbox>
          </Form.Item>

          {/* Submit Buttons */}
          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button
                onClick={handleCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={!agreedToTerms}
                icon={<SendOutlined />}
              >
                Submit Application
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* Application Tips */}
        <Alert
          message="Application Tips"
          description={
            <ul className="mt-2 space-y-1">
              <li>• Write a personalized cover letter for better chances</li>
              <li>• Ensure your resume is up-to-date and relevant</li>
              <li>• Highlight skills that match the job requirements</li>
              <li>• Be honest about your notice period and salary expectations</li>
            </ul>
          }
          type="info"
          showIcon
          className="mt-4"
        />
      </div>
    </Modal>
  );
};

export default JobApplicationModal;
