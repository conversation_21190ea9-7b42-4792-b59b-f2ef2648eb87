import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';

/**
 * Optimized useAssessments Hook for Companies
 *
 * Note: This hook handles company-specific assessment logic that's not
 * currently in the company store. Could be moved to store in the future.
 *
 * Features:
 * - Fetches assessments created by the company
 * - Retrieves assessment results for company assessments
 * - Provides refresh capabilities
 */
const useAssessments = () => {
  const [assessments, setAssessments] = useState([]);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user, profile } = useAuth();

  // Optimized fetch function with better error handling
  const fetchAssessments = useCallback(async () => {
    if (!user || !profile) return;

    setLoading(true);
    setError(null);

    try {
      // For companies, fetch assessments they've created
      const { data: assessmentsData, error: assessmentsError } = await supabase
        .from('assessments')
        .select('*')
        .eq('company_id', user.id)
        .order('created_at', { ascending: false });

      if (assessmentsError) throw assessmentsError;

      setAssessments(assessmentsData || []);

      // Fetch all results for their assessments if assessments exist
      if (assessmentsData && assessmentsData.length > 0) {
        const { data: resultsData, error: resultsError } = await supabase
          .from('assessment_results')
          .select('*, assessment:assessments(*), candidate:profiles(*)')
          .in(
            'assessment_id',
            assessmentsData.map((a) => a.id)
          )
          .order('created_at', { ascending: false });

        if (resultsError) throw resultsError;
        setResults(resultsData || []);
      } else {
        setResults([]);
      }

      return {
        assessments: assessmentsData || [],
        results: resultsData || [],
      };
    } catch (err) {
      console.error('Assessment fetch failed:', err);
      setError(err.message);
      return {
        assessments: [],
        results: [],
      };
    } finally {
      setLoading(false);
    }
  }, [user, profile]);

  // Effect to fetch data when user/profile changes
  useEffect(() => {
    if (user && profile) {
      fetchAssessments();
    }
  }, [user, profile, fetchAssessments]);

  // Enhanced refresh function
  const refresh = useCallback(() => {
    return fetchAssessments();
  }, [fetchAssessments]);

  return {
    // Data
    assessments: assessments || [],
    results: results || [],

    // UI state
    loading,
    error,

    // Actions
    refetch: fetchAssessments,
    refresh,

    // Computed data
    totalAssessments: assessments.length,
    totalResults: results.length,
    completionRate:
      assessments.length > 0 ? Math.round((results.length / assessments.length) * 100) : 0,
  };
};

export default useAssessments;
