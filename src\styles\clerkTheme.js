import { dark } from '@clerk/themes';

// Custom Clerk theme configuration
export const getClerkTheme = (isDark = false) => {
  const baseTheme = isDark ? dark : undefined;
  
  return {
    baseTheme,
    variables: {
      colorPrimary: '#1890ff',
      colorDanger: '#ff4d4f',
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorNeutral: isDark ? '#ffffff' : '#000000',
      colorBackground: isDark ? '#141414' : '#ffffff',
      colorInputBackground: isDark ? '#1f1f1f' : '#ffffff',
      colorInputText: isDark ? '#ffffff' : '#000000',
      colorText: isDark ? '#ffffff' : '#000000',
      colorTextSecondary: isDark ? '#a6a6a6' : '#666666',
      colorShimmer: isDark ? '#2a2a2a' : '#f0f0f0',
      borderRadius: '8px',
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: '14px',
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      spacingUnit: '1rem',
    },
    elements: {
      // Root container
      rootBox: {
        width: '100%',
        maxWidth: '400px',
      },
      
      // Card container
      card: {
        backgroundColor: isDark ? '#1f1f1f' : '#ffffff',
        border: `1px solid ${isDark ? '#303030' : '#e8e8e8'}`,
        borderRadius: '12px',
        boxShadow: isDark 
          ? '0 4px 12px rgba(0, 0, 0, 0.3)' 
          : '0 4px 12px rgba(0, 0, 0, 0.1)',
        padding: '2rem',
      },
      
      // Header elements
      headerTitle: {
        fontSize: '24px',
        fontWeight: '600',
        color: isDark ? '#ffffff' : '#000000',
        marginBottom: '0.5rem',
      },
      
      headerSubtitle: {
        fontSize: '14px',
        color: isDark ? '#a6a6a6' : '#666666',
        marginBottom: '1.5rem',
      },
      
      // Form elements
      formFieldInput: {
        backgroundColor: isDark ? '#262626' : '#ffffff',
        border: `1px solid ${isDark ? '#404040' : '#d9d9d9'}`,
        borderRadius: '8px',
        padding: '12px 16px',
        fontSize: '14px',
        color: isDark ? '#ffffff' : '#000000',
        height: '48px',
        '&:focus': {
          borderColor: '#1890ff',
          boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
        },
        '&::placeholder': {
          color: isDark ? '#8c8c8c' : '#bfbfbf',
        },
      },
      
      formFieldLabel: {
        fontSize: '14px',
        fontWeight: '500',
        color: isDark ? '#ffffff' : '#000000',
        marginBottom: '8px',
      },
      
      // Buttons
      formButtonPrimary: {
        backgroundColor: '#1890ff',
        border: 'none',
        borderRadius: '8px',
        padding: '12px 24px',
        fontSize: '14px',
        fontWeight: '500',
        color: '#ffffff',
        height: '48px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          backgroundColor: '#40a9ff',
          transform: 'translateY(-1px)',
        },
        '&:active': {
          backgroundColor: '#096dd9',
          transform: 'translateY(0)',
        },
        '&:disabled': {
          backgroundColor: isDark ? '#434343' : '#f5f5f5',
          color: isDark ? '#8c8c8c' : '#bfbfbf',
          cursor: 'not-allowed',
          transform: 'none',
        },
      },
      
      // Social buttons
      socialButtonsBlockButton: {
        backgroundColor: isDark ? '#262626' : '#ffffff',
        border: `1px solid ${isDark ? '#404040' : '#d9d9d9'}`,
        borderRadius: '8px',
        padding: '12px 16px',
        fontSize: '14px',
        fontWeight: '500',
        color: isDark ? '#ffffff' : '#000000',
        height: '48px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          backgroundColor: isDark ? '#303030' : '#fafafa',
          borderColor: '#1890ff',
        },
      },
      
      // Links
      footerActionLink: {
        color: '#1890ff',
        textDecoration: 'none',
        fontSize: '14px',
        fontWeight: '500',
        '&:hover': {
          color: '#40a9ff',
          textDecoration: 'underline',
        },
      },
      
      // Divider
      dividerLine: {
        backgroundColor: isDark ? '#404040' : '#e8e8e8',
      },
      
      dividerText: {
        color: isDark ? '#8c8c8c' : '#8c8c8c',
        fontSize: '12px',
        fontWeight: '500',
      },
      
      // Error messages
      formFieldErrorText: {
        color: '#ff4d4f',
        fontSize: '12px',
        marginTop: '4px',
      },
      
      // Loading spinner
      spinner: {
        color: '#1890ff',
      },
      
      // Modal overlay
      modalBackdrop: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
      },
      
      // Footer
      footer: {
        marginTop: '1.5rem',
        textAlign: 'center',
      },
      
      // Identity preview
      identityPreview: {
        backgroundColor: isDark ? '#262626' : '#fafafa',
        border: `1px solid ${isDark ? '#404040' : '#e8e8e8'}`,
        borderRadius: '8px',
        padding: '12px',
      },
      
      // Avatar
      avatarBox: {
        width: '48px',
        height: '48px',
        borderRadius: '50%',
        backgroundColor: isDark ? '#404040' : '#e8e8e8',
      },
    },
  };
};

export default getClerkTheme;
