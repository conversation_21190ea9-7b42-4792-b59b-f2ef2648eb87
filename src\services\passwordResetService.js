import { supabase } from '@/utils/supabaseClient';

/**
 * Optimized Password Reset Service
 */
class PasswordResetService {
  static async sendPasswordResetOTP(email) {
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: { shouldCreateUser: false },
    });

    if (error) {
      if (error.message.includes('rate limit')) {
        throw new Error('Too many requests. Please wait a few minutes before trying again.');
      }

      if (
        error.message.includes('Signups not allowed') ||
        error.message.includes('User not found') ||
        error.message.includes('Invalid login credentials')
      ) {
        throw new Error(
          'No account found with this email address. Please check your email or create a new account.'
        );
      }

      if (error.message.includes('Email not confirmed')) {
        throw new Error(
          'Please verify your email address first. Check your inbox for the verification email.'
        );
      }

      throw new Error('Unable to send verification code. Please try again or contact support.');
    }

    return {
      success: true,
      message: 'Verification code sent to your email.',
      email,
    };
  }

  static async verifyPasswordResetOTP(email, otp) {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token: otp,
      type: 'email',
    });

    if (error) {
      if (error.message.includes('Token has expired')) {
        throw new Error('Verification code has expired. Please request a new one.');
      }
      if (error.message.includes('Invalid token')) {
        throw new Error('Invalid verification code. Please check and try again.');
      }
      throw new Error(error.message || 'Verification failed. Please try again.');
    }

    return {
      success: true,
      user: data.user,
      session: data.session,
      message: 'Verification successful. You can now reset your password.',
    };
  }

  static async resetPassword(newPassword) {
    const { error } = await supabase.auth.updateUser({ password: newPassword });

    if (error) {
      if (error.message.includes('Password should be at least')) {
        throw new Error('Password must be at least 6 characters long.');
      }
      throw new Error(error.message || 'Failed to reset password. Please try again.');
    }

    return {
      success: true,
      message: 'Password reset successfully!',
    };
  }

  static async resendPasswordResetOTP(email) {
    return this.sendPasswordResetOTP(email);
  }
}

export default PasswordResetService;
