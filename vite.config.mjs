import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: { host: true },
  resolve: {
    extensions: ['.jsx', '.js'],
    alias: {
      '@': '/src',
      '@assets': '/src/assets',
      '@components': '/src/components',
      '@features': '/src/features',
      '@hooks': '/src/hooks',
      '@layouts': '/src/layouts',
      '@pages': '/src/pages',
      '@router': '/src/router',
      '@services': '/src/services',
      '@store': '/src/store',
      '@styles': '/src/styles',
      '@utils': '/src/utils',
      '@constants': '/src/constants',
    },
  },
});
