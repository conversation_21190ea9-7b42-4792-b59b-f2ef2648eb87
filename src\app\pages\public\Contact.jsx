import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Typography, Form, Input, Button, Select, Card, Row, Col, Divider } from 'antd';
import {
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  SendOutlined,
  UserOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import useDeviceDetect from '@/hooks/useDeviceDetect';
import showToast from '@/utils/toast';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const Contact = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { isMobile, isTablet } = useDeviceDetect();

  const navigate = useNavigate();

  const contactInfo = [
    {
      icon: <MailOutlined className="text-primary text-2xl" />,
      title: 'Email Us',
      description: 'Our team will respond within 24 hours',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>',
    },
    {
      icon: <PhoneOutlined className="text-primary text-2xl" />,
      title: 'Call Us',
      description: 'Monday to Friday, 9am to 5pm',
      value: '+****************',
      link: 'tel:+15551234567',
    },
    {
      icon: <EnvironmentOutlined className="text-primary text-2xl" />,
      title: 'Visit Us',
      description: 'Our headquarters location',
      value: '123 Interview St, San Francisco, CA 94103',
      link: 'https://maps.google.com/?q=123+Interview+St,+San+Francisco,+CA+94103',
    },
  ];

  const inquiryTypes = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'support', label: 'Technical Support' },
    { value: 'billing', label: 'Billing Question' },
    { value: 'partnership', label: 'Partnership Opportunity' },
    { value: 'feedback', label: 'Feedback' },
  ];

  const onFinish = (values) => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      console.log('Form values:', values);
      showToast.success('Your message has been sent successfully!');
      form.resetFields();
      setLoading(false);
    }, 1500);
  };

  return (
    <div className="contact-page max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center py-8 md:py-12">
        <Title
          level={1}
          className="mb-4"
        >
          Get in Touch
        </Title>
        <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
          Have questions or need assistance? We're here to help. Reach out to our team through any
          of the channels below.
        </Paragraph>
      </div>

      {/* Contact Info Cards */}
      <Row
        gutter={[24, 24]}
        className="mb-12"
      >
        {contactInfo.map((item, index) => (
          <Col
            xs={24}
            md={8}
            key={index}
          >
            <Card
              className="contact-card text-center h-full hover:shadow-lg transition-all"
              bordered={false}
            >
              <div className="inline-block p-3 bg-secondary rounded-full mb-4">{item.icon}</div>
              <Title
                level={4}
                className="mb-2"
              >
                {item.title}
              </Title>
              <Paragraph className="text-muted mb-4">{item.description}</Paragraph>
              <a
                href={item.link}
                className="text-primary font-medium hover:underline"
                target={item.title === 'Visit Us' ? '_blank' : undefined}
                rel={item.title === 'Visit Us' ? 'noopener noreferrer' : undefined}
              >
                {item.value}
              </a>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Contact Form Section */}
      <Row
        gutter={[32, 32]}
        className="mb-12"
      >
        <Col
          xs={24}
          lg={12}
        >
          <div className="form-content pr-0 lg:pr-8">
            <Title
              level={2}
              className="mb-4"
            >
              Send Us a Message
            </Title>
            <Paragraph className="text-muted mb-6">
              Fill out the form below and we'll get back to you as soon as possible. Your feedback
              and questions are important to us.
            </Paragraph>

            <div className="bg-secondary p-6 rounded-lg mb-6">
              <div className="flex items-start mb-4">
                <QuestionCircleOutlined className="text-primary mt-1 mr-3" />
                <div>
                  <Text strong>Frequently Asked Questions</Text>
                  <Paragraph className="text-sm text-muted mb-0">
                    Check our{' '}
                    <Link
                      to="/faqs"
                      className="text-primary hover:underline"
                    >
                      FAQ page
                    </Link>{' '}
                    for quick answers to common questions.
                  </Paragraph>
                </div>
              </div>

              <Divider className="my-4" />

              <div className="flex items-start">
                <UserOutlined className="text-primary mt-1 mr-3" />
                <div>
                  <Text strong>Need Immediate Help?</Text>
                  <Paragraph className="text-sm text-muted mb-0">
                    Visit our{' '}
                    <Link
                      to="/help-center"
                      className="text-primary hover:underline"
                    >
                      Help Center
                    </Link>{' '}
                    for detailed guides and tutorials.
                  </Paragraph>
                </div>
              </div>
            </div>
          </div>
        </Col>

        <Col
          xs={24}
          lg={12}
        >
          <Card
            bordered={false}
            className="contact-form-card"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              requiredMark={false}
            >
              <Row gutter={16}>
                <Col
                  xs={24}
                  sm={12}
                >
                  <Form.Item
                    name="firstName"
                    label="First Name"
                    rules={[{ required: true, message: 'Please enter your first name' }]}
                  >
                    <Input placeholder="Enter your first name" />
                  </Form.Item>
                </Col>
                <Col
                  xs={24}
                  sm={12}
                >
                  <Form.Item
                    name="lastName"
                    label="Last Name"
                    rules={[{ required: true, message: 'Please enter your last name' }]}
                  >
                    <Input placeholder="Enter your last name" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="email"
                label="Email Address"
                rules={[
                  { required: true, message: 'Please enter your email' },
                  { type: 'email', message: 'Please enter a valid email' },
                ]}
              >
                <Input
                  placeholder="Enter your email address"
                  prefix={<MailOutlined className="text-muted" />}
                />
              </Form.Item>

              <Form.Item
                name="phone"
                label="Phone Number (Optional)"
              >
                <Input
                  placeholder="Enter your phone number"
                  prefix={<PhoneOutlined className="text-muted" />}
                />
              </Form.Item>

              <Form.Item
                name="inquiryType"
                label="Inquiry Type"
                rules={[{ required: true, message: 'Please select an inquiry type' }]}
              >
                <Select placeholder="Select an inquiry type">
                  {inquiryTypes.map((type) => (
                    <Option
                      key={type.value}
                      value={type.value}
                    >
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="message"
                label="Message"
                rules={[{ required: true, message: 'Please enter your message' }]}
              >
                <TextArea
                  placeholder="How can we help you?"
                  rows={5}
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SendOutlined />}
                  loading={loading}
                  size="large"
                  block
                >
                  Send Message
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* Map Section */}
      <div className="map-section mb-12">
        <Title
          level={3}
          className="mb-6 text-center"
        >
          Our Location
        </Title>
        <div
          className="map-container rounded-lg overflow-hidden shadow-md"
          style={{ height: '400px' }}
        >
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d15551.21141032225!2d77.73946325281958!3d12.984457900000017!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae0f8aa61f52b5%3A0xf238a4af44212ea7!2sHope%20Farm%20Bus%20stop!5e0!3m2!1sen!2sin!4v1746950152846!5m2!1sen!2sin"
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen=""
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title="Office Location"
          ></iframe>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="faq-section text-center mb-12">
        <Title
          level={3}
          className="mb-4"
        >
          Still Have Questions?
        </Title>
        <Paragraph className="text-muted mb-6 max-w-2xl mx-auto">
          Check our comprehensive FAQ section for answers to commonly asked questions. If you can't
          find what you're looking for, don't hesitate to reach out.
        </Paragraph>
        <Button
          type="primary"
          size="large"
          onClick={() => navigate('/faqs')}
        >
          Visit FAQ Page
        </Button>
      </div>
    </div>
  );
};

export default Contact;
