import { Layout, <PERSON>u, <PERSON><PERSON>, Avatar, Spin } from 'antd';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { logo_lite, logo_dark } from '@/assets';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Sider } = Layout;

const AppSidebar = ({
  collapsed,
  sidebarItems,
  selectedKey,
  isDark,
  profile,
  handleLogout,
  isLoggingOut,
}) => {
  const { isMobile, isTablet } = useDeviceDetect();

  // Don't render the sidebar on mobile devices - use MobileDrawer instead
  if (isMobile) return null;

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={250}
      className="min-h-screen bg-card shadow-md transition-all"
      style={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 999,
        display: isMobile ? 'none' : 'block',
      }}
      breakpoint="lg"
      collapsedWidth={isTablet ? 0 : 80}
    >
      <div className="logo p-4 flex items-center justify-center">
        {!collapsed && (
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="logo"
            className="w-32"
          />
        )}
        {collapsed && !isTablet && (
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="logo"
            className="w-10"
          />
        )}
      </div>
      <Menu
        mode="inline"
        selectedKeys={[selectedKey]}
        items={sidebarItems}
        className="border-0 bg-transparent"
        theme={isDark ? 'dark' : 'light'}
        style={{ fontSize: isTablet ? '14px' : '16px' }}
      />

      <div className="absolute bottom-0 left-0 right-0 p-4">
        {!collapsed && (
          <div className="user-info flex items-center mb-4 p-2 bg-secondary rounded-lg">
            <Avatar
              size={isTablet ? 'small' : 'middle'}
              icon={<UserOutlined style={{ fontSize: isTablet ? '16px' : '20px' }} />}
              src={profile?.profile_photo_url}
            />
            <div className="ml-2 overflow-hidden">
              <div
                className="font-semibold mb-[-2px] truncate"
                style={{ fontSize: '12px' }}
              >
                {profile?.email.split('@')[0] || 'User'}
              </div>
              <div
                style={{ fontSize: '12px' }}
                className="opacity-70 truncate"
              >
                {profile?.role || '<EMAIL>'}
              </div>
            </div>
          </div>
        )}
        <Button
          type="primary"
          danger
          icon={<LogoutOutlined style={{ fontSize: isTablet ? '14px' : '16px' }} />}
          onClick={handleLogout}
          block
          size={isTablet ? 'middle' : 'large'}
          style={{ fontSize: isTablet ? '14px' : '16px', height: isTablet ? '36px' : '40px' }}
          loading={isLoggingOut}
        >
          {!collapsed && (isLoggingOut ? 'Logging out...' : 'Logout')}
        </Button>
      </div>
    </Sider>
  );
};

export default AppSidebar;
