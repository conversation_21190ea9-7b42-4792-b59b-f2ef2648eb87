import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Typography, Spin, message, Tabs, Space, Timeline, Alert } from 'antd';
import {
  UserOutlined,
  UploadOutlined,
  EditOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  GlobalOutlined,
  TrophyOutlined,
  StarOutlined,
} from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import {
  ProfileHeader,
  ProfileOverview,
  EditProfileModal,
} from '@/features/candidate/components/profile';
import { ProfileStrength, Rating } from '@/shared/components';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const Profile = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [photoFile, setPhotoFile] = useState(null);
  const [resumeFile, setResumeFile] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [editMode, setEditMode] = useState(false);
  const { user, profile, saveProfile } = useAuth();
  const { uploadProfilePhoto, uploadResume } = useCandidateStore();

  useEffect(() => {
    if (profile) {
      setLoading(false);
    }
  }, [profile]);

  const handleSubmit = async (values) => {
    setSaving(true);
    try {
      // Update profile using auth hook
      const result = await saveProfile(user.id, values);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile');
      }

      // Upload profile photo if provided
      if (photoFile) {
        const { success: photoSuccess, error: photoError } = await uploadProfilePhoto(
          user.id,
          photoFile
        );
        if (!photoSuccess) {
          console.error('Photo upload failed:', photoError);
        }
      }

      // Upload resume if provided
      if (resumeFile) {
        const { success: resumeSuccess, error: resumeError } = await uploadResume(
          user.id,
          resumeFile
        );
        if (!resumeSuccess) {
          console.error('Resume upload failed:', resumeError);
        }
      }

      message.success('Profile updated successfully');
      setEditMode(false);
      setPhotoFile(null);
      setResumeFile(null);
    } catch (err) {
      message.error(err.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="candidate-profile max-w-7xl mx-auto px-4 py-6">
      {/* Profile Header */}
      <ProfileHeader
        profile={profile}
        user={user}
        onEditProfile={() => setEditMode(true)}
        onPreviewProfile={() => window.open('/candidate/profile/preview', '_blank')}
        onViewResume={() => window.open(profile.resume_url, '_blank')}
        ratings={[]} // TODO: Add actual ratings data
      />

      {/* Profile Tabs */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="rounded-xl overflow-hidden"
        type="card"
        size="large"
      >
        {/* Overview Tab */}
        <TabPane
          tab={
            <span className="flex items-center">
              <UserOutlined className="mr-2" /> Overview
            </span>
          }
          key="overview"
        >
          <ProfileOverview
            profile={profile}
            onEditProfile={() => setEditMode(true)}
          />
        </TabPane>

        {/* Profile Strength Tab */}
        <TabPane
          tab={
            <span className="flex items-center">
              <TrophyOutlined className="mr-2" /> Profile Strength
            </span>
          }
          key="strength"
        >
          <ProfileStrength
            profile={profile}
            showBreakdown={true}
          />
        </TabPane>

        {/* Ratings Tab */}
        <TabPane
          tab={
            <span className="flex items-center">
              <StarOutlined className="mr-2" /> Company Ratings
            </span>
          }
          key="ratings"
        >
          <Rating
            ratings={[]}
            showSummary={true}
          />
        </TabPane>

        {/* Experience Tab */}
        <TabPane
          tab={
            <span className="flex items-center">
              <GlobalOutlined
                size={16}
                className="mr-2"
              />{' '}
              Work Experience
            </span>
          }
          key="experience"
        >
          <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
            {profile?.experience &&
            Array.isArray(profile.experience) &&
            profile.experience.length > 0 ? (
              <Timeline mode="left">
                {profile.experience.map((exp, index) => (
                  <Timeline.Item
                    key={index}
                    color="blue"
                    label={
                      <span className="text-gray-500">
                        {exp.start_date} - {exp.end_date || 'Present'}
                      </span>
                    }
                  >
                    <div className="mb-1">
                      <Text
                        strong
                        className="text-lg"
                      >
                        {exp.title}
                      </Text>
                    </div>
                    <div className="mb-2">
                      <Text className="text-primary">{exp.company}</Text>
                      {exp.location && (
                        <Text className="text-gray-500 ml-2">
                          <EnvironmentOutlined /> {exp.location}
                        </Text>
                      )}
                    </div>
                    <Paragraph className="text-gray-600 dark:text-gray-300">
                      {exp.description}
                    </Paragraph>
                    {exp.achievements && Array.isArray(exp.achievements) && (
                      <div className="mt-2">
                        <Text strong>Key Achievements:</Text>
                        <ul className="list-disc pl-5 mt-1">
                          {exp.achievements.map((achievement, i) => (
                            <li
                              key={i}
                              className="text-gray-600 dark:text-gray-300"
                            >
                              {achievement}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Alert
                message="No work experience added"
                description="Add your work history to showcase your professional experience to potential employers."
                type="info"
                showIcon
                action={
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => setEditMode(true)}
                  >
                    Add Experience
                  </Button>
                }
              />
            )}
          </Card>
        </TabPane>

        {/* Resume Tab */}
        <TabPane
          tab={
            <span className="flex items-center">
              <FileTextOutlined className="mr-2" /> Resume
            </span>
          }
          key="resume"
        >
          <Card className="shadow-sm hover:shadow-md transition-all rounded-lg overflow-hidden">
            {profile?.resume_url ? (
              <div className="flex flex-col items-center">
                <div className="mb-6 text-center">
                  <FileTextOutlined
                    style={{ fontSize: 64 }}
                    className="text-primary mb-4"
                  />
                  <Title level={4}>Your Resume</Title>
                  <Text className="text-gray-500">View or download your uploaded resume</Text>
                </div>
                <Space>
                  <Button
                    type="primary"
                    size="large"
                    icon={<GlobalOutlined />}
                    href={profile.resume_url}
                    target="_blank"
                    className="bg-primary hover:bg-primary-hover transition-colors"
                  >
                    View Resume
                  </Button>
                  <Button
                    size="large"
                    icon={<EditOutlined />}
                    onClick={() => setEditMode(true)}
                  >
                    Update Resume
                  </Button>
                </Space>
              </div>
            ) : (
              <div className="flex flex-col items-center py-10">
                <Alert
                  message="No resume uploaded"
                  description="Upload your resume to make it easier for employers to review your qualifications."
                  type="warning"
                  showIcon
                  className="mb-6"
                  style={{ maxWidth: 500 }}
                />
                <Button
                  type="primary"
                  size="large"
                  icon={<UploadOutlined />}
                  onClick={() => setEditMode(true)}
                  className="bg-primary hover:bg-primary-hover transition-colors"
                >
                  Upload Resume
                </Button>
              </div>
            )}
          </Card>
        </TabPane>
      </Tabs>

      {/* Edit Profile Modal */}
      <EditProfileModal
        visible={editMode}
        onCancel={() => setEditMode(false)}
        onSave={handleSubmit}
        profile={profile}
        loading={saving}
        onPhotoUpload={(file) => setPhotoFile(file)}
        onResumeUpload={(file) => setResumeFile(file)}
      />
    </div>
  );
};

export default Profile;
